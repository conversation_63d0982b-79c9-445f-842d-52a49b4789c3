"use client";

import React, { useState, useEffect } from "react";
import { useTranslations } from "next-intl";
import { useSearchParams } from "next/navigation";
import { supabase } from "@/lib/supabase";
import VendorCard from "@/components/vendors/VendorCard";
import SearchBar, { SearchFilters } from "@/components/search/SearchBar";
import { motion, AnimatePresence } from "framer-motion";
import {
  FaStore,
  FaMapMarkedAlt,
  FaList,
  FaThLarge,
  FaSearch,
  FaStar,
  FaHeart,
  FaShoppingBag,
  FaLeaf,
  FaFish,
  FaAppleAlt,
  FaBreadSlice,
  FaGlassWhiskey,
  FaHandHeart,
  FaQuoteLeft,
  FaArrowRight,
  FaCheckCircle,
  FaTrophy,
  FaUsers,
  FaMapPin
} from "react-icons/fa";
import { HiSparkles, HiLocationMarker, HiPhone, HiMail } from "react-icons/hi";
import GoogleMap from "@/components/maps/GoogleMap";
import { MapMarker } from "@/components/maps/GoogleMap";
import Link from "next/link";
import Image from "next/image";
// import { useGoogleMaps } from "@/contexts/GoogleMapsContext";

// Types pour les vendeurs
interface Vendor {
  id: string;
  name: string;
  description: string;
  profile_image?: string;
  rating: number;
  location?: {
    latitude: number;
    longitude: number;
  };
  categories: string[];
  product_count: number;
}

// Données de démonstration pour les vendeurs mis en avant
const featuredVendors = [
  {
    id: "1",
    name: "Ferme Bio de Thiès",
    description: "Spécialiste des légumes biologiques et fruits de saison",
    profile_image: "/assets/images/vendors/ferme-bio-thies.jpg",
    rating: 4.9,
    location: { latitude: 14.7969, longitude: -16.9267 },
    categories: ["Légumes", "Fruits", "Bio"],
    product_count: 45,
    badge: "Recommandé",
    reviews: 127,
    deliveryTime: "30-45 min"
  },
  {
    id: "2",
    name: "Boucherie Halal Premium",
    description: "Viandes fraîches et halal, sélectionnées avec soin",
    profile_image: "/assets/images/vendors/boucherie-halal.jpg",
    rating: 4.8,
    location: { latitude: 14.6928, longitude: -17.4467 },
    categories: ["Viandes", "Halal"],
    product_count: 32,
    badge: "Populaire",
    reviews: 89,
    deliveryTime: "20-30 min"
  },
  {
    id: "3",
    name: "Poissonnerie du Port",
    description: "Poissons et fruits de mer frais du jour",
    profile_image: "/assets/images/vendors/poissonnerie-port.jpg",
    rating: 4.7,
    location: { latitude: 14.6692, longitude: -17.4391 },
    categories: ["Poissons", "Fruits de mer"],
    product_count: 28,
    badge: "Nouveau",
    reviews: 56,
    deliveryTime: "25-35 min"
  },
  {
    id: "4",
    name: "Boulangerie Artisanale",
    description: "Pain frais et pâtisseries traditionnelles",
    profile_image: "/assets/images/vendors/boulangerie-artisanale.jpg",
    rating: 4.6,
    location: { latitude: 14.7158, longitude: -17.4731 },
    categories: ["Boulangerie", "Pâtisserie"],
    product_count: 38,
    badge: "Artisanal",
    reviews: 73,
    deliveryTime: "15-25 min"
  }
];

// Catégories avec icônes
const categories = [
  { id: "legumes", name: "Légumes", icon: FaLeaf, color: "from-green-400 to-green-600", count: 156 },
  { id: "fruits", name: "Fruits", icon: FaAppleAlt, color: "from-red-400 to-pink-600", count: 89 },
  { id: "viandes", name: "Viandes", icon: FaHandHeart, color: "from-red-500 to-red-700", count: 67 },
  { id: "poissons", name: "Poissons", icon: FaFish, color: "from-blue-400 to-blue-600", count: 45 },
  { id: "boulangerie", name: "Boulangerie", icon: FaBreadSlice, color: "from-yellow-400 to-orange-500", count: 34 },
  { id: "boissons", name: "Boissons", icon: FaGlassWhiskey, color: "from-purple-400 to-purple-600", count: 23 }
];

// Témoignages
const testimonials = [
  {
    id: 1,
    text: "Grâce à LocaFresh, je vends 3x plus, et mes clients sont ravis ! La plateforme est intuitive et le support excellent.",
    author: "Aminata Diallo",
    role: "Vendeuse de légumes",
    avatar: "/assets/images/testimonials/aminata.jpg",
    rating: 5
  },
  {
    id: 2,
    text: "Une expérience formidable ! Mes produits bio trouvent enfin leur public grâce à cette marketplace locale.",
    author: "Mamadou Seck",
    role: "Producteur bio",
    avatar: "/assets/images/testimonials/mamadou.jpg",
    rating: 5
  },
  {
    id: 3,
    text: "Interface moderne, livraisons rapides, clients satisfaits. LocaFresh a révolutionné mon business !",
    author: "Fatou Ndiaye",
    role: "Boulangère",
    avatar: "/assets/images/testimonials/fatou.jpg",
    rating: 5
  }
];

// Page de liste des vendeurs - Design Premium
export default function VendorsPage() {
  const t = useTranslations();
  const searchParams = useSearchParams();
  // const { isLoaded, currentLocation } = useGoogleMaps();
  const currentLocation = { lat: 14.6928, lng: -17.4467 }; // Dakar par défaut

  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [viewMode, setViewMode] = useState<"list" | "grid" | "map">("grid");
  const [searchQuery, setSearchQuery] = useState("");
  const [filters, setFilters] = useState<SearchFilters>({});
  const [totalResults, setTotalResults] = useState(0);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  // Initialiser la recherche à partir des paramètres d'URL
  useEffect(() => {
    const q = searchParams.get("q") || "";
    const category = searchParams.get("category") || undefined;
    const location = searchParams.get("location") || undefined;
    const radius = searchParams.get("radius") ? parseInt(searchParams.get("radius") as string) : undefined;
    const view = searchParams.get("view") as "list" | "grid" | "map" || "grid";

    setSearchQuery(q);
    setFilters({
      category,
      location,
      radius,
    });
    setViewMode(["list", "grid", "map"].includes(view) ? view as "list" | "grid" | "map" : "grid");

    // Charger les vendeurs
    fetchVendors(q, {
      category,
      location,
      radius,
    });
  }, [searchParams]);

  // Charger les vendeurs
  const fetchVendors = async (query: string, searchFilters: SearchFilters) => {
    setIsLoading(true);

    try {
      // Construire la requête
      let vendorsQuery = supabase
        .from("sellers")
        .select(`
          *,
          categories:seller_categories(category),
          products:products(id)
        `);

      // Appliquer les filtres
      if (query) {
        vendorsQuery = vendorsQuery.or(`name.ilike.%${query}%, description.ilike.%${query}%`);
      }

      if (searchFilters.category) {
        vendorsQuery = vendorsQuery.contains("categories.category", [searchFilters.category]);
      }

      // Trier par note
      vendorsQuery = vendorsQuery.order("rating", { ascending: false });

      const { data, error, count } = await vendorsQuery;

      if (error) throw error;

      // Transformer les données
      const transformedVendors = data.map(vendor => ({
        ...vendor,
        categories: vendor.categories.map((c: any) => c.category),
        product_count: vendor.products.length,
      }));

      setVendors(transformedVendors);
      setTotalResults(count || data.length);
    } catch (error) {
      console.error("Error fetching vendors:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Gérer la recherche
  const handleSearch = (query: string, searchFilters: SearchFilters) => {
    setSearchQuery(query);
    setFilters(searchFilters);
    fetchVendors(query, searchFilters);
  };

  // Créer les marqueurs pour la carte
  const createMapMarkers = (): MapMarker[] => {
    return vendors
      .filter(vendor => vendor.location)
      .map(vendor => ({
        id: vendor.id,
        position: {
          lat: vendor.location!.latitude,
          lng: vendor.location!.longitude,
        },
        title: vendor.name,
        content: (
          <div className="p-2">
            <h3 className="font-medium text-gray-900 dark:text-white">{vendor.name}</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {vendor.categories.slice(0, 3).join(", ")}
              {vendor.categories.length > 3 && "..."}
            </p>
            <div className="mt-1 flex items-center">
              {[...Array(5)].map((_, i) => (
                <svg
                  key={i}
                  className={`h-4 w-4 ${
                    i < Math.round(vendor.rating)
                      ? "text-yellow-400"
                      : "text-gray-300 dark:text-gray-600"
                  }`}
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              ))}
              <span className="ml-1 text-sm text-gray-500 dark:text-gray-400">
                ({vendor.rating.toFixed(1)})
              </span>
            </div>
          </div>
        ),
      }));
  };

  // Afficher un indicateur de chargement premium
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#f8fffe] to-[#e6f7ed]">
        {/* Header Premium */}
        <div className="bg-[#20c261] relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-[#20c261] to-[#1aad55] opacity-90"></div>
          <div className="relative z-10 container mx-auto px-4 py-16">
            <div className="text-center">
              <div className="w-16 h-16 border-4 border-white border-t-transparent rounded-full animate-spin mx-auto mb-6"></div>
              <h1 className="text-4xl font-bold text-white mb-4 font-poppins">
                Chargement des vendeurs...
              </h1>
              <p className="text-xl text-white/90 max-w-2xl mx-auto">
                Nous préparons la meilleure sélection de vendeurs locaux pour vous
              </p>
            </div>
          </div>
        </div>

        {/* Skeleton Content */}
        <div className="container mx-auto px-4 py-16">
          <div className="animate-pulse space-y-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="bg-white rounded-3xl shadow-lg p-6">
                  <div className="h-48 bg-gray-200 rounded-2xl mb-4"></div>
                  <div className="h-6 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
                  <div className="h-10 bg-gray-200 rounded-full"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#f8fffe] to-[#e6f7ed]">
      {/* Header Premium avec Hero */}
      <motion.div
        className="bg-[#20c261] relative overflow-hidden"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
      >
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-r from-[#20c261] to-[#1aad55] opacity-90"></div>
        <div className="absolute top-0 left-0 w-full h-full">
          <div className="absolute top-10 left-10 w-32 h-32 bg-white/10 rounded-full blur-xl"></div>
          <div className="absolute top-32 right-20 w-24 h-24 bg-white/10 rounded-full blur-xl"></div>
          <div className="absolute bottom-20 left-1/3 w-40 h-40 bg-white/10 rounded-full blur-xl"></div>
        </div>

        <div className="relative z-10 container mx-auto px-4 py-20">
          <motion.div
            className="text-center max-w-4xl mx-auto"
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <h1 className="text-5xl md:text-6xl font-bold text-white mb-6 font-poppins leading-tight">
              🛒 Découvrez nos <span className="text-yellow-300">Vendeurs</span>
            </h1>
            <p className="text-xl md:text-2xl text-white/90 mb-8 leading-relaxed">
              Trouvez les meilleurs vendeurs locaux, sélectionnés avec soin pour vous servir, chaque jour.
            </p>

            {/* Barre de recherche premium */}
            <motion.div
              className="max-w-2xl mx-auto"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-6 flex items-center pointer-events-none">
                  <FaSearch className="h-6 w-6 text-[#666666]" />
                </div>
                <input
                  type="text"
                  placeholder="Rechercher un vendeur, une spécialité..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-16 pr-6 py-5 bg-white rounded-full text-lg font-medium text-[#333333] placeholder-[#666666] shadow-xl border-0 focus:outline-none focus:ring-4 focus:ring-white/30 transition-all duration-300"
                />
                <button className="absolute inset-y-0 right-0 pr-2 flex items-center">
                  <div className="bg-[#20c261] text-white p-3 rounded-full hover:bg-[#1aad55] transition-all duration-300 hover:scale-105">
                    <FaSearch className="h-5 w-5" />
                  </div>
                </button>
              </div>
            </motion.div>

            {/* Stats rapides */}
            <motion.div
              className="flex items-center justify-center gap-8 mt-8"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              <div className="bg-white/20 backdrop-blur-sm px-6 py-3 rounded-full border border-white/30">
                <span className="text-white font-semibold">🌱 {featuredVendors.length}+ vendeurs</span>
              </div>
              <div className="bg-white/20 backdrop-blur-sm px-6 py-3 rounded-full border border-white/30">
                <span className="text-white font-semibold">🚚 Livraison rapide</span>
              </div>
              <div className="bg-white/20 backdrop-blur-sm px-6 py-3 rounded-full border border-white/30">
                <span className="text-white font-semibold">⭐ Qualité garantie</span>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </motion.div>

      {/* Section Vendeurs mis en avant */}
      <div className="container mx-auto px-4 py-20">
        <motion.div
          className="text-center mb-16"
          initial={{ y: 30, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-5xl font-bold text-[#333333] mb-6 font-poppins">
            🌟 Vendeurs mis en avant
          </h2>
          <p className="text-xl text-[#666666] max-w-3xl mx-auto leading-relaxed">
            Découvrez notre sélection de vendeurs d'exception, choisis pour leur qualité, leur service et leur engagement envers les produits locaux.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {featuredVendors.map((vendor, index) => (
            <motion.div
              key={vendor.id}
              className="group"
              initial={{ y: 50, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -8 }}
            >
              <div className="bg-white rounded-3xl shadow-xl border border-[#eaedf0] overflow-hidden hover:shadow-2xl transition-all duration-500 relative">
                {/* Badge */}
                <div className="absolute top-4 left-4 z-10">
                  <span className={`px-3 py-1 rounded-full text-sm font-semibold ${
                    vendor.badge === "Recommandé" ? "bg-[#20c261] text-white" :
                    vendor.badge === "Populaire" ? "bg-yellow-400 text-[#333333]" :
                    vendor.badge === "Nouveau" ? "bg-blue-500 text-white" :
                    "bg-purple-500 text-white"
                  }`}>
                    {vendor.badge}
                  </span>
                </div>

                {/* Image */}
                <div className="relative h-56 bg-gradient-to-br from-[#e6f7ed] to-[#20c261]/20 overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <FaStore className="h-16 w-16 text-[#20c261]/30" />
                  </div>
                  {/* Effet hover */}
                  <div className="absolute inset-0 bg-[#20c261]/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>

                {/* Contenu */}
                <div className="p-6">
                  <div className="flex items-start justify-between mb-3">
                    <h3 className="text-xl font-bold text-[#333333] group-hover:text-[#20c261] transition-colors duration-300">
                      {vendor.name}
                    </h3>
                    <div className="flex items-center gap-1">
                      <FaStar className="h-4 w-4 text-yellow-400" />
                      <span className="text-sm font-semibold text-[#333333]">{vendor.rating}</span>
                    </div>
                  </div>

                  <p className="text-[#666666] text-sm mb-4 line-clamp-2">
                    {vendor.description}
                  </p>

                  <div className="flex items-center gap-4 mb-4 text-sm text-[#666666]">
                    <div className="flex items-center gap-1">
                      <FaMapPin className="h-3 w-3" />
                      <span>{vendor.deliveryTime}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <FaShoppingBag className="h-3 w-3" />
                      <span>{vendor.product_count} produits</span>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-2 mb-6">
                    {vendor.categories.slice(0, 2).map((category) => (
                      <span key={category} className="px-3 py-1 bg-[#e6f7ed] text-[#20c261] text-xs font-medium rounded-full">
                        {category}
                      </span>
                    ))}
                  </div>

                  <Link href={`/sellers/${vendor.id}`}>
                    <button className="w-full bg-[#20c261] text-white py-3 px-6 rounded-full font-semibold hover:bg-[#1aad55] transition-all duration-300 hover:scale-105 hover:shadow-lg">
                      Voir les produits
                    </button>
                  </Link>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Section Catégories */}
      <div className="bg-white py-20">
        <div className="container mx-auto px-4">
          <motion.div
            className="text-center mb-16"
            initial={{ y: 30, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-5xl font-bold text-[#333333] mb-6 font-poppins">
              🛍️ Explorez par catégorie
            </h2>
            <p className="text-xl text-[#666666] max-w-3xl mx-auto">
              Trouvez exactement ce que vous cherchez parmi nos catégories soigneusement organisées
            </p>
          </motion.div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {categories.map((category, index) => {
              const IconComponent = category.icon;
              return (
                <motion.div
                  key={category.id}
                  className="group cursor-pointer"
                  initial={{ y: 30, opacity: 0 }}
                  whileInView={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ y: -5 }}
                  onClick={() => setSelectedCategory(selectedCategory === category.id ? null : category.id)}
                >
                  <div className={`bg-white rounded-3xl p-8 text-center shadow-lg border-2 transition-all duration-300 hover:shadow-xl ${
                    selectedCategory === category.id
                      ? 'border-[#20c261] bg-[#e6f7ed]'
                      : 'border-[#eaedf0] hover:border-[#20c261]/30'
                  }`}>
                    <div className={`w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-br ${category.color} flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                      <IconComponent className="h-8 w-8 text-white" />
                    </div>
                    <h3 className="text-lg font-bold text-[#333333] mb-2 group-hover:text-[#20c261] transition-colors duration-300">
                      {category.name}
                    </h3>
                    <p className="text-sm text-[#666666]">
                      {category.count} vendeurs
                    </p>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Section Témoignages */}
      <div className="bg-gradient-to-br from-[#f8fffe] to-[#e6f7ed] py-20">
        <div className="container mx-auto px-4">
          <motion.div
            className="text-center mb-16"
            initial={{ y: 30, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-5xl font-bold text-[#333333] mb-6 font-poppins">
              💬 Ce que disent nos vendeurs
            </h2>
            <p className="text-xl text-[#666666] max-w-3xl mx-auto">
              Découvrez les témoignages de nos partenaires vendeurs qui ont transformé leur business avec LocaFresh
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={testimonial.id}
                className="bg-white rounded-3xl p-8 shadow-xl border border-[#eaedf0] relative overflow-hidden group hover:shadow-2xl transition-all duration-500"
                initial={{ y: 50, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
              >
                {/* Quote Icon */}
                <div className="absolute top-6 right-6 opacity-10 group-hover:opacity-20 transition-opacity duration-300">
                  <FaQuoteLeft className="h-12 w-12 text-[#20c261]" />
                </div>

                {/* Stars */}
                <div className="flex gap-1 mb-6">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <FaStar key={i} className="h-5 w-5 text-yellow-400" />
                  ))}
                </div>

                {/* Testimonial Text */}
                <p className="text-[#333333] text-lg leading-relaxed mb-8 italic">
                  "{testimonial.text}"
                </p>

                {/* Author */}
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-[#20c261] to-[#1aad55] rounded-full flex items-center justify-center">
                    <span className="text-white font-bold text-lg">
                      {testimonial.author.charAt(0)}
                    </span>
                  </div>
                  <div>
                    <h4 className="font-bold text-[#333333]">{testimonial.author}</h4>
                    <p className="text-[#666666] text-sm">{testimonial.role}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* Section CTA Devenir Vendeur */}
      <div className="bg-[#e6f7ed] py-20">
        <div className="container mx-auto px-4">
          <motion.div
            className="max-w-4xl mx-auto text-center"
            initial={{ y: 30, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="bg-white rounded-3xl p-12 shadow-xl border border-[#eaedf0] relative overflow-hidden">
              {/* Background Pattern */}
              <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-[#20c261]/5 to-transparent rounded-full transform translate-x-32 -translate-y-32"></div>
              <div className="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-[#20c261]/5 to-transparent rounded-full transform -translate-x-24 translate-y-24"></div>

              <div className="relative z-10">
                <div className="flex items-center justify-center gap-4 mb-8">
                  <div className="w-16 h-16 bg-[#20c261] rounded-2xl flex items-center justify-center">
                    <FaStore className="h-8 w-8 text-white" />
                  </div>
                  <div className="w-16 h-16 bg-yellow-400 rounded-2xl flex items-center justify-center">
                    <FaTrophy className="h-8 w-8 text-[#333333]" />
                  </div>
                  <div className="w-16 h-16 bg-blue-500 rounded-2xl flex items-center justify-center">
                    <FaUsers className="h-8 w-8 text-white" />
                  </div>
                </div>

                <h2 className="text-4xl md:text-5xl font-bold text-[#333333] mb-6 font-poppins">
                  🚀 Rejoignez notre communauté !
                </h2>
                <p className="text-xl text-[#666666] mb-8 leading-relaxed max-w-2xl mx-auto">
                  Développez votre business, atteignez plus de clients et augmentez vos ventes avec LocaFresh.
                  Rejoignez des centaines de vendeurs qui nous font déjà confiance.
                </p>

                <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
                  <div className="flex items-center gap-2 text-[#20c261] font-semibold">
                    <FaCheckCircle className="h-5 w-5" />
                    <span>Commission attractive</span>
                  </div>
                  <div className="flex items-center gap-2 text-[#20c261] font-semibold">
                    <FaCheckCircle className="h-5 w-5" />
                    <span>Support dédié</span>
                  </div>
                  <div className="flex items-center gap-2 text-[#20c261] font-semibold">
                    <FaCheckCircle className="h-5 w-5" />
                    <span>Outils marketing</span>
                  </div>
                </div>

                <Link href="/register/vendor">
                  <motion.button
                    className="bg-[#20c261] text-white px-12 py-4 rounded-full text-xl font-bold hover:bg-[#1aad55] transition-all duration-300 hover:scale-105 hover:shadow-xl inline-flex items-center gap-3"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Devenir vendeur
                    <FaArrowRight className="h-5 w-5" />
                  </motion.button>
                </Link>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Footer Premium */}
      <footer className="bg-[#1e2530] text-white py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
            {/* Logo et description */}
            <div className="md:col-span-2">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-12 h-12 bg-[#20c261] rounded-xl flex items-center justify-center">
                  <FaLeaf className="h-6 w-6 text-white" />
                </div>
                <h3 className="text-2xl font-bold font-poppins">LocaFresh</h3>
              </div>
              <p className="text-gray-300 text-lg leading-relaxed mb-6 max-w-md">
                La marketplace qui connecte les producteurs locaux aux consommateurs,
                pour des produits frais, authentiques et de qualité.
              </p>
              <div className="flex gap-4">
                <div className="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center hover:bg-[#20c261] transition-colors duration-300 cursor-pointer">
                  <span className="text-sm font-bold">f</span>
                </div>
                <div className="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center hover:bg-[#20c261] transition-colors duration-300 cursor-pointer">
                  <span className="text-sm font-bold">t</span>
                </div>
                <div className="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center hover:bg-[#20c261] transition-colors duration-300 cursor-pointer">
                  <span className="text-sm font-bold">in</span>
                </div>
              </div>
            </div>

            {/* Liens rapides */}
            <div>
              <h4 className="text-lg font-bold mb-6">Liens rapides</h4>
              <ul className="space-y-3">
                <li><Link href="/products" className="text-gray-300 hover:text-[#20c261] transition-colors duration-300">Produits</Link></li>
                <li><Link href="/vendors" className="text-gray-300 hover:text-[#20c261] transition-colors duration-300">Vendeurs</Link></li>
                <li><Link href="/about" className="text-gray-300 hover:text-[#20c261] transition-colors duration-300">À propos</Link></li>
                <li><Link href="/contact" className="text-gray-300 hover:text-[#20c261] transition-colors duration-300">Contact</Link></li>
              </ul>
            </div>

            {/* Contact */}
            <div>
              <h4 className="text-lg font-bold mb-6">Contact</h4>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <HiPhone className="h-5 w-5 text-[#20c261]" />
                  <span className="text-gray-300">+221 77 123 45 67</span>
                </div>
                <div className="flex items-center gap-3">
                  <HiMail className="h-5 w-5 text-[#20c261]" />
                  <span className="text-gray-300"><EMAIL></span>
                </div>
                <div className="flex items-center gap-3">
                  <HiLocationMarker className="h-5 w-5 text-[#20c261]" />
                  <span className="text-gray-300">Dakar, Sénégal</span>
                </div>
              </div>
            </div>
          </div>

          {/* Copyright */}
          <div className="border-t border-gray-700 pt-8 text-center">
            <p className="text-gray-400">
              © 2024 LocaFresh. Tous droits réservés. Fait avec ❤️ au Sénégal.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}