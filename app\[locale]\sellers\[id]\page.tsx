'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { useParams } from 'next/navigation';
import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { supabase } from '@/lib/supabase';
import { FaStar, FaMapMarkerAlt, FaPhone, FaEnvelope, FaStore, FaCalendarAlt, FaShoppingBag, FaHeart, FaRegHeart, FaShare, FaClock, FaImages, FaComments, FaTruck, FaShoppingCart, FaCheckCircle, FaTimesCircle, FaEye, FaChartLine, FaBox, FaCamera, FaEdit, FaPlus, FaGripVertical } from 'react-icons/fa';
import { useOptionalGeolocationContext } from '@/contexts/GeolocationContext';
import { useAuth } from '@/contexts/AuthContext';
import { useCart } from '@/contexts/CartContext';
import StaticMap from '@/components/map/StaticMap';
import toast from 'react-hot-toast';
import { Tab } from '@headlessui/react';

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}

// Fonction utilitaire pour formater la distance
const formatDistanceLocal = (distance: number): string => {
  if (distance < 1) {
    return `${Math.round(distance * 1000)} m`;
  }
  return `${distance.toFixed(1)} km`;
};

// Fonction utilitaire pour calculer la distance
const calculateDistanceLocal = (
  pos1: { lat: number; lng: number },
  pos2: { lat: number; lng: number }
): number => {
  const R = 6371; // Rayon de la Terre en km
  const dLat = (pos2.lat - pos1.lat) * Math.PI / 180;
  const dLng = (pos2.lng - pos1.lng) * Math.PI / 180;
  const a =
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(pos1.lat * Math.PI / 180) * Math.cos(pos2.lat * Math.PI / 180) *
    Math.sin(dLng/2) * Math.sin(dLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
};

interface SellerProfile {
  id: string;
  name: string;
  avatar_url: string;
  email: string;
  phone: string;
  created_at: string;
  seller_profile: {
    description: string;
    address: string;
    location: { lat: number; lng: number };
    rating: number;
    review_count: number;
    opening_hours: string;
    categories: string[];
    gallery: string[];
    specialties: string[];
    delivery_zones: string[];
    delivery_fee: number;
    min_order: number;
    is_available: boolean;
    response_time: string;
  };
  products: Product[];
  reviews: Review[];
  stats: {
    total_orders: number;
    total_revenue: number;
    avg_rating: number;
    completion_rate: number;
  };
}

interface Review {
  id: string;
  user_name: string;
  user_avatar?: string;
  rating: number;
  comment: string;
  date: string;
  order_items?: string[];
}

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  image_url: string;
  category: string;
  stock: number;
  created_at: string;
}

export default function SellerProfilePage() {
  const t = useTranslations();
  const params = useParams();
  const { user } = useAuth();
  const { addToCart } = useCart();
  const geolocationContext = useOptionalGeolocationContext();
  const { calculateDistance, formatDistance, location } = geolocationContext || {
    calculateDistance: null,
    formatDistance: null,
    location: null
  };

  const [seller, setSeller] = useState<SellerProfile | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isFavorite, setIsFavorite] = useState<boolean>(false);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [distance, setDistance] = useState<number | null>(null);

  // Données de démonstration pour les vendeurs
  const demoSellers: { [key: string]: SellerProfile } = {
    '1': {
      id: '1',
      name: 'Ferme Bio de Thiès',
      avatar_url: '/assets/images/vendors/ferme-bio-thies.jpg',
      email: '<EMAIL>',
      phone: '+221 77 123 45 67',
      created_at: '2023-01-15T00:00:00Z',
      seller_profile: {
        description: 'Spécialiste des légumes biologiques et fruits de saison, cultivés avec passion dans les terres fertiles de Thiès. Nous privilégions les méthodes naturelles et respectueuses de l\'environnement.',
        address: 'Route de Dakar, Thiès, Sénégal',
        location: { lat: 14.7969, lng: -16.9267 },
        rating: 4.9,
        review_count: 127,
        opening_hours: 'Lun-Sam: 7h-18h\nDim: 8h-16h',
        categories: ['Légumes', 'Fruits', 'Bio'],
        gallery: [
          '/assets/images/products/vegetables/tomates-cerises-bio.jpg',
          '/assets/images/products/vegetables/salade-verte-croquante.jpg',
          '/assets/images/products/vegetables/carottes-orange-fraiche.jpg',
          '/assets/images/products/vegetables/choux-verts-bio.jpg'
        ],
        specialties: ['Agriculture biologique', 'Légumes de saison', 'Fruits tropicaux', 'Certification bio'],
        delivery_zones: ['Thiès', 'Dakar', 'Rufisque', 'Pikine'],
        delivery_fee: 1500,
        min_order: 5000,
        is_available: true,
        response_time: '< 2h'
      },
      products: [
        {
          id: '1',
          name: 'Tomates Bio 1kg',
          description: 'Tomates biologiques fraîches, cultivées sans pesticides',
          price: 1500,
          image_url: '/assets/images/products/vegetables/tomates-cerises-bio.jpg',
          category: 'Légumes',
          stock: 50,
          created_at: '2024-01-01T00:00:00Z'
        },
        {
          id: '2',
          name: 'Salade Verte Bio',
          description: 'Salade fraîche du jour, croquante et savoureuse',
          price: 800,
          image_url: '/assets/images/products/vegetables/salade-verte-croquante.jpg',
          category: 'Légumes',
          stock: 30,
          created_at: '2024-01-01T00:00:00Z'
        },
        {
          id: '1a',
          name: 'Carottes Bio 1kg',
          description: 'Carottes biologiques croquantes et sucrées',
          price: 1200,
          image_url: '/assets/images/products/vegetables/carottes-orange-fraiche.jpg',
          category: 'Légumes',
          stock: 40,
          created_at: '2024-01-01T00:00:00Z'
        },
        {
          id: '1b',
          name: 'Choux Verts Bio',
          description: 'Choux verts biologiques riches en vitamines',
          price: 900,
          image_url: '/assets/images/products/vegetables/choux-verts-bio.jpg',
          category: 'Légumes',
          stock: 25,
          created_at: '2024-01-01T00:00:00Z'
        }
      ],
      reviews: [
        {
          id: '1',
          user_name: 'Aminata Diallo',
          user_avatar: '/assets/images/avatars/user1.jpg',
          rating: 5,
          comment: 'Excellente qualité ! Les légumes sont vraiment frais et biologiques. Je recommande vivement cette ferme.',
          date: '2024-01-15T00:00:00Z',
          order_items: ['Tomates Bio', 'Salade Verte']
        },
        {
          id: '2',
          user_name: 'Moussa Sow',
          user_avatar: '/assets/images/avatars/user2.jpg',
          rating: 5,
          comment: 'Livraison rapide et produits de qualité. Les tomates sont délicieuses !',
          date: '2024-01-10T00:00:00Z',
          order_items: ['Tomates Bio', 'Carottes Bio']
        },
        {
          id: '3',
          user_name: 'Fatou Ndiaye',
          rating: 4,
          comment: 'Très bon vendeur, produits frais. Juste un petit retard sur la livraison.',
          date: '2024-01-08T00:00:00Z',
          order_items: ['Salade Verte', 'Choux Verts']
        }
      ],
      stats: {
        total_orders: 342,
        total_revenue: 2850000,
        avg_rating: 4.9,
        completion_rate: 98.5
      }
    },
    '2': {
      id: '2',
      name: 'Boucherie Halal Premium',
      avatar_url: '/assets/images/vendors/boucherie-halal.jpg',
      email: '<EMAIL>',
      phone: '+221 77 234 56 78',
      created_at: '2023-03-20T00:00:00Z',
      seller_profile: {
        description: 'Boucherie halal certifiée proposant des viandes fraîches de qualité supérieure. Nos animaux sont élevés localement selon les traditions halal.',
        address: 'Marché Kermel, Dakar, Sénégal',
        location: { lat: 14.6928, lng: -17.4467 },
        rating: 4.8,
        review_count: 89,
        opening_hours: 'Lun-Sam: 8h-19h\nDim: Fermé',
        categories: ['Viandes', 'Halal'],
        gallery: [
          '/assets/images/products/meat/boeuf-steak-halal.jpg',
          '/assets/images/vendors/boucherie-halal.jpg'
        ],
        specialties: ['Viande halal certifiée', 'Bœuf de zébu local', 'Agneau du Fouta', 'Découpe traditionnelle'],
        delivery_zones: ['Dakar', 'Pikine', 'Guédiawaye', 'Parcelles Assainies'],
        delivery_fee: 2000,
        min_order: 10000,
        is_available: true,
        response_time: '< 1h'
      },
      products: [
        {
          id: '3',
          name: 'Bœuf de Zébu Local 1kg',
          description: 'Viande de bœuf zébu local, élevé en pâturage naturel',
          price: 8500,
          image_url: '/assets/images/products/meat/boeuf-steak-halal.jpg',
          category: 'Viandes',
          stock: 20,
          created_at: '2024-01-01T00:00:00Z'
        }
      ],
      reviews: [
        {
          id: '4',
          user_name: 'Ibrahim Diop',
          rating: 5,
          comment: 'Excellente boucherie ! Viande halal de qualité et service impeccable.',
          date: '2024-01-12T00:00:00Z',
          order_items: ['Bœuf de Zébu']
        },
        {
          id: '5',
          user_name: 'Khadija Ba',
          rating: 4,
          comment: 'Très bonne qualité de viande, prix correct. Je recommande.',
          date: '2024-01-05T00:00:00Z',
          order_items: ['Bœuf de Zébu']
        }
      ],
      stats: {
        total_orders: 156,
        total_revenue: 1250000,
        avg_rating: 4.8,
        completion_rate: 96.2
      }
    }
  };

  // Charger les données du vendeur
  useEffect(() => {
    const fetchSellerData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Utiliser les données de démonstration
        const demoSeller = demoSellers[params.id];

        if (!demoSeller) {
          setError('Vendeur non trouvé');
          setIsLoading(false);
          return;
        }

        setSeller(demoSeller);

        // Calculer la distance si la géolocalisation est disponible
        if (location && demoSeller.seller_profile.location) {
          const distanceFunc = calculateDistance || calculateDistanceLocal;
          const dist = distanceFunc(location, demoSeller.seller_profile.location);
          setDistance(dist);
        }

        // Simuler la vérification des favoris
        setIsFavorite(false);

      } catch (err) {
        console.error('Erreur lors du chargement des données du vendeur:', err);
        setError('Erreur lors du chargement des données du vendeur');
      } finally {
        setIsLoading(false);
      }
    };

    fetchSellerData();
  }, [params.id, user, location]);

  // Filtrer les produits par catégorie
  const filteredProducts = selectedCategory
    ? seller?.products.filter(product => product.category === selectedCategory)
    : seller?.products;

  // Ajouter/supprimer des favoris (version démo)
  const toggleFavorite = async () => {
    if (!user) {
      toast.error('Vous devez être connecté pour ajouter des favoris');
      return;
    }

    try {
      // Simuler l'ajout/suppression des favoris
      if (isFavorite) {
        setIsFavorite(false);
        toast.success('Vendeur retiré des favoris');
      } else {
        setIsFavorite(true);
        toast.success('Vendeur ajouté aux favoris');
      }
    } catch (err) {
      console.error('Erreur lors de la modification des favoris:', err);
      toast.error('Une erreur est survenue');
    }
  };

  // Partager le profil
  const shareSeller = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `${seller?.name} sur LocalMarket`,
          text: `Découvrez ${seller?.name} sur LocalMarket`,
          url: window.location.href,
        });
      } catch (err) {
        console.error('Erreur lors du partage:', err);
      }
    } else {
      // Fallback: copier le lien dans le presse-papier
      navigator.clipboard.writeText(window.location.href);
      toast.success('Lien copié dans le presse-papier');
    }
  };

  // Ajouter un produit au panier
  const handleAddToCart = (product: Product) => {
    addToCart({
      id: product.id,
      name: product.name,
      price: product.price,
      quantity: 1,
      image: product.image_url,
      seller_id: seller?.id || '',
      seller_name: seller?.name || '',
    });

    toast.success(`${product.name} ajouté au panier`);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
      </div>
    );
  }

  if (error || !seller) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 dark:bg-red-900/10 border border-red-200 dark:border-red-900/30 rounded-lg p-6 text-center">
          <h2 className="text-xl font-bold text-red-700 dark:text-red-400 mb-2">
            {error || 'Vendeur non trouvé'}
          </h2>
          <p className="text-red-600 dark:text-red-300 mb-4">
            Impossible de charger les informations du vendeur.
          </p>
          <Link
            href="/sellers"
            className="inline-block bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg"
          >
            Retour à la liste des vendeurs
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* En-tête du profil */}
      <motion.div
        className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden mb-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="relative h-48 bg-gradient-to-r from-primary-600 to-primary-800">
          {/* Image de couverture */}
          <div className="absolute inset-0 flex items-center justify-center text-white/30">
            <FaStore className="w-32 h-32" />
          </div>

          {/* Actions */}
          <div className="absolute top-4 right-4 flex space-x-2">
            <button
              onClick={toggleFavorite}
              className="bg-white/20 hover:bg-white/30 p-2 rounded-full backdrop-blur-sm"
              aria-label={isFavorite ? 'Retirer des favoris' : 'Ajouter aux favoris'}
            >
              {isFavorite ? (
                <FaHeart className="text-red-500" />
              ) : (
                <FaRegHeart className="text-white" />
              )}
            </button>
            <button
              onClick={shareSeller}
              className="bg-white/20 hover:bg-white/30 p-2 rounded-full backdrop-blur-sm"
              aria-label="Partager"
            >
              <FaShare className="text-white" />
            </button>
          </div>
        </div>

        <div className="relative px-6 py-6 sm:px-8 sm:py-8">
          {/* Avatar */}
          <div className="absolute -top-16 left-6 sm:left-8">
            <div className="w-24 h-24 sm:w-32 sm:h-32 rounded-full border-4 border-white dark:border-gray-800 overflow-hidden bg-white dark:bg-gray-700 shadow-lg">
              {seller.avatar_url ? (
                <Image
                  src={seller.avatar_url}
                  alt={seller.name}
                  fill
                  className="object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400">
                  <FaStore className="w-12 h-12" />
                </div>
              )}
            </div>
          </div>

          {/* Informations du vendeur */}
          <div className="mt-12 sm:mt-16">
            <div className="flex flex-col sm:flex-row sm:items-end justify-between">
              <div>
                <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white font-heading">
                  {seller.name}
                </h1>

                <div className="flex flex-wrap items-center gap-x-4 gap-y-2 mt-2">
                  {seller.seller_profile.rating > 0 && (
                    <div className="flex items-center">
                      <div className="flex">
                        {[...Array(5)].map((_, i) => (
                          <FaStar
                            key={i}
                            className={`w-4 h-4 ${
                              i < Math.floor(seller.seller_profile.rating)
                                ? 'text-yellow-500'
                                : 'text-gray-300 dark:text-gray-600'
                            }`}
                          />
                        ))}
                      </div>
                      <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">
                        {seller.seller_profile.rating.toFixed(1)} ({seller.seller_profile.review_count} avis)
                      </span>
                    </div>
                  )}

                  {distance !== null && (
                    <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                      <FaMapMarkerAlt className="mr-1" />
                      {(formatDistance || formatDistanceLocal)(distance)}
                    </div>
                  )}

                  <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                    <FaCalendarAlt className="mr-1" />
                    Membre depuis {new Date(seller.created_at).toLocaleDateString()}
                  </div>
                </div>
              </div>

              <div className="mt-4 sm:mt-0">
                <Link
                  href={`/chat/${seller.id}`}
                  className="inline-block bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg"
                >
                  Contacter
                </Link>
              </div>
            </div>

            {/* Description */}
            {seller.seller_profile.description && (
              <div className="mt-4">
                <p className="text-gray-600 dark:text-gray-300">
                  {seller.seller_profile.description}
                </p>
              </div>
            )}

            {/* Catégories */}
            {seller.seller_profile.categories && seller.seller_profile.categories.length > 0 && (
              <div className="mt-4 flex flex-wrap gap-2">
                {seller.seller_profile.categories.map((category) => (
                  <span
                    key={category}
                    className="px-3 py-1 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-400 text-sm rounded-full"
                  >
                    {category}
                  </span>
                ))}
              </div>
            )}
          </div>
        </div>
      </motion.div>

      {/* Statistiques du vendeur */}
      <motion.div
        className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
          <div className="flex items-center">
            <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-lg">
              <FaShoppingCart className="text-green-600 dark:text-green-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm text-gray-500 dark:text-gray-400">Commandes</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {seller.stats.total_orders}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
          <div className="flex items-center">
            <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
              <FaChartLine className="text-blue-600 dark:text-blue-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm text-gray-500 dark:text-gray-400">Chiffre d'affaires</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {(seller.stats.total_revenue / 1000000).toFixed(1)}M
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
          <div className="flex items-center">
            <div className="p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg">
              <FaStar className="text-yellow-600 dark:text-yellow-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm text-gray-500 dark:text-gray-400">Note moyenne</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {seller.stats.avg_rating.toFixed(1)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
          <div className="flex items-center">
            <div className="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
              <FaCheckCircle className="text-purple-600 dark:text-purple-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm text-gray-500 dark:text-gray-400">Taux de réussite</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {seller.stats.completion_rate}%
              </p>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Contenu principal avec onglets */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        <Tab.Group>
          <Tab.List className="flex space-x-1 rounded-xl bg-blue-900/20 p-1 mb-8">
            <Tab
              className={({ selected }) =>
                classNames(
                  'w-full rounded-lg py-2.5 text-sm font-medium leading-5',
                  'ring-white ring-opacity-60 ring-offset-2 ring-offset-blue-400 focus:outline-none focus:ring-2',
                  selected
                    ? 'bg-white text-blue-700 shadow'
                    : 'text-blue-100 hover:bg-white/[0.12] hover:text-white'
                )
              }
            >
              <div className="flex items-center justify-center space-x-2">
                <FaBox />
                <span>Produits</span>
              </div>
            </Tab>
            <Tab
              className={({ selected }) =>
                classNames(
                  'w-full rounded-lg py-2.5 text-sm font-medium leading-5',
                  'ring-white ring-opacity-60 ring-offset-2 ring-offset-blue-400 focus:outline-none focus:ring-2',
                  selected
                    ? 'bg-white text-blue-700 shadow'
                    : 'text-blue-100 hover:bg-white/[0.12] hover:text-white'
                )
              }
            >
              <div className="flex items-center justify-center space-x-2">
                <FaImages />
                <span>Galerie</span>
              </div>
            </Tab>
            <Tab
              className={({ selected }) =>
                classNames(
                  'w-full rounded-lg py-2.5 text-sm font-medium leading-5',
                  'ring-white ring-opacity-60 ring-offset-2 ring-offset-blue-400 focus:outline-none focus:ring-2',
                  selected
                    ? 'bg-white text-blue-700 shadow'
                    : 'text-blue-100 hover:bg-white/[0.12] hover:text-white'
                )
              }
            >
              <div className="flex items-center justify-center space-x-2">
                <FaComments />
                <span>Avis ({seller.reviews.length})</span>
              </div>
            </Tab>
            <Tab
              className={({ selected }) =>
                classNames(
                  'w-full rounded-lg py-2.5 text-sm font-medium leading-5',
                  'ring-white ring-opacity-60 ring-offset-2 ring-offset-blue-400 focus:outline-none focus:ring-2',
                  selected
                    ? 'bg-white text-blue-700 shadow'
                    : 'text-blue-100 hover:bg-white/[0.12] hover:text-white'
                )
              }
            >
              <div className="flex items-center justify-center space-x-2">
                <FaMapMarkerAlt />
                <span>Infos & Livraison</span>
              </div>
            </Tab>
          </Tab.List>

          <Tab.Panels>
            {/* Onglet Produits */}
            <Tab.Panel>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Colonne de gauche: Informations de contact et carte */}
                <motion.div
                  className="lg:col-span-1"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                >
          {/* Informations de contact */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 mb-6">
            <h2 className="text-xl font-bold text-gray-900 dark:text-white font-heading mb-4">
              Informations de contact
            </h2>

            <div className="space-y-4">
              {seller.phone && (
                <div className="flex items-start">
                  <FaPhone className="text-primary-600 dark:text-primary-400 mt-1 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Téléphone</p>
                    <p className="text-gray-900 dark:text-white">{seller.phone}</p>
                  </div>
                </div>
              )}

              {seller.email && (
                <div className="flex items-start">
                  <FaEnvelope className="text-primary-600 dark:text-primary-400 mt-1 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Email</p>
                    <p className="text-gray-900 dark:text-white">{seller.email}</p>
                  </div>
                </div>
              )}

              {seller.seller_profile.address && (
                <div className="flex items-start">
                  <FaMapMarkerAlt className="text-primary-600 dark:text-primary-400 mt-1 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Adresse</p>
                    <p className="text-gray-900 dark:text-white">{seller.seller_profile.address}</p>
                  </div>
                </div>
              )}

              {seller.seller_profile.opening_hours && (
                <div className="flex items-start">
                  <FaCalendarAlt className="text-primary-600 dark:text-primary-400 mt-1 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Horaires d'ouverture</p>
                    <p className="text-gray-900 dark:text-white whitespace-pre-line">
                      {seller.seller_profile.opening_hours}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Carte */}
          {seller.seller_profile.location && seller.seller_profile.location.lat !== 0 && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white font-heading mb-4">
                Emplacement
              </h2>

              <StaticMap
                location={seller.seller_profile.location}
                height="250px"
                markerTitle={seller.name}
                className="rounded-lg overflow-hidden"
              />

              <div className="mt-4">
                <Link
                  href={`https://www.google.com/maps/dir/?api=1&destination=${seller.seller_profile.location.lat},${seller.seller_profile.location.lng}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-block w-full bg-primary-600 hover:bg-primary-700 text-white text-center px-4 py-2 rounded-lg"
                >
                  Itinéraire
                </Link>
              </div>
            </div>
          )}
        </motion.div>

        {/* Colonne de droite: Produits */}
        <motion.div
          className="lg:col-span-2"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white font-heading">
                Produits
              </h2>

              <div className="relative">
                <select
                  className="bg-gray-100 dark:bg-gray-700 border-0 text-gray-700 dark:text-gray-300 rounded-lg py-2 pl-3 pr-8 appearance-none focus:outline-none focus:ring-2 focus:ring-primary-500"
                  value={selectedCategory || ''}
                  onChange={(e) => setSelectedCategory(e.target.value || null)}
                >
                  <option value="">Toutes les catégories</option>
                  {Array.from(new Set(seller.products.map(p => p.category))).map((category) => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                  <svg className="w-4 h-4 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </div>
              </div>
            </div>

            {filteredProducts && filteredProducts.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredProducts.map((product) => (
                  <div
                    key={product.id}
                    className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden shadow-sm hover:shadow-md transition-shadow"
                  >
                    <div className="relative h-40 bg-gray-200 dark:bg-gray-700">
                      {product.image_url ? (
                        <Image
                          src={product.image_url}
                          alt={product.name}
                          fill
                          className="object-cover"
                        />
                      ) : (
                        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-500">
                          <FaShoppingBag className="w-10 h-10" />
                        </div>
                      )}
                      <div className="absolute top-2 right-2 bg-white dark:bg-gray-800 px-2 py-1 rounded-full text-xs font-medium text-gray-700 dark:text-gray-300">
                        {product.category}
                      </div>
                    </div>

                    <div className="p-4">
                      <h3 className="font-medium text-gray-900 dark:text-white mb-1">{product.name}</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-2">
                        {product.description}
                      </p>

                      <div className="flex justify-between items-center">
                        <span className="font-bold text-gray-900 dark:text-white">
                          {product.price.toLocaleString()} FCFA
                        </span>

                        <button
                          onClick={() => handleAddToCart(product)}
                          className="p-2 rounded-full bg-primary-100 text-primary-700 hover:bg-primary-200 dark:bg-primary-900/30 dark:text-primary-400 dark:hover:bg-primary-900/50"
                          aria-label="Ajouter au panier"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <FaShoppingBag className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  Aucun produit disponible
                </h3>
                <p className="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
                  Ce vendeur n'a pas encore ajouté de produits ou aucun produit ne correspond à la catégorie sélectionnée.
                </p>
              </div>
            )}
          </div>
        </motion.div>
      </div>
            </Tab.Panel>

            {/* Onglet Galerie */}
            <Tab.Panel>
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
                <h2 className="text-xl font-bold text-gray-900 dark:text-white font-heading mb-6">
                  Galerie Photos
                </h2>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {seller.seller_profile.gallery.map((image, index) => (
                    <div key={index} className="relative aspect-square rounded-lg overflow-hidden group cursor-pointer">
                      <Image
                        src={image}
                        alt={`Photo ${index + 1}`}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300 flex items-center justify-center">
                        <FaEye className="text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </Tab.Panel>

            {/* Onglet Avis */}
            <Tab.Panel>
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
                <h2 className="text-xl font-bold text-gray-900 dark:text-white font-heading mb-6">
                  Avis Clients ({seller.reviews.length})
                </h2>
                <div className="space-y-6">
                  {seller.reviews.map((review) => (
                    <div key={review.id} className="border-b border-gray-200 dark:border-gray-700 pb-6 last:border-b-0">
                      <div className="flex items-start space-x-4">
                        <div className="flex-shrink-0">
                          {review.user_avatar ? (
                            <Image
                              src={review.user_avatar}
                              alt={review.user_name}
                              width={40}
                              height={40}
                              className="rounded-full"
                            />
                          ) : (
                            <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                {review.user_name.charAt(0)}
                              </span>
                            </div>
                          )}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium text-gray-900 dark:text-white">
                              {review.user_name}
                            </h4>
                            <span className="text-sm text-gray-500 dark:text-gray-400">
                              {new Date(review.date).toLocaleDateString()}
                            </span>
                          </div>
                          <div className="flex items-center mt-1">
                            {[...Array(5)].map((_, i) => (
                              <FaStar
                                key={i}
                                className={`w-4 h-4 ${
                                  i < review.rating
                                    ? 'text-yellow-500'
                                    : 'text-gray-300 dark:text-gray-600'
                                }`}
                              />
                            ))}
                          </div>
                          <p className="mt-2 text-gray-600 dark:text-gray-300">
                            {review.comment}
                          </p>
                          {review.order_items && review.order_items.length > 0 && (
                            <div className="mt-2 flex flex-wrap gap-2">
                              {review.order_items.map((item, index) => (
                                <span
                                  key={index}
                                  className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-xs text-gray-600 dark:text-gray-300 rounded"
                                >
                                  {item}
                                </span>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </Tab.Panel>

            {/* Onglet Infos & Livraison */}
            <Tab.Panel>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Informations de contact */}
                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white font-heading mb-4">
                    Informations de contact
                  </h2>

                  <div className="space-y-4">
                    {seller.phone && (
                      <div className="flex items-start">
                        <FaPhone className="text-primary-600 dark:text-primary-400 mt-1 mr-3" />
                        <div>
                          <p className="text-sm text-gray-500 dark:text-gray-400">Téléphone</p>
                          <p className="text-gray-900 dark:text-white">{seller.phone}</p>
                        </div>
                      </div>
                    )}

                    {seller.email && (
                      <div className="flex items-start">
                        <FaEnvelope className="text-primary-600 dark:text-primary-400 mt-1 mr-3" />
                        <div>
                          <p className="text-sm text-gray-500 dark:text-gray-400">Email</p>
                          <p className="text-gray-900 dark:text-white">{seller.email}</p>
                        </div>
                      </div>
                    )}

                    {seller.seller_profile.address && (
                      <div className="flex items-start">
                        <FaMapMarkerAlt className="text-primary-600 dark:text-primary-400 mt-1 mr-3" />
                        <div>
                          <p className="text-sm text-gray-500 dark:text-gray-400">Adresse</p>
                          <p className="text-gray-900 dark:text-white">{seller.seller_profile.address}</p>
                        </div>
                      </div>
                    )}

                    {seller.seller_profile.opening_hours && (
                      <div className="flex items-start">
                        <FaClock className="text-primary-600 dark:text-primary-400 mt-1 mr-3" />
                        <div>
                          <p className="text-sm text-gray-500 dark:text-gray-400">Horaires d'ouverture</p>
                          <p className="text-gray-900 dark:text-white whitespace-pre-line">
                            {seller.seller_profile.opening_hours}
                          </p>
                        </div>
                      </div>
                    )}

                    <div className="flex items-start">
                      <FaClock className="text-primary-600 dark:text-primary-400 mt-1 mr-3" />
                      <div>
                        <p className="text-sm text-gray-500 dark:text-gray-400">Temps de réponse</p>
                        <p className="text-gray-900 dark:text-white">{seller.seller_profile.response_time}</p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <div className={`w-3 h-3 rounded-full mt-2 mr-3 ${seller.seller_profile.is_available ? 'bg-green-500' : 'bg-red-500'}`}></div>
                      <div>
                        <p className="text-sm text-gray-500 dark:text-gray-400">Statut</p>
                        <p className="text-gray-900 dark:text-white">
                          {seller.seller_profile.is_available ? 'Disponible' : 'Indisponible'}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Spécialités */}
                  <div className="mt-6">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Spécialités
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {seller.seller_profile.specialties.map((specialty, index) => (
                        <span
                          key={index}
                          className="px-3 py-1 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-400 text-sm rounded-full"
                        >
                          {specialty}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Informations de livraison */}
                <div className="space-y-6">
                  <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
                    <h2 className="text-xl font-bold text-gray-900 dark:text-white font-heading mb-4">
                      Livraison
                    </h2>

                    <div className="space-y-4">
                      <div className="flex items-start">
                        <FaTruck className="text-primary-600 dark:text-primary-400 mt-1 mr-3" />
                        <div>
                          <p className="text-sm text-gray-500 dark:text-gray-400">Frais de livraison</p>
                          <p className="text-gray-900 dark:text-white">
                            {seller.seller_profile.delivery_fee.toLocaleString()} FCFA
                          </p>
                        </div>
                      </div>

                      <div className="flex items-start">
                        <FaShoppingCart className="text-primary-600 dark:text-primary-400 mt-1 mr-3" />
                        <div>
                          <p className="text-sm text-gray-500 dark:text-gray-400">Commande minimum</p>
                          <p className="text-gray-900 dark:text-white">
                            {seller.seller_profile.min_order.toLocaleString()} FCFA
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="mt-6">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                        Zones de livraison
                      </h3>
                      <div className="flex flex-wrap gap-2">
                        {seller.seller_profile.delivery_zones.map((zone, index) => (
                          <span
                            key={index}
                            className="px-3 py-1 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-400 text-sm rounded-full"
                          >
                            {zone}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Carte */}
                  {seller.seller_profile.location && seller.seller_profile.location.lat !== 0 && (
                    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
                      <h2 className="text-xl font-bold text-gray-900 dark:text-white font-heading mb-4">
                        Emplacement
                      </h2>

                      <StaticMap
                        location={seller.seller_profile.location}
                        height="300px"
                        markerTitle={seller.name}
                        className="rounded-lg overflow-hidden"
                      />

                      {distance !== null && (
                        <div className="mt-4 flex items-center text-sm text-gray-600 dark:text-gray-400">
                          <FaMapMarkerAlt className="mr-2" />
                          Distance: {(formatDistance || formatDistanceLocal)(distance)}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </Tab.Panel>
          </Tab.Panels>
        </Tab.Group>
      </motion.div>
    </div>
  );
}
