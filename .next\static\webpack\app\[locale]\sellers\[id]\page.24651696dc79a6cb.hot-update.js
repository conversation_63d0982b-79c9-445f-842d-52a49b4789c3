"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/sellers/[id]/page",{

/***/ "(app-pages-browser)/./app/[locale]/sellers/[id]/page.tsx":
/*!********************************************!*\
  !*** ./app/[locale]/sellers/[id]/page.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SellerProfilePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/esm/development/react-client/index.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./lib/supabase.ts\");\n/* harmony import */ var _barrel_optimize_names_FaCalendarAlt_FaEnvelope_FaHeart_FaMapMarkerAlt_FaPhone_FaRegHeart_FaShare_FaShoppingBag_FaStar_FaStore_react_icons_fa__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=FaCalendarAlt,FaEnvelope,FaHeart,FaMapMarkerAlt,FaPhone,FaRegHeart,FaShare,FaShoppingBag,FaStar,FaStore!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _contexts_GeolocationContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/GeolocationContext */ \"(app-pages-browser)/./contexts/GeolocationContext.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_CartContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/CartContext */ \"(app-pages-browser)/./contexts/CartContext.tsx\");\n/* harmony import */ var _components_map_StaticMap__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/map/StaticMap */ \"(app-pages-browser)/./components/map/StaticMap.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction classNames() {\n    for(var _len = arguments.length, classes = new Array(_len), _key = 0; _key < _len; _key++){\n        classes[_key] = arguments[_key];\n    }\n    return classes.filter(Boolean).join(' ');\n}\n// Fonction utilitaire pour formater la distance\nconst formatDistanceLocal = (distance)=>{\n    if (distance < 1) {\n        return \"\".concat(Math.round(distance * 1000), \" m\");\n    }\n    return \"\".concat(distance.toFixed(1), \" km\");\n};\n// Fonction utilitaire pour calculer la distance\nconst calculateDistanceLocal = (pos1, pos2)=>{\n    const R = 6371; // Rayon de la Terre en km\n    const dLat = (pos2.lat - pos1.lat) * Math.PI / 180;\n    const dLng = (pos2.lng - pos1.lng) * Math.PI / 180;\n    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(pos1.lat * Math.PI / 180) * Math.cos(pos2.lat * Math.PI / 180) * Math.sin(dLng / 2) * Math.sin(dLng / 2);\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n    return R * c;\n};\nfunction SellerProfilePage() {\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_11__.useTranslations)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth)();\n    const { addToCart } = (0,_contexts_CartContext__WEBPACK_IMPORTED_MODULE_8__.useCart)();\n    const geolocationContext = (0,_contexts_GeolocationContext__WEBPACK_IMPORTED_MODULE_6__.useOptionalGeolocationContext)();\n    const { calculateDistance, formatDistance, location } = geolocationContext || {\n        calculateDistance: null,\n        formatDistance: null,\n        location: null\n    };\n    const [seller, setSeller] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isFavorite, setIsFavorite] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [distance, setDistance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Données de démonstration pour les vendeurs\n    const demoSellers = {\n        '1': {\n            id: '1',\n            name: 'Ferme Bio de Thiès',\n            avatar_url: '/assets/images/vendors/ferme-bio-thies.jpg',\n            email: '<EMAIL>',\n            phone: '+221 77 123 45 67',\n            created_at: '2023-01-15T00:00:00Z',\n            seller_profile: {\n                description: 'Spécialiste des légumes biologiques et fruits de saison, cultivés avec passion dans les terres fertiles de Thiès. Nous privilégions les méthodes naturelles et respectueuses de l\\'environnement.',\n                address: 'Route de Dakar, Thiès, Sénégal',\n                location: {\n                    lat: 14.7969,\n                    lng: -16.9267\n                },\n                rating: 4.9,\n                review_count: 127,\n                opening_hours: 'Lun-Sam: 7h-18h\\nDim: 8h-16h',\n                categories: [\n                    'Légumes',\n                    'Fruits',\n                    'Bio'\n                ]\n            },\n            products: [\n                {\n                    id: '1',\n                    name: 'Tomates Bio 1kg',\n                    description: 'Tomates biologiques fraîches, cultivées sans pesticides',\n                    price: 1500,\n                    image_url: '/assets/images/products/vegetables/tomates-cerises-bio.jpg',\n                    category: 'Légumes',\n                    stock: 50,\n                    created_at: '2024-01-01T00:00:00Z'\n                },\n                {\n                    id: '2',\n                    name: 'Salade Verte Bio',\n                    description: 'Salade fraîche du jour, croquante et savoureuse',\n                    price: 800,\n                    image_url: '/assets/images/products/vegetables/salade-verte-croquante.jpg',\n                    category: 'Légumes',\n                    stock: 30,\n                    created_at: '2024-01-01T00:00:00Z'\n                }\n            ]\n        },\n        '2': {\n            id: '2',\n            name: 'Boucherie Halal Premium',\n            avatar_url: '/assets/images/vendors/boucherie-halal.jpg',\n            email: '<EMAIL>',\n            phone: '+221 77 234 56 78',\n            created_at: '2023-03-20T00:00:00Z',\n            seller_profile: {\n                description: 'Boucherie halal certifiée proposant des viandes fraîches de qualité supérieure. Nos animaux sont élevés localement selon les traditions halal.',\n                address: 'Marché Kermel, Dakar, Sénégal',\n                location: {\n                    lat: 14.6928,\n                    lng: -17.4467\n                },\n                rating: 4.8,\n                review_count: 89,\n                opening_hours: 'Lun-Sam: 8h-19h\\nDim: Fermé',\n                categories: [\n                    'Viandes',\n                    'Halal'\n                ]\n            },\n            products: [\n                {\n                    id: '3',\n                    name: 'Bœuf de Zébu Local 1kg',\n                    description: 'Viande de bœuf zébu local, élevé en pâturage naturel',\n                    price: 8500,\n                    image_url: '/assets/images/products/meat/boeuf-steak-halal.jpg',\n                    category: 'Viandes',\n                    stock: 20,\n                    created_at: '2024-01-01T00:00:00Z'\n                }\n            ]\n        }\n    };\n    // Charger les données du vendeur\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SellerProfilePage.useEffect\": ()=>{\n            const fetchSellerData = {\n                \"SellerProfilePage.useEffect.fetchSellerData\": async ()=>{\n                    setIsLoading(true);\n                    setError(null);\n                    try {\n                        // Utiliser les données de démonstration\n                        const demoSeller = demoSellers[params.id];\n                        if (!demoSeller) {\n                            setError('Vendeur non trouvé');\n                            setIsLoading(false);\n                            return;\n                        }\n                        setSeller(demoSeller);\n                        // Calculer la distance si la géolocalisation est disponible\n                        if (location && demoSeller.seller_profile.location) {\n                            const distanceFunc = calculateDistance || calculateDistanceLocal;\n                            const dist = distanceFunc(location, demoSeller.seller_profile.location);\n                            setDistance(dist);\n                        }\n                        // Simuler la vérification des favoris\n                        setIsFavorite(false);\n                    } catch (err) {\n                        console.error('Erreur lors du chargement des données du vendeur:', err);\n                        setError('Erreur lors du chargement des données du vendeur');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"SellerProfilePage.useEffect.fetchSellerData\"];\n            fetchSellerData();\n        }\n    }[\"SellerProfilePage.useEffect\"], [\n        params.id,\n        user,\n        location\n    ]);\n    // Filtrer les produits par catégorie\n    const filteredProducts = selectedCategory ? seller === null || seller === void 0 ? void 0 : seller.products.filter((product)=>product.category === selectedCategory) : seller === null || seller === void 0 ? void 0 : seller.products;\n    // Ajouter/supprimer des favoris\n    const toggleFavorite = async ()=>{\n        if (!user) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_10__[\"default\"].error('Vous devez être connecté pour ajouter des favoris');\n            return;\n        }\n        try {\n            if (isFavorite) {\n                // Supprimer des favoris\n                await _lib_supabase__WEBPACK_IMPORTED_MODULE_5__.supabase.from('user_favorites').delete().eq('user_id', user.id).eq('seller_id', params.id);\n                setIsFavorite(false);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_10__[\"default\"].success('Vendeur retiré des favoris');\n            } else {\n                // Ajouter aux favoris\n                await _lib_supabase__WEBPACK_IMPORTED_MODULE_5__.supabase.from('user_favorites').insert({\n                    user_id: user.id,\n                    seller_id: params.id\n                });\n                setIsFavorite(true);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_10__[\"default\"].success('Vendeur ajouté aux favoris');\n            }\n        } catch (err) {\n            console.error('Erreur lors de la modification des favoris:', err);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_10__[\"default\"].error('Une erreur est survenue');\n        }\n    };\n    // Partager le profil\n    const shareSeller = async ()=>{\n        if (navigator.share) {\n            try {\n                await navigator.share({\n                    title: \"\".concat(seller === null || seller === void 0 ? void 0 : seller.name, \" sur LocalMarket\"),\n                    text: \"D\\xe9couvrez \".concat(seller === null || seller === void 0 ? void 0 : seller.name, \" sur LocalMarket\"),\n                    url: window.location.href\n                });\n            } catch (err) {\n                console.error('Erreur lors du partage:', err);\n            }\n        } else {\n            // Fallback: copier le lien dans le presse-papier\n            navigator.clipboard.writeText(window.location.href);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_10__[\"default\"].success('Lien copié dans le presse-papier');\n        }\n    };\n    // Ajouter un produit au panier\n    const handleAddToCart = (product)=>{\n        addToCart({\n            id: product.id,\n            name: product.name,\n            price: product.price,\n            quantity: 1,\n            image: product.image_url,\n            seller_id: (seller === null || seller === void 0 ? void 0 : seller.id) || '',\n            seller_name: (seller === null || seller === void 0 ? void 0 : seller.name) || ''\n        });\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_10__[\"default\"].success(\"\".concat(product.name, \" ajout\\xe9 au panier\"));\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                    lineNumber: 285,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                lineNumber: 284,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n            lineNumber: 283,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !seller) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 dark:bg-red-900/10 border border-red-200 dark:border-red-900/30 rounded-lg p-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-red-700 dark:text-red-400 mb-2\",\n                        children: error || 'Vendeur non trouvé'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600 dark:text-red-300 mb-4\",\n                        children: \"Impossible de charger les informations du vendeur.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                        href: \"/sellers\",\n                        className: \"inline-block bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg\",\n                        children: \"Retour \\xe0 la liste des vendeurs\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                lineNumber: 294,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n            lineNumber: 293,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                className: \"bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden mb-8\",\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.5\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative h-48 bg-gradient-to-r from-primary-600 to-primary-800\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 flex items-center justify-center text-white/30\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarAlt_FaEnvelope_FaHeart_FaMapMarkerAlt_FaPhone_FaRegHeart_FaShare_FaShoppingBag_FaStar_FaStore_react_icons_fa__WEBPACK_IMPORTED_MODULE_13__.FaStore, {\n                                    className: \"w-32 h-32\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-4 right-4 flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: toggleFavorite,\n                                        className: \"bg-white/20 hover:bg-white/30 p-2 rounded-full backdrop-blur-sm\",\n                                        \"aria-label\": isFavorite ? 'Retirer des favoris' : 'Ajouter aux favoris',\n                                        children: isFavorite ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarAlt_FaEnvelope_FaHeart_FaMapMarkerAlt_FaPhone_FaRegHeart_FaShare_FaShoppingBag_FaStar_FaStore_react_icons_fa__WEBPACK_IMPORTED_MODULE_13__.FaHeart, {\n                                            className: \"text-red-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarAlt_FaEnvelope_FaHeart_FaMapMarkerAlt_FaPhone_FaRegHeart_FaShare_FaShoppingBag_FaStar_FaStore_react_icons_fa__WEBPACK_IMPORTED_MODULE_13__.FaRegHeart, {\n                                            className: \"text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: shareSeller,\n                                        className: \"bg-white/20 hover:bg-white/30 p-2 rounded-full backdrop-blur-sm\",\n                                        \"aria-label\": \"Partager\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarAlt_FaEnvelope_FaHeart_FaMapMarkerAlt_FaPhone_FaRegHeart_FaShare_FaShoppingBag_FaStar_FaStore_react_icons_fa__WEBPACK_IMPORTED_MODULE_13__.FaShare, {\n                                            className: \"text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                        lineNumber: 321,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative px-6 py-6 sm:px-8 sm:py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-16 left-6 sm:left-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-24 h-24 sm:w-32 sm:h-32 rounded-full border-4 border-white dark:border-gray-800 overflow-hidden bg-white dark:bg-gray-700 shadow-lg\",\n                                    children: seller.avatar_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        src: seller.avatar_url,\n                                        alt: seller.name,\n                                        fill: true,\n                                        className: \"object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full h-full flex items-center justify-center bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarAlt_FaEnvelope_FaHeart_FaMapMarkerAlt_FaPhone_FaRegHeart_FaShare_FaShoppingBag_FaStar_FaStore_react_icons_fa__WEBPACK_IMPORTED_MODULE_13__.FaStore, {\n                                            className: \"w-12 h-12\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-12 sm:mt-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row sm:items-end justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white font-heading\",\n                                                        children: seller.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap items-center gap-x-4 gap-y-2 mt-2\",\n                                                        children: [\n                                                            seller.seller_profile.rating > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex\",\n                                                                        children: [\n                                                                            ...Array(5)\n                                                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarAlt_FaEnvelope_FaHeart_FaMapMarkerAlt_FaPhone_FaRegHeart_FaShare_FaShoppingBag_FaStar_FaStore_react_icons_fa__WEBPACK_IMPORTED_MODULE_13__.FaStar, {\n                                                                                className: \"w-4 h-4 \".concat(i < Math.floor(seller.seller_profile.rating) ? 'text-yellow-500' : 'text-gray-300 dark:text-gray-600')\n                                                                            }, i, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 382,\n                                                                                columnNumber: 27\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 380,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-sm text-gray-600 dark:text-gray-400\",\n                                                                        children: [\n                                                                            seller.seller_profile.rating.toFixed(1),\n                                                                            \" (\",\n                                                                            seller.seller_profile.review_count,\n                                                                            \" avis)\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 392,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            distance !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center text-sm text-gray-600 dark:text-gray-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarAlt_FaEnvelope_FaHeart_FaMapMarkerAlt_FaPhone_FaRegHeart_FaShare_FaShoppingBag_FaStar_FaStore_react_icons_fa__WEBPACK_IMPORTED_MODULE_13__.FaMapMarkerAlt, {\n                                                                        className: \"mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 400,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    (formatDistance || formatDistanceLocal)(distance)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 399,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center text-sm text-gray-600 dark:text-gray-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarAlt_FaEnvelope_FaHeart_FaMapMarkerAlt_FaPhone_FaRegHeart_FaShare_FaShoppingBag_FaStar_FaStore_react_icons_fa__WEBPACK_IMPORTED_MODULE_13__.FaCalendarAlt, {\n                                                                        className: \"mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 406,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    \"Membre depuis \",\n                                                                    new Date(seller.created_at).toLocaleDateString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 405,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 sm:mt-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                    href: \"/chat/\".concat(seller.id),\n                                                    className: \"inline-block bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg\",\n                                                    children: \"Contacter\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 13\n                                    }, this),\n                                    seller.seller_profile.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-300\",\n                                            children: seller.seller_profile.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 15\n                                    }, this),\n                                    seller.seller_profile.categories && seller.seller_profile.categories.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 flex flex-wrap gap-2\",\n                                        children: seller.seller_profile.categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-3 py-1 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-400 text-sm rounded-full\",\n                                                children: category\n                                            }, category, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                lineNumber: 315,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"lg:col-span-1\",\n                        initial: {\n                            opacity: 0,\n                            x: -20\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        transition: {\n                            duration: 0.5,\n                            delay: 0.2\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold text-gray-900 dark:text-white font-heading mb-4\",\n                                        children: \"Informations de contact\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            seller.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarAlt_FaEnvelope_FaHeart_FaMapMarkerAlt_FaPhone_FaRegHeart_FaShare_FaShoppingBag_FaStar_FaStore_react_icons_fa__WEBPACK_IMPORTED_MODULE_13__.FaPhone, {\n                                                        className: \"text-primary-600 dark:text-primary-400 mt-1 mr-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                                children: \"T\\xe9l\\xe9phone\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-900 dark:text-white\",\n                                                                children: seller.phone\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 469,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 17\n                                            }, this),\n                                            seller.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarAlt_FaEnvelope_FaHeart_FaMapMarkerAlt_FaPhone_FaRegHeart_FaShare_FaShoppingBag_FaStar_FaStore_react_icons_fa__WEBPACK_IMPORTED_MODULE_13__.FaEnvelope, {\n                                                        className: \"text-primary-600 dark:text-primary-400 mt-1 mr-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                                children: \"Email\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 478,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-900 dark:text-white\",\n                                                                children: seller.email\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 479,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 17\n                                            }, this),\n                                            seller.seller_profile.address && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarAlt_FaEnvelope_FaHeart_FaMapMarkerAlt_FaPhone_FaRegHeart_FaShare_FaShoppingBag_FaStar_FaStore_react_icons_fa__WEBPACK_IMPORTED_MODULE_13__.FaMapMarkerAlt, {\n                                                        className: \"text-primary-600 dark:text-primary-400 mt-1 mr-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 486,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                                children: \"Adresse\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 488,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-900 dark:text-white\",\n                                                                children: seller.seller_profile.address\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 489,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 17\n                                            }, this),\n                                            seller.seller_profile.opening_hours && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarAlt_FaEnvelope_FaHeart_FaMapMarkerAlt_FaPhone_FaRegHeart_FaShare_FaShoppingBag_FaStar_FaStore_react_icons_fa__WEBPACK_IMPORTED_MODULE_13__.FaCalendarAlt, {\n                                                        className: \"text-primary-600 dark:text-primary-400 mt-1 mr-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                                children: \"Horaires d'ouverture\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 498,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-900 dark:text-white whitespace-pre-line\",\n                                                                children: seller.seller_profile.opening_hours\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 499,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 463,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 11\n                            }, this),\n                            seller.seller_profile.location && seller.seller_profile.location.lat !== 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold text-gray-900 dark:text-white font-heading mb-4\",\n                                        children: \"Emplacement\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 511,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_map_StaticMap__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        location: seller.seller_profile.location,\n                                        height: \"250px\",\n                                        markerTitle: seller.name,\n                                        className: \"rounded-lg overflow-hidden\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                            href: \"https://www.google.com/maps/dir/?api=1&destination=\".concat(seller.seller_profile.location.lat, \",\").concat(seller.seller_profile.location.lng),\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"inline-block w-full bg-primary-600 hover:bg-primary-700 text-white text-center px-4 py-2 rounded-lg\",\n                                            children: \"Itin\\xe9raire\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 523,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                lineNumber: 510,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                        lineNumber: 451,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        className: \"lg:col-span-2\",\n                        initial: {\n                            opacity: 0,\n                            x: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        transition: {\n                            duration: 0.5,\n                            delay: 0.2\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-bold text-gray-900 dark:text-white font-heading\",\n                                            children: \"Produits\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 545,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    className: \"bg-gray-100 dark:bg-gray-700 border-0 text-gray-700 dark:text-gray-300 rounded-lg py-2 pl-3 pr-8 appearance-none focus:outline-none focus:ring-2 focus:ring-primary-500\",\n                                                    value: selectedCategory || '',\n                                                    onChange: (e)=>setSelectedCategory(e.target.value || null),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"Toutes les cat\\xe9gories\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 555,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        Array.from(new Set(seller.products.map((p)=>p.category))).map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: category,\n                                                                children: category\n                                                            }, category, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 557,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-gray-500 dark:text-gray-400\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: \"2\",\n                                                            d: \"M19 9l-7 7-7-7\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 564,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 563,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 562,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 549,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 544,\n                                    columnNumber: 13\n                                }, this),\n                                filteredProducts && filteredProducts.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                    children: filteredProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden shadow-sm hover:shadow-md transition-shadow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative h-40 bg-gray-200 dark:bg-gray-700\",\n                                                    children: [\n                                                        product.image_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            src: product.image_url,\n                                                            alt: product.name,\n                                                            fill: true,\n                                                            className: \"object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 579,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-500\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarAlt_FaEnvelope_FaHeart_FaMapMarkerAlt_FaPhone_FaRegHeart_FaShare_FaShoppingBag_FaStar_FaStore_react_icons_fa__WEBPACK_IMPORTED_MODULE_13__.FaShoppingBag, {\n                                                                className: \"w-10 h-10\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 587,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 586,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-2 right-2 bg-white dark:bg-gray-800 px-2 py-1 rounded-full text-xs font-medium text-gray-700 dark:text-gray-300\",\n                                                            children: product.category\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 590,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 577,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-gray-900 dark:text-white mb-1\",\n                                                            children: product.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 596,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400 line-clamp-2 mb-2\",\n                                                            children: product.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 597,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-bold text-gray-900 dark:text-white\",\n                                                                    children: [\n                                                                        product.price.toLocaleString(),\n                                                                        \" FCFA\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 602,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleAddToCart(product),\n                                                                    className: \"p-2 rounded-full bg-primary-100 text-primary-700 hover:bg-primary-200 dark:bg-primary-900/30 dark:text-primary-400 dark:hover:bg-primary-900/50\",\n                                                                    \"aria-label\": \"Ajouter au panier\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                        className: \"h-5 w-5\",\n                                                                        fill: \"none\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        stroke: \"currentColor\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 612,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 611,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 606,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 601,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 595,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, product.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 573,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 571,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaCalendarAlt_FaEnvelope_FaHeart_FaMapMarkerAlt_FaPhone_FaRegHeart_FaShare_FaShoppingBag_FaStar_FaStore_react_icons_fa__WEBPACK_IMPORTED_MODULE_13__.FaShoppingBag, {\n                                            className: \"mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 622,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 dark:text-white mb-2\",\n                                            children: \"Aucun produit disponible\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 623,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-400 max-w-md mx-auto\",\n                                            children: \"Ce vendeur n'a pas encore ajout\\xe9 de produits ou aucun produit ne correspond \\xe0 la cat\\xe9gorie s\\xe9lectionn\\xe9e.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 626,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 621,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                            lineNumber: 543,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                        lineNumber: 537,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n                lineNumber: 449,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\sellers\\\\[id]\\\\page.tsx\",\n        lineNumber: 313,\n        columnNumber: 5\n    }, this);\n}\n_s(SellerProfilePage, \"eH9gfMQyPiJHFdG36YE3dxe6BNs=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_11__.useTranslations,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth,\n        _contexts_CartContext__WEBPACK_IMPORTED_MODULE_8__.useCart,\n        _contexts_GeolocationContext__WEBPACK_IMPORTED_MODULE_6__.useOptionalGeolocationContext\n    ];\n});\n_c = SellerProfilePage;\nvar _c;\n$RefreshReg$(_c, \"SellerProfilePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/sellers/[id]/page.tsx\n"));

/***/ })

});