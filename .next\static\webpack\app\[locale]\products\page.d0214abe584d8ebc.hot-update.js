"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/products/page",{

/***/ "(app-pages-browser)/./app/[locale]/products/page.tsx":
/*!****************************************!*\
  !*** ./app/[locale]/products/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/esm/development/react-client/index.js\");\n/* harmony import */ var _contexts_OfflineModeContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/OfflineModeContext */ \"(app-pages-browser)/./contexts/OfflineModeContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ProductsPage() {\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_3__.useTranslations)();\n    const { isOnline, offlineData } = (0,_contexts_OfflineModeContext__WEBPACK_IMPORTED_MODULE_2__.useOfflineMode)();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredProducts, setFilteredProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('grid');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('name');\n    const [priceRange, setPriceRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        0,\n        50000\n    ]);\n    const [minRating, setMinRating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showOnlyPromo, setShowOnlyPromo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showOnlyBio, setShowOnlyBio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [favorites, setFavorites] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [cart, setCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [showScrollTop, setShowScrollTop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTags, setSelectedTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showSuggestions, setShowSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const productsPerPage = 12;\n    // Tags populaires inspirés de TastyDaily\n    const popularTags = [\n        {\n            name: 'Bio',\n            emoji: '🌱',\n            color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'\n        },\n        {\n            name: 'Promo',\n            emoji: '🏷️',\n            color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'\n        },\n        {\n            name: 'Local',\n            emoji: '📍',\n            color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'\n        },\n        {\n            name: 'Frais',\n            emoji: '❄️',\n            color: 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-200'\n        },\n        {\n            name: 'Premium',\n            emoji: '⭐',\n            color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'\n        },\n        {\n            name: 'Traditionnel',\n            emoji: '🏛️',\n            color: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'\n        },\n        {\n            name: 'Artisanal',\n            emoji: '🎨',\n            color: 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200'\n        },\n        {\n            name: 'Nouveau',\n            emoji: '✨',\n            color: 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200'\n        }\n    ];\n    // Catégories LocaFresh avec emojis - Inspiré de TastyDaily\n    const locaFreshCategories = [\n        {\n            id: 'all',\n            name: 'Tout',\n            emoji: '🛒',\n            color: 'from-gray-400 to-gray-500'\n        },\n        {\n            id: 'Fruits',\n            name: 'Fruits',\n            emoji: '🍎',\n            color: 'from-red-400 to-orange-500'\n        },\n        {\n            id: 'Légumes',\n            name: 'Légumes',\n            emoji: '🥬',\n            color: 'from-green-400 to-green-500'\n        },\n        {\n            id: 'Viandes',\n            name: 'Viandes',\n            emoji: '🥩',\n            color: 'from-red-500 to-red-600'\n        },\n        {\n            id: 'Volaille',\n            name: 'Volaille',\n            emoji: '🐔',\n            color: 'from-yellow-400 to-orange-500'\n        },\n        {\n            id: 'Poissons',\n            name: 'Poissons',\n            emoji: '🐟',\n            color: 'from-blue-400 to-blue-500'\n        },\n        {\n            id: 'Boulangerie',\n            name: 'Boulangerie',\n            emoji: '🍞',\n            color: 'from-amber-400 to-amber-500'\n        },\n        {\n            id: 'Boissons',\n            name: 'Boissons',\n            emoji: '🥤',\n            color: 'from-cyan-400 to-blue-500'\n        },\n        {\n            id: 'Artisanat',\n            name: 'Artisanat',\n            emoji: '🎨',\n            color: 'from-purple-400 to-pink-500'\n        }\n    ];\n    // Données de démonstration enrichies pour LocaFresh - 27 produits variés\n    const demoProducts = [\n        {\n            id: '1',\n            name: 'Mangues Bio Kent',\n            price: 2500,\n            category: 'Fruits',\n            seller: 'Ferme Bio Diallo',\n            location: 'Thiès',\n            rating: 4.8,\n            image: 'https://images.unsplash.com/photo-1553279768-865429fa0078?w=400',\n            inStock: true,\n            description: 'Mangues biologiques fraîches, cultivées sans pesticides',\n            unit: 'kg',\n            weight: '1kg',\n            isPromo: true,\n            promoPrice: 2000,\n            badges: [\n                'Bio',\n                'Promo',\n                'Local'\n            ],\n            isNew: false,\n            isBio: true,\n            distance: 2.5,\n            deliveryTime: '20-30 min'\n        },\n        {\n            id: '2',\n            name: 'Tomates Cerises Bio',\n            price: 1800,\n            category: 'Légumes',\n            seller: 'Jardin de Fatou',\n            location: 'Rufisque',\n            rating: 4.6,\n            image: 'https://images.unsplash.com/photo-1546470427-e5b89b618b84?w=400',\n            inStock: true,\n            description: 'Tomates cerises fraîches du jour, cultivées en agriculture biologique',\n            unit: 'barquette',\n            weight: '250g',\n            badges: [\n                'Bio',\n                'Frais',\n                'Local'\n            ],\n            isNew: true,\n            isBio: true,\n            distance: 1.8,\n            deliveryTime: '15-25 min'\n        },\n        {\n            id: '3',\n            name: 'Pain Traditionnel au Feu de Bois',\n            price: 500,\n            category: 'Boulangerie',\n            seller: 'Boulangerie Artisanale',\n            location: 'Dakar',\n            rating: 4.9,\n            image: 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400',\n            inStock: true,\n            description: 'Pain traditionnel sénégalais cuit au feu de bois selon la recette ancestrale',\n            unit: 'pièce',\n            badges: [\n                'Artisanal',\n                'Traditionnel'\n            ],\n            isNew: false,\n            distance: 1.2,\n            deliveryTime: '10-20 min'\n        },\n        {\n            id: '4',\n            name: 'Bissap Artisanal aux Épices',\n            price: 1200,\n            category: 'Boissons',\n            seller: 'Les Délices de Khadija',\n            location: 'Saint-Louis',\n            rating: 4.7,\n            image: 'https://images.unsplash.com/photo-1622597467836-f3285f2131b8?w=400',\n            inStock: true,\n            description: 'Bissap artisanal aux épices naturelles, sans conservateurs',\n            unit: 'bouteille',\n            weight: '500ml',\n            badges: [\n                'Artisanal',\n                'Traditionnel'\n            ],\n            isNew: false,\n            distance: 4.2,\n            deliveryTime: '30-40 min'\n        },\n        {\n            id: '5',\n            name: 'Thiof Frais du Matin',\n            price: 3500,\n            category: 'Poissons',\n            seller: 'Pêcheurs de Soumbédioune',\n            location: 'Dakar',\n            rating: 4.5,\n            image: 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=400',\n            inStock: true,\n            description: 'Thiof fraîchement pêché ce matin, qualité premium',\n            unit: 'kg',\n            badges: [\n                'Frais',\n                'Premium',\n                'Local'\n            ],\n            isNew: false,\n            distance: 3.2,\n            deliveryTime: '25-35 min'\n        },\n        {\n            id: '6',\n            name: 'Sac Artisanal en Raphia',\n            price: 8000,\n            category: 'Artisanat',\n            seller: 'Atelier Sénégal Authentique',\n            location: 'Kaolack',\n            rating: 4.8,\n            image: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400',\n            inStock: true,\n            description: 'Sac artisanal en raphia tressé à la main, design traditionnel',\n            unit: 'pièce',\n            badges: [\n                'Artisanal',\n                'Traditionnel'\n            ],\n            isNew: true,\n            distance: 5.1,\n            deliveryTime: '40-50 min'\n        },\n        {\n            id: '7',\n            name: 'Bananes Bio Plantain',\n            price: 1500,\n            category: 'Fruits',\n            seller: 'Coopérative Fruits Bio',\n            location: 'Ziguinchor',\n            rating: 4.4,\n            image: 'https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=400',\n            inStock: true,\n            description: 'Bananes plantain biologiques, parfaites pour la cuisine',\n            unit: 'régime',\n            weight: '2kg',\n            isPromo: true,\n            promoPrice: 1200,\n            badges: [\n                'Bio',\n                'Promo'\n            ],\n            isNew: false,\n            isBio: true,\n            distance: 6.8,\n            deliveryTime: '45-60 min'\n        },\n        {\n            id: '8',\n            name: 'Poulet Fermier Bio',\n            price: 12000,\n            category: 'Volaille',\n            seller: 'Ferme Avicole Bio',\n            location: 'Mbour',\n            rating: 4.7,\n            image: 'https://images.unsplash.com/photo-1604503468506-a8da13d82791?w=400',\n            inStock: true,\n            description: 'Poulet fermier élevé en liberté, nourri aux grains bio',\n            unit: 'kg',\n            badges: [\n                'Bio',\n                'Premium'\n            ],\n            isNew: false,\n            isBio: true,\n            distance: 3.8,\n            deliveryTime: '30-40 min'\n        },\n        {\n            id: '9',\n            name: 'Ananas Victoria',\n            price: 3000,\n            category: 'Fruits',\n            seller: 'Plantation Tropicale',\n            location: 'Casamance',\n            rating: 4.9,\n            image: 'https://images.unsplash.com/photo-1550258987-190a2d41a8ba?w=400',\n            inStock: true,\n            description: 'Ananas Victoria extra sucré, cultivé en Casamance',\n            unit: 'pièce',\n            weight: '1.5kg',\n            badges: [\n                'Premium',\n                'Local'\n            ],\n            isNew: false,\n            distance: 8.2,\n            deliveryTime: '60-75 min'\n        },\n        {\n            id: '10',\n            name: 'Crevettes Fraîches',\n            price: 8500,\n            category: 'Poissons',\n            seller: 'Pêcheurs de Joal',\n            location: 'Joal-Fadiouth',\n            rating: 4.6,\n            image: 'https://images.unsplash.com/photo-1565680018434-b513d5e5fd47?w=400',\n            inStock: true,\n            description: 'Crevettes fraîches pêchées dans les eaux sénégalaises',\n            unit: 'kg',\n            badges: [\n                'Frais',\n                'Premium'\n            ],\n            isNew: true,\n            distance: 4.5,\n            deliveryTime: '35-45 min'\n        },\n        {\n            id: '11',\n            name: 'Miel Pur Local 500g',\n            price: 4500,\n            category: 'Artisanat',\n            seller: 'Apiculteurs de Casamance',\n            location: 'Casamance',\n            rating: 4.9,\n            image: 'https://images.unsplash.com/photo-1587049352846-4a222e784d38?w=400',\n            inStock: true,\n            description: 'Miel pur et naturel récolté dans les ruches traditionnelles',\n            unit: 'pot',\n            weight: '500g',\n            badges: [\n                'Traditionnel',\n                'Local'\n            ],\n            isNew: false,\n            distance: 8.2,\n            deliveryTime: '60-75 min'\n        },\n        {\n            id: '12',\n            name: 'Jus de Gingembre',\n            price: 800,\n            category: 'Boissons',\n            seller: 'Boissons Naturelles Dakar',\n            location: 'Dakar',\n            rating: 4.3,\n            image: 'https://images.unsplash.com/photo-1570197788417-0e82375c9371?w=400',\n            inStock: true,\n            description: 'Jus de gingembre frais, énergisant et rafraîchissant',\n            unit: 'bouteille',\n            weight: '330ml',\n            isPromo: true,\n            promoPrice: 600,\n            badges: [\n                'Promo',\n                'Frais'\n            ],\n            isNew: false,\n            distance: 2.1,\n            deliveryTime: '15-25 min'\n        },\n        {\n            id: '13',\n            name: 'Croissants Artisanaux',\n            price: 1500,\n            category: 'Boulangerie',\n            seller: 'Pâtisserie Française',\n            location: 'Dakar',\n            rating: 4.7,\n            image: 'https://images.unsplash.com/photo-1555507036-ab794f4afe5a?w=400',\n            inStock: true,\n            description: 'Croissants pur beurre, préparés selon la tradition française',\n            unit: 'lot de 6',\n            badges: [\n                'Artisanal',\n                'Nouveau'\n            ],\n            isNew: true,\n            distance: 1.5,\n            deliveryTime: '10-20 min'\n        },\n        {\n            id: '14',\n            name: 'Oignons Rouges',\n            price: 900,\n            category: 'Légumes',\n            seller: 'Maraîchers de Niayes',\n            location: 'Niayes',\n            rating: 4.2,\n            image: 'https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=400',\n            inStock: true,\n            description: 'Oignons rouges frais des Niayes, parfaits pour vos plats',\n            unit: 'kg',\n            badges: [\n                'Local',\n                'Frais'\n            ],\n            isNew: false,\n            distance: 3.7,\n            deliveryTime: '25-35 min'\n        },\n        {\n            id: '15',\n            name: 'Œufs de Poules Élevées au Sol',\n            price: 2200,\n            category: 'Volaille',\n            seller: 'Ferme Avicole Naturelle',\n            location: 'Thiès',\n            rating: 4.8,\n            image: 'https://images.unsplash.com/photo-1582722872445-44dc5f7e3c8f?w=400',\n            inStock: true,\n            description: 'Œufs frais de poules élevées au sol, riches en oméga-3',\n            unit: 'douzaine',\n            badges: [\n                'Bio',\n                'Premium'\n            ],\n            isNew: false,\n            isBio: true,\n            distance: 2.8,\n            deliveryTime: '20-30 min'\n        }\n    ];\n    // Fonctions utilitaires\n    const formatPrice = (price)=>{\n        return new Intl.NumberFormat('fr-FR', {\n            style: 'currency',\n            currency: 'XOF',\n            minimumFractionDigits: 0\n        }).format(price).replace('XOF', 'FCFA');\n    };\n    const toggleFavorite = (productId)=>{\n        setFavorites((prev)=>{\n            const newFavorites = new Set(prev);\n            if (newFavorites.has(productId)) {\n                newFavorites.delete(productId);\n            } else {\n                newFavorites.add(productId);\n            }\n            return newFavorites;\n        });\n    };\n    const updateCart = (productId, quantity)=>{\n        setCart((prev)=>({\n                ...prev,\n                [productId]: Math.max(0, quantity)\n            }));\n    };\n    const toggleTag = (tagName)=>{\n        setSelectedTags((prev)=>prev.includes(tagName) ? prev.filter((tag)=>tag !== tagName) : [\n                ...prev,\n                tagName\n            ]);\n    };\n    const clearAllFilters = ()=>{\n        setSelectedCategory('all');\n        setSearchQuery('');\n        setSelectedTags([]);\n        setShowOnlyPromo(false);\n        setShowOnlyBio(false);\n        setMinRating(0);\n        setPriceRange([\n            0,\n            50000\n        ]);\n    };\n    const addNotification = (message, type)=>{\n        const id = Date.now().toString();\n        setNotifications((prev)=>[\n                ...prev,\n                {\n                    id,\n                    message,\n                    type\n                }\n            ]);\n        setTimeout(()=>{\n            setNotifications((prev)=>prev.filter((n)=>n.id !== id));\n        }, 3000);\n    };\n    const scrollToTop = ()=>{\n        window.scrollTo({\n            top: 0,\n            behavior: 'smooth'\n        });\n    };\n    // Filtrage et tri\n    const filteredAndSortedProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductsPage.useMemo[filteredAndSortedProducts]\": ()=>{\n            let filtered = products.filter({\n                \"ProductsPage.useMemo[filteredAndSortedProducts].filtered\": (product)=>{\n                    const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) || product.seller.toLowerCase().includes(searchQuery.toLowerCase()) || product.category.toLowerCase().includes(searchQuery.toLowerCase()) || product.description.toLowerCase().includes(searchQuery.toLowerCase());\n                    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;\n                    const matchesPrice = product.price >= priceRange[0] && product.price <= priceRange[1];\n                    const matchesRating = product.rating >= minRating;\n                    const matchesPromo = !showOnlyPromo || product.isPromo;\n                    const matchesBio = !showOnlyBio || product.isBio;\n                    const matchesTags = selectedTags.length === 0 || selectedTags.every({\n                        \"ProductsPage.useMemo[filteredAndSortedProducts].filtered\": (tag)=>{\n                            var _product_badges;\n                            return (_product_badges = product.badges) === null || _product_badges === void 0 ? void 0 : _product_badges.includes(tag);\n                        }\n                    }[\"ProductsPage.useMemo[filteredAndSortedProducts].filtered\"]);\n                    return matchesSearch && matchesCategory && matchesPrice && matchesRating && matchesPromo && matchesBio && matchesTags;\n                }\n            }[\"ProductsPage.useMemo[filteredAndSortedProducts].filtered\"]);\n            filtered.sort({\n                \"ProductsPage.useMemo[filteredAndSortedProducts]\": (a, b)=>{\n                    switch(sortBy){\n                        case 'price':\n                            return (a.promoPrice || a.price) - (b.promoPrice || b.price);\n                        case 'rating':\n                            return (b.rating || 0) - (a.rating || 0);\n                        case 'distance':\n                            return (a.distance || 0) - (b.distance || 0);\n                        default:\n                            return a.name.localeCompare(b.name);\n                    }\n                }\n            }[\"ProductsPage.useMemo[filteredAndSortedProducts]\"]);\n            return filtered;\n        }\n    }[\"ProductsPage.useMemo[filteredAndSortedProducts]\"], [\n        products,\n        searchQuery,\n        selectedCategory,\n        priceRange,\n        minRating,\n        showOnlyPromo,\n        showOnlyBio,\n        sortBy,\n        selectedTags\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductsPage.useEffect\": ()=>{\n            const fetchProducts = {\n                \"ProductsPage.useEffect.fetchProducts\": async ()=>{\n                    setIsLoading(true);\n                    await new Promise({\n                        \"ProductsPage.useEffect.fetchProducts\": (resolve)=>setTimeout(resolve, 1000)\n                    }[\"ProductsPage.useEffect.fetchProducts\"]);\n                    setProducts(demoProducts);\n                    setIsLoading(false);\n                }\n            }[\"ProductsPage.useEffect.fetchProducts\"];\n            fetchProducts();\n        }\n    }[\"ProductsPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductsPage.useEffect\": ()=>{\n            setFilteredProducts(filteredAndSortedProducts);\n        }\n    }[\"ProductsPage.useEffect\"], [\n        filteredAndSortedProducts\n    ]);\n    // Skeleton Loader\n    const SkeletonLoader = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-8 bg-gray-200 rounded animate-pulse mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 471,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-200 rounded animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 472,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                lineNumber: 470,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n            lineNumber: 469,\n            columnNumber: 5\n        }, this);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonLoader, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n            lineNumber: 478,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-4xl font-bold mb-4\",\n                    children: \"\\uD83D\\uDED2 LocaFresh Market\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                    lineNumber: 484,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xl mb-6\",\n                    children: \"D\\xe9couvrez les meilleurs produits locaux\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                    lineNumber: 485,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: [\n                            \"\\uD83C\\uDF31 \",\n                            filteredAndSortedProducts.length,\n                            \" produits frais\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 488,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                    lineNumber: 487,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-2 mb-4\",\n                    children: locaFreshCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setSelectedCategory(category.id),\n                            className: \"px-4 py-2 rounded \".concat(selectedCategory === category.id ? 'bg-blue-500 text-white' : 'bg-gray-200'),\n                            children: [\n                                category.emoji,\n                                \" \",\n                                category.name\n                            ]\n                        }, category.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 493,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                    lineNumber: 491,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                    children: filteredAndSortedProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white p-4 rounded shadow\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-bold\",\n                                    children: product.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: product.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 509,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg font-bold\",\n                                    children: formatPrice(product.price)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 510,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, product.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 507,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                    lineNumber: 505,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n            lineNumber: 483,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n        lineNumber: 482,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductsPage, \"hurTDdy4Sgg7oAUhXXKRbGKFv38=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_3__.useTranslations,\n        _contexts_OfflineModeContext__WEBPACK_IMPORTED_MODULE_2__.useOfflineMode\n    ];\n});\n_c = ProductsPage;\nvar _c;\n$RefreshReg$(_c, \"ProductsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/products/page.tsx\n"));

/***/ })

});