'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, DirectionsRenderer } from '@react-google-maps/api';
import { useGoogleMaps } from '@/contexts/GoogleMapsContext';
import { 
  FaMapMarkerAlt, 
  FaTruck, 
  FaCheckCircle, 
  FaClock, 
  FaPhone, 
  FaComments,
  FaBox,
  FaStore
} from 'react-icons/fa';

interface OrderTrackingProps {
  orderId: string;
  onClose?: () => void;
}

interface OrderStatus {
  id: string;
  status: 'confirmee' | 'preparee' | 'en_route' | 'livree';
  timestamp: string;
  message: string;
  location?: { lat: number; lng: number };
}

interface DeliveryPerson {
  name: string;
  phone: string;
  vehicle: string;
  rating: number;
  photo?: string;
}

export default function OrderTracking({ orderId, onClose }: OrderTrackingProps) {
  const { isLoaded } = useGoogleMaps();
  const [currentStatus, setCurrentStatus] = useState<OrderStatus['status']>('en_route');
  const [deliveryLocation, setDeliveryLocation] = useState({ lat: 14.7167, lng: -17.4677 });
  const [customerLocation] = useState({ lat: 14.6928, lng: -17.4467 });
  const [directions, setDirections] = useState<google.maps.DirectionsResult | null>(null);
  const [estimatedTime, setEstimatedTime] = useState('15-20 min');

  // Données de démonstration
  const orderStatuses: OrderStatus[] = [
    {
      id: '1',
      status: 'confirmee',
      timestamp: '2024-01-15T10:30:00Z',
      message: 'Commande confirmée par le vendeur'
    },
    {
      id: '2',
      status: 'preparee',
      timestamp: '2024-01-15T10:45:00Z',
      message: 'Commande préparée et prête pour livraison'
    },
    {
      id: '3',
      status: 'en_route',
      timestamp: '2024-01-15T11:00:00Z',
      message: 'Livreur en route vers votre adresse',
      location: deliveryLocation
    }
  ];

  const deliveryPerson: DeliveryPerson = {
    name: 'Mamadou Diallo',
    phone: '+221 77 555 66 77',
    vehicle: 'Moto Yamaha',
    rating: 4.8,
    photo: '/assets/images/delivery/driver1.jpg'
  };

  const orderItems = [
    { name: 'Tomates Bio 1kg', quantity: 2, price: 1500 },
    { name: 'Salade Verte Bio', quantity: 1, price: 800 }
  ];

  // Simuler le mouvement du livreur
  useEffect(() => {
    const interval = setInterval(() => {
      setDeliveryLocation(prev => ({
        lat: prev.lat + (Math.random() - 0.5) * 0.001,
        lng: prev.lng + (Math.random() - 0.5) * 0.001
      }));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  // Calculer l'itinéraire
  useEffect(() => {
    if (isLoaded && window.google) {
      const directionsService = new google.maps.DirectionsService();
      
      directionsService.route(
        {
          origin: deliveryLocation,
          destination: customerLocation,
          travelMode: google.maps.TravelMode.DRIVING,
        },
        (result, status) => {
          if (status === 'OK' && result) {
            setDirections(result);
          }
        }
      );
    }
  }, [isLoaded, deliveryLocation, customerLocation]);

  const getStatusIcon = (status: OrderStatus['status']) => {
    switch (status) {
      case 'confirmee': return <FaCheckCircle className="text-green-500" />;
      case 'preparee': return <FaBox className="text-blue-500" />;
      case 'en_route': return <FaTruck className="text-orange-500" />;
      case 'livree': return <FaCheckCircle className="text-green-500" />;
      default: return <FaClock className="text-gray-400" />;
    }
  };

  const getStatusColor = (status: OrderStatus['status'], isActive: boolean) => {
    if (!isActive) return 'text-gray-400';
    
    switch (status) {
      case 'confirmee': return 'text-green-600';
      case 'preparee': return 'text-blue-600';
      case 'en_route': return 'text-orange-600';
      case 'livree': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  if (!isLoaded) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
      {/* En-tête */}
      <div className="bg-gradient-to-r from-primary-600 to-primary-800 text-white p-6">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-bold">Suivi de commande</h2>
            <p className="text-primary-100">Commande #{orderId}</p>
          </div>
          {onClose && (
            <button
              onClick={onClose}
              className="text-white hover:text-primary-200 transition-colors"
            >
              ✕
            </button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 p-6">
        {/* Carte en temps réel */}
        <div className="space-y-4">
          <div className="bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden" style={{ height: '300px' }}>
            <GoogleMap
              mapContainerStyle={{ width: '100%', height: '100%' }}
              center={deliveryLocation}
              zoom={14}
              options={{
                zoomControl: true,
                streetViewControl: false,
                mapTypeControl: false,
                fullscreenControl: false,
              }}
            >
              {/* Marqueur du livreur */}
              <Marker
                position={deliveryLocation}
                icon={{
                  url: '/assets/images/icons/delivery-truck.png',
                  scaledSize: new google.maps.Size(40, 40),
                }}
                title="Livreur"
              />
              
              {/* Marqueur du client */}
              <Marker
                position={customerLocation}
                icon={{
                  url: '/assets/images/icons/home-marker.png',
                  scaledSize: new google.maps.Size(35, 35),
                }}
                title="Votre adresse"
              />

              {/* Itinéraire */}
              {directions && (
                <DirectionsRenderer
                  directions={directions}
                  options={{
                    suppressMarkers: true,
                    polylineOptions: {
                      strokeColor: '#10B981',
                      strokeWeight: 4,
                    },
                  }}
                />
              )}
            </GoogleMap>
          </div>

          {/* Informations du livreur */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h3 className="font-semibold text-gray-900 dark:text-white mb-3">
              Votre livreur
            </h3>
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                <FaTruck className="text-gray-600 dark:text-gray-300" />
              </div>
              <div className="flex-1">
                <p className="font-medium text-gray-900 dark:text-white">
                  {deliveryPerson.name}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {deliveryPerson.vehicle} • ⭐ {deliveryPerson.rating}
                </p>
              </div>
              <div className="flex space-x-2">
                <button className="p-2 bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/50">
                  <FaPhone className="w-4 h-4" />
                </button>
                <button className="p-2 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-900/50">
                  <FaComments className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>

          {/* Temps estimé */}
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-900/30 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <FaClock className="text-green-600 dark:text-green-400" />
              <span className="font-medium text-green-800 dark:text-green-300">
                Arrivée estimée: {estimatedTime}
              </span>
            </div>
          </div>
        </div>

        {/* Statut et détails */}
        <div className="space-y-6">
          {/* Progression de la commande */}
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-white mb-4">
              Statut de la commande
            </h3>
            <div className="space-y-4">
              {orderStatuses.map((status, index) => {
                const isActive = orderStatuses.findIndex(s => s.status === currentStatus) >= index;
                const isCurrent = status.status === currentStatus;
                
                return (
                  <motion.div
                    key={status.id}
                    className="flex items-start space-x-3"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                      isActive ? 'bg-green-100 dark:bg-green-900/30' : 'bg-gray-100 dark:bg-gray-700'
                    }`}>
                      {getStatusIcon(status.status)}
                    </div>
                    <div className="flex-1">
                      <p className={`font-medium ${getStatusColor(status.status, isActive)}`}>
                        {status.message}
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {new Date(status.timestamp).toLocaleTimeString()}
                      </p>
                      {isCurrent && (
                        <motion.div
                          className="mt-1 w-2 h-2 bg-green-500 rounded-full"
                          animate={{ scale: [1, 1.2, 1] }}
                          transition={{ repeat: Infinity, duration: 1 }}
                        />
                      )}
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>

          {/* Détails de la commande */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h3 className="font-semibold text-gray-900 dark:text-white mb-3">
              Détails de la commande
            </h3>
            <div className="space-y-2">
              {orderItems.map((item, index) => (
                <div key={index} className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-300">
                    {item.quantity}x {item.name}
                  </span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {(item.quantity * item.price).toLocaleString()} FCFA
                  </span>
                </div>
              ))}
              <div className="border-t border-gray-200 dark:border-gray-600 pt-2 mt-2">
                <div className="flex justify-between font-semibold">
                  <span className="text-gray-900 dark:text-white">Total</span>
                  <span className="text-gray-900 dark:text-white">
                    {orderItems.reduce((sum, item) => sum + (item.quantity * item.price), 0).toLocaleString()} FCFA
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
