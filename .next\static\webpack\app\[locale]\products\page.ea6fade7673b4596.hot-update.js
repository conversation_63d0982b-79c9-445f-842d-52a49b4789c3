"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/products/page",{

/***/ "(app-pages-browser)/./app/[locale]/products/page.tsx":
/*!****************************************!*\
  !*** ./app/[locale]/products/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/esm/development/react-client/index.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FaArrowRight,FaFilter,FaFire,FaHeart,FaList,FaMinus,FaPlus,FaSearch,FaShoppingCart,FaStar,FaTh,FaTimes!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _contexts_OfflineModeContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/OfflineModeContext */ \"(app-pages-browser)/./contexts/OfflineModeContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ProductsPage() {\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_5__.useTranslations)();\n    const { isOnline, offlineData } = (0,_contexts_OfflineModeContext__WEBPACK_IMPORTED_MODULE_4__.useOfflineMode)();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredProducts, setFilteredProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('grid');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('name');\n    const [priceRange, setPriceRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        0,\n        50000\n    ]);\n    const [minRating, setMinRating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showOnlyPromo, setShowOnlyPromo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showOnlyBio, setShowOnlyBio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [favorites, setFavorites] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [cart, setCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [showScrollTop, setShowScrollTop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTags, setSelectedTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showSuggestions, setShowSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const productsPerPage = 12;\n    // Tags populaires inspirés de TastyDaily\n    const popularTags = [\n        {\n            name: 'Bio',\n            emoji: '🌱',\n            color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'\n        },\n        {\n            name: 'Promo',\n            emoji: '🏷️',\n            color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'\n        },\n        {\n            name: 'Local',\n            emoji: '📍',\n            color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'\n        },\n        {\n            name: 'Frais',\n            emoji: '❄️',\n            color: 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-200'\n        },\n        {\n            name: 'Premium',\n            emoji: '⭐',\n            color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'\n        },\n        {\n            name: 'Traditionnel',\n            emoji: '🏛️',\n            color: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'\n        },\n        {\n            name: 'Artisanal',\n            emoji: '🎨',\n            color: 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200'\n        },\n        {\n            name: 'Nouveau',\n            emoji: '✨',\n            color: 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200'\n        }\n    ];\n    // Catégories LocaFresh avec emojis - Inspiré de TastyDaily\n    const locaFreshCategories = [\n        {\n            id: 'all',\n            name: 'Tout',\n            emoji: '🛒',\n            color: 'from-gray-400 to-gray-500'\n        },\n        {\n            id: 'Fruits',\n            name: 'Fruits',\n            emoji: '🍎',\n            color: 'from-red-400 to-orange-500'\n        },\n        {\n            id: 'Légumes',\n            name: 'Légumes',\n            emoji: '🥬',\n            color: 'from-green-400 to-green-500'\n        },\n        {\n            id: 'Viandes',\n            name: 'Viandes',\n            emoji: '🥩',\n            color: 'from-red-500 to-red-600'\n        },\n        {\n            id: 'Volaille',\n            name: 'Volaille',\n            emoji: '🐔',\n            color: 'from-yellow-400 to-orange-500'\n        },\n        {\n            id: 'Poissons',\n            name: 'Poissons',\n            emoji: '🐟',\n            color: 'from-blue-400 to-blue-500'\n        },\n        {\n            id: 'Boulangerie',\n            name: 'Boulangerie',\n            emoji: '🍞',\n            color: 'from-amber-400 to-amber-500'\n        },\n        {\n            id: 'Boissons',\n            name: 'Boissons',\n            emoji: '🥤',\n            color: 'from-cyan-400 to-blue-500'\n        },\n        {\n            id: 'Artisanat',\n            name: 'Artisanat',\n            emoji: '🎨',\n            color: 'from-purple-400 to-pink-500'\n        }\n    ];\n    // Données de démonstration enrichies pour LocaFresh - 27 produits variés\n    const demoProducts = [\n        {\n            id: '1',\n            name: 'Mangues Bio Kent',\n            price: 2500,\n            category: 'Fruits',\n            seller: 'Ferme Bio Diallo',\n            location: 'Thiès',\n            rating: 4.8,\n            image: 'https://images.unsplash.com/photo-1553279768-865429fa0078?w=400',\n            inStock: true,\n            description: 'Mangues biologiques fraîches, cultivées sans pesticides',\n            unit: 'kg',\n            weight: '1kg',\n            isPromo: true,\n            promoPrice: 2000,\n            badges: [\n                'Bio',\n                'Promo',\n                'Local'\n            ],\n            isNew: false,\n            isBio: true,\n            distance: 2.5,\n            deliveryTime: '20-30 min'\n        },\n        {\n            id: '2',\n            name: 'Tomates Cerises Bio',\n            price: 1800,\n            category: 'Légumes',\n            seller: 'Jardin de Fatou',\n            location: 'Rufisque',\n            rating: 4.6,\n            image: 'https://images.unsplash.com/photo-1546470427-e5b89b618b84?w=400',\n            inStock: true,\n            description: 'Tomates cerises fraîches du jour, cultivées en agriculture biologique',\n            unit: 'barquette',\n            weight: '250g',\n            badges: [\n                'Bio',\n                'Frais',\n                'Local'\n            ],\n            isNew: true,\n            isBio: true,\n            distance: 1.8,\n            deliveryTime: '15-25 min'\n        },\n        {\n            id: '3',\n            name: 'Pain Traditionnel au Feu de Bois',\n            price: 500,\n            category: 'Boulangerie',\n            seller: 'Boulangerie Artisanale',\n            location: 'Dakar',\n            rating: 4.9,\n            image: 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400',\n            inStock: true,\n            description: 'Pain traditionnel sénégalais cuit au feu de bois selon la recette ancestrale',\n            unit: 'pièce',\n            badges: [\n                'Artisanal',\n                'Traditionnel'\n            ],\n            isNew: false,\n            distance: 1.2,\n            deliveryTime: '10-20 min'\n        },\n        {\n            id: '4',\n            name: 'Bissap Artisanal aux Épices',\n            price: 1200,\n            category: 'Boissons',\n            seller: 'Les Délices de Khadija',\n            location: 'Saint-Louis',\n            rating: 4.7,\n            image: 'https://images.unsplash.com/photo-1622597467836-f3285f2131b8?w=400',\n            inStock: true,\n            description: 'Bissap artisanal aux épices naturelles, sans conservateurs',\n            unit: 'bouteille',\n            weight: '500ml',\n            badges: [\n                'Artisanal',\n                'Traditionnel'\n            ],\n            isNew: false,\n            distance: 4.2,\n            deliveryTime: '30-40 min'\n        },\n        {\n            id: '5',\n            name: 'Thiof Frais du Matin',\n            price: 3500,\n            category: 'Poissons',\n            seller: 'Pêcheurs de Soumbédioune',\n            location: 'Dakar',\n            rating: 4.5,\n            image: 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=400',\n            inStock: true,\n            description: 'Thiof fraîchement pêché ce matin, qualité premium',\n            unit: 'kg',\n            badges: [\n                'Frais',\n                'Premium',\n                'Local'\n            ],\n            isNew: false,\n            distance: 3.2,\n            deliveryTime: '25-35 min'\n        },\n        {\n            id: '6',\n            name: 'Sac Artisanal en Raphia',\n            price: 8000,\n            category: 'Artisanat',\n            seller: 'Atelier Sénégal Authentique',\n            location: 'Kaolack',\n            rating: 4.8,\n            image: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400',\n            inStock: true,\n            description: 'Sac artisanal en raphia tressé à la main, design traditionnel',\n            unit: 'pièce',\n            badges: [\n                'Artisanal',\n                'Traditionnel'\n            ],\n            isNew: true,\n            distance: 5.1,\n            deliveryTime: '40-50 min'\n        },\n        {\n            id: '7',\n            name: 'Bananes Bio Plantain',\n            price: 1500,\n            category: 'Fruits',\n            seller: 'Coopérative Fruits Bio',\n            location: 'Ziguinchor',\n            rating: 4.4,\n            image: 'https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=400',\n            inStock: true,\n            description: 'Bananes plantain biologiques, parfaites pour la cuisine',\n            unit: 'régime',\n            weight: '2kg',\n            isPromo: true,\n            promoPrice: 1200,\n            badges: [\n                'Bio',\n                'Promo'\n            ],\n            isNew: false,\n            isBio: true,\n            distance: 6.8,\n            deliveryTime: '45-60 min'\n        },\n        {\n            id: '8',\n            name: 'Poulet Fermier Bio',\n            price: 12000,\n            category: 'Volaille',\n            seller: 'Ferme Avicole Bio',\n            location: 'Mbour',\n            rating: 4.7,\n            image: 'https://images.unsplash.com/photo-1604503468506-a8da13d82791?w=400',\n            inStock: true,\n            description: 'Poulet fermier élevé en liberté, nourri aux grains bio',\n            unit: 'kg',\n            badges: [\n                'Bio',\n                'Premium'\n            ],\n            isNew: false,\n            isBio: true,\n            distance: 3.8,\n            deliveryTime: '30-40 min'\n        },\n        {\n            id: '9',\n            name: 'Ananas Victoria',\n            price: 3000,\n            category: 'Fruits',\n            seller: 'Plantation Tropicale',\n            location: 'Casamance',\n            rating: 4.9,\n            image: 'https://images.unsplash.com/photo-1550258987-190a2d41a8ba?w=400',\n            inStock: true,\n            description: 'Ananas Victoria extra sucré, cultivé en Casamance',\n            unit: 'pièce',\n            weight: '1.5kg',\n            badges: [\n                'Premium',\n                'Local'\n            ],\n            isNew: false,\n            distance: 8.2,\n            deliveryTime: '60-75 min'\n        },\n        {\n            id: '10',\n            name: 'Crevettes Fraîches',\n            price: 8500,\n            category: 'Poissons',\n            seller: 'Pêcheurs de Joal',\n            location: 'Joal-Fadiouth',\n            rating: 4.6,\n            image: 'https://images.unsplash.com/photo-1565680018434-b513d5e5fd47?w=400',\n            inStock: true,\n            description: 'Crevettes fraîches pêchées dans les eaux sénégalaises',\n            unit: 'kg',\n            badges: [\n                'Frais',\n                'Premium'\n            ],\n            isNew: true,\n            distance: 4.5,\n            deliveryTime: '35-45 min'\n        },\n        {\n            id: '11',\n            name: 'Miel Pur Local 500g',\n            price: 4500,\n            category: 'Artisanat',\n            seller: 'Apiculteurs de Casamance',\n            location: 'Casamance',\n            rating: 4.9,\n            image: 'https://images.unsplash.com/photo-1587049352846-4a222e784d38?w=400',\n            inStock: true,\n            description: 'Miel pur et naturel récolté dans les ruches traditionnelles',\n            unit: 'pot',\n            weight: '500g',\n            badges: [\n                'Traditionnel',\n                'Local'\n            ],\n            isNew: false,\n            distance: 8.2,\n            deliveryTime: '60-75 min'\n        },\n        {\n            id: '12',\n            name: 'Jus de Gingembre',\n            price: 800,\n            category: 'Boissons',\n            seller: 'Boissons Naturelles Dakar',\n            location: 'Dakar',\n            rating: 4.3,\n            image: 'https://images.unsplash.com/photo-1570197788417-0e82375c9371?w=400',\n            inStock: true,\n            description: 'Jus de gingembre frais, énergisant et rafraîchissant',\n            unit: 'bouteille',\n            weight: '330ml',\n            isPromo: true,\n            promoPrice: 600,\n            badges: [\n                'Promo',\n                'Frais'\n            ],\n            isNew: false,\n            distance: 2.1,\n            deliveryTime: '15-25 min'\n        },\n        {\n            id: '13',\n            name: 'Croissants Artisanaux',\n            price: 1500,\n            category: 'Boulangerie',\n            seller: 'Pâtisserie Française',\n            location: 'Dakar',\n            rating: 4.7,\n            image: 'https://images.unsplash.com/photo-1555507036-ab794f4afe5a?w=400',\n            inStock: true,\n            description: 'Croissants pur beurre, préparés selon la tradition française',\n            unit: 'lot de 6',\n            badges: [\n                'Artisanal',\n                'Nouveau'\n            ],\n            isNew: true,\n            distance: 1.5,\n            deliveryTime: '10-20 min'\n        },\n        {\n            id: '14',\n            name: 'Oignons Rouges',\n            price: 900,\n            category: 'Légumes',\n            seller: 'Maraîchers de Niayes',\n            location: 'Niayes',\n            rating: 4.2,\n            image: 'https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=400',\n            inStock: true,\n            description: 'Oignons rouges frais des Niayes, parfaits pour vos plats',\n            unit: 'kg',\n            badges: [\n                'Local',\n                'Frais'\n            ],\n            isNew: false,\n            distance: 3.7,\n            deliveryTime: '25-35 min'\n        },\n        {\n            id: '15',\n            name: 'Œufs de Poules Élevées au Sol',\n            price: 2200,\n            category: 'Volaille',\n            seller: 'Ferme Avicole Naturelle',\n            location: 'Thiès',\n            rating: 4.8,\n            image: 'https://images.unsplash.com/photo-1582722872445-44dc5f7e3c8f?w=400',\n            inStock: true,\n            description: 'Œufs frais de poules élevées au sol, riches en oméga-3',\n            unit: 'douzaine',\n            badges: [\n                'Bio',\n                'Premium'\n            ],\n            isNew: false,\n            isBio: true,\n            distance: 2.8,\n            deliveryTime: '20-30 min'\n        },\n        {\n            id: '30',\n            name: 'Jus de Bissap Artisanal 1L',\n            price: 1500,\n            category: 'Artisanat',\n            seller: 'Boissons Naturelles Dakar',\n            location: 'Dakar',\n            rating: 4.7,\n            image: 'https://images.unsplash.com/photo-1544145945-f90425340c7e?w=400',\n            inStock: true,\n            description: 'Jus de bissap artisanal, préparé selon la tradition sénégalaise avec des fleurs d\\'hibiscus locales',\n            unit: 'bouteille',\n            badges: [\n                'Artisanal',\n                'Traditionnel'\n            ],\n            isNew: false,\n            distance: 2.1,\n            deliveryTime: '15-25 min'\n        },\n        {\n            id: '31',\n            name: 'Confiture de Mangue Artisanale 250g',\n            price: 2800,\n            category: 'Artisanat',\n            seller: 'Confitures de Casamance',\n            location: 'Casamance',\n            rating: 4.8,\n            image: 'https://images.unsplash.com/photo-1484723091739-30a097e8f929?w=400',\n            inStock: true,\n            description: 'Confiture artisanale de mangues Kent, préparée avec des fruits locaux et du sucre de canne',\n            unit: 'pot',\n            badges: [\n                'Artisanal',\n                'Bio',\n                'Local'\n            ],\n            isNew: true,\n            isBio: true,\n            distance: 8.2,\n            deliveryTime: '60-75 min'\n        },\n        {\n            id: '32',\n            name: 'Savon Naturel au Karité 100g',\n            price: 1200,\n            category: 'Artisanat',\n            seller: 'Savonnerie Traditionnelle',\n            location: 'Thiès',\n            rating: 4.9,\n            image: 'https://images.unsplash.com/photo-1556228720-195a672e8a03?w=400',\n            inStock: true,\n            description: 'Savon artisanal au beurre de karité pur, fabriqué selon les méthodes traditionnelles',\n            unit: 'pièce',\n            badges: [\n                'Artisanal',\n                'Bio',\n                'Traditionnel'\n            ],\n            isNew: false,\n            isBio: true,\n            distance: 2.8,\n            deliveryTime: '20-30 min'\n        },\n        {\n            id: '33',\n            name: 'Panier Tressé en Raphia',\n            price: 3500,\n            category: 'Artisanat',\n            seller: 'Artisans de Kaolack',\n            location: 'Kaolack',\n            rating: 4.6,\n            image: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400',\n            inStock: true,\n            description: 'Panier artisanal tressé à la main en raphia naturel, idéal pour les courses ou la décoration',\n            unit: 'pièce',\n            badges: [\n                'Artisanal',\n                'Traditionnel',\n                'Local'\n            ],\n            isNew: true,\n            distance: 5.1,\n            deliveryTime: '40-50 min'\n        },\n        {\n            id: '34',\n            name: 'Jus de Gingembre Frais 500ml',\n            price: 1000,\n            category: 'Artisanat',\n            seller: 'Boissons Naturelles Dakar',\n            location: 'Dakar',\n            rating: 4.5,\n            image: 'https://images.unsplash.com/photo-1570197788417-0e82375c9371?w=400',\n            inStock: true,\n            description: 'Jus de gingembre frais artisanal, énergisant et rafraîchissant, préparé quotidiennement',\n            unit: 'bouteille',\n            badges: [\n                'Artisanal',\n                'Frais'\n            ],\n            isNew: false,\n            distance: 2.1,\n            deliveryTime: '15-25 min'\n        },\n        {\n            id: '35',\n            name: 'Oignons Violets de Galmi 1kg',\n            price: 1200,\n            category: 'Légumes',\n            seller: 'Maraîchers de Galmi',\n            location: 'Galmi, Niger',\n            rating: 4.7,\n            image: 'https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=400',\n            inStock: true,\n            description: 'Oignons violets de Galmi, réputés pour leur saveur douce et leur conservation exceptionnelle',\n            unit: 'kg',\n            badges: [\n                'Premium',\n                'Local'\n            ],\n            isNew: false,\n            distance: 12.5,\n            deliveryTime: '2-3 heures'\n        },\n        {\n            id: '36',\n            name: 'Piments Scotch Bonnet 250g',\n            price: 800,\n            category: 'Légumes',\n            seller: 'Jardin Épicé de Thiès',\n            location: 'Thiès',\n            rating: 4.8,\n            image: 'https://images.unsplash.com/photo-1583454110551-21f2fa2afe61?w=400',\n            inStock: true,\n            description: 'Piments Scotch Bonnet extra forts, parfaits pour relever vos plats traditionnels',\n            unit: 'barquette',\n            weight: '250g',\n            badges: [\n                'Frais',\n                'Local'\n            ],\n            isNew: true,\n            distance: 2.8,\n            deliveryTime: '20-30 min'\n        },\n        {\n            id: '37',\n            name: 'Choux Verts Bio 1 pièce',\n            price: 600,\n            category: 'Légumes',\n            seller: 'Bio Ferme des Niayes',\n            location: 'Niayes',\n            rating: 4.5,\n            image: 'https://images.unsplash.com/photo-1594282486552-05b4d80fbb9f?w=400',\n            inStock: true,\n            description: 'Choux verts biologiques cultivés dans les Niayes, riches en vitamines',\n            unit: 'pièce',\n            badges: [\n                'Bio',\n                'Frais',\n                'Local'\n            ],\n            isNew: false,\n            isBio: true,\n            distance: 3.7,\n            deliveryTime: '25-35 min'\n        },\n        {\n            id: '38',\n            name: 'Carottes Orange 1kg',\n            price: 900,\n            category: 'Légumes',\n            seller: 'Potager de Saint-Louis',\n            location: 'Saint-Louis',\n            rating: 4.6,\n            image: 'https://images.unsplash.com/photo-1445282768818-728615cc910a?w=400',\n            inStock: true,\n            description: 'Carottes orange fraîches, croquantes et sucrées, cultivées dans la vallée du fleuve',\n            unit: 'kg',\n            badges: [\n                'Frais',\n                'Local'\n            ],\n            isNew: false,\n            distance: 4.2,\n            deliveryTime: '30-40 min'\n        },\n        {\n            id: '39',\n            name: 'Aubergines Violettes 500g',\n            price: 1100,\n            category: 'Légumes',\n            seller: 'Jardin de Fatou',\n            location: 'Rufisque',\n            rating: 4.4,\n            image: 'https://images.unsplash.com/photo-1659261200833-ec8761558af7?w=400',\n            inStock: true,\n            description: 'Aubergines violettes fraîches, parfaites pour vos thieboudienne et ragoûts',\n            unit: 'barquette',\n            weight: '500g',\n            badges: [\n                'Frais',\n                'Local'\n            ],\n            isNew: false,\n            distance: 1.8,\n            deliveryTime: '15-25 min'\n        },\n        {\n            id: '40',\n            name: 'Gombo Frais 300g',\n            price: 700,\n            category: 'Légumes',\n            seller: 'Maraîchers de Casamance',\n            location: 'Casamance',\n            rating: 4.9,\n            image: 'https://images.unsplash.com/photo-1631207829628-5d8b3c8b1e3d?w=400',\n            inStock: true,\n            description: 'Gombo frais de Casamance, ingrédient essentiel de la cuisine sénégalaise',\n            unit: 'barquette',\n            weight: '300g',\n            badges: [\n                'Frais',\n                'Traditionnel',\n                'Local'\n            ],\n            isNew: false,\n            distance: 8.2,\n            deliveryTime: '60-75 min'\n        },\n        {\n            id: '41',\n            name: 'Épinards Locaux 250g',\n            price: 500,\n            category: 'Légumes',\n            seller: 'Jardin Vert de Pikine',\n            location: 'Pikine',\n            rating: 4.3,\n            image: 'https://images.unsplash.com/photo-1576045057995-568f588f82fb?w=400',\n            inStock: true,\n            description: 'Épinards locaux frais, riches en fer et vitamines, cultivés sans pesticides',\n            unit: 'botte',\n            weight: '250g',\n            badges: [\n                'Bio',\n                'Frais',\n                'Local'\n            ],\n            isNew: true,\n            isBio: true,\n            distance: 1.5,\n            deliveryTime: '10-20 min'\n        },\n        {\n            id: '42',\n            name: 'Courgettes Vertes 1kg',\n            price: 1300,\n            category: 'Légumes',\n            seller: 'Ferme Maraîchère de Mbour',\n            location: 'Mbour',\n            rating: 4.7,\n            image: 'https://images.unsplash.com/photo-1566385101042-1a0aa0c1268c?w=400',\n            inStock: true,\n            description: 'Courgettes vertes tendres et savoureuses, parfaites pour vos gratins et ratatouilles',\n            unit: 'kg',\n            badges: [\n                'Frais',\n                'Local'\n            ],\n            isNew: false,\n            distance: 3.8,\n            deliveryTime: '30-40 min'\n        },\n        {\n            id: '43',\n            name: 'Oranges Douces de Valencia 2kg',\n            price: 1800,\n            category: 'Fruits',\n            seller: 'Vergers de Casamance',\n            location: 'Casamance',\n            rating: 4.8,\n            image: 'https://images.unsplash.com/photo-1547514701-42782101795e?w=400',\n            inStock: true,\n            description: 'Oranges douces et juteuses de Valencia, riches en vitamine C, cultivées en Casamance',\n            unit: 'kg',\n            weight: '2kg',\n            badges: [\n                'Frais',\n                'Local',\n                'Premium'\n            ],\n            isNew: false,\n            distance: 8.2,\n            deliveryTime: '60-75 min'\n        },\n        {\n            id: '44',\n            name: 'Papayes Mûres 1 pièce',\n            price: 2200,\n            category: 'Fruits',\n            seller: 'Plantation Tropicale du Sud',\n            location: 'Ziguinchor',\n            rating: 4.9,\n            image: 'https://images.unsplash.com/photo-1617112848923-cc2234396a8d?w=400',\n            inStock: true,\n            description: 'Papayes mûres à point, chair orange et sucrée, excellente source d\\'enzymes digestives',\n            unit: 'pièce',\n            weight: '1.5kg',\n            badges: [\n                'Frais',\n                'Premium',\n                'Local'\n            ],\n            isNew: true,\n            distance: 6.8,\n            deliveryTime: '45-60 min'\n        },\n        {\n            id: '45',\n            name: 'Citrons Verts 500g',\n            price: 800,\n            category: 'Fruits',\n            seller: 'Agrumes de Thiès',\n            location: 'Thiès',\n            rating: 4.6,\n            image: 'https://images.unsplash.com/photo-1568702846914-96b305d2aaeb?w=400',\n            inStock: true,\n            description: 'Citrons verts frais et acidulés, parfaits pour vos boissons et marinades',\n            unit: 'barquette',\n            weight: '500g',\n            badges: [\n                'Frais',\n                'Local'\n            ],\n            isNew: false,\n            distance: 2.8,\n            deliveryTime: '20-30 min'\n        },\n        {\n            id: '46',\n            name: 'Pastèques Rouges 1 pièce',\n            price: 3500,\n            category: 'Fruits',\n            seller: 'Ferme de la Vallée',\n            location: 'Saint-Louis',\n            rating: 4.7,\n            image: 'https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=400',\n            inStock: true,\n            description: 'Pastèques rouges sucrées et rafraîchissantes, cultivées dans la vallée du fleuve Sénégal',\n            unit: 'pièce',\n            weight: '4-5kg',\n            badges: [\n                'Frais',\n                'Local',\n                'Premium'\n            ],\n            isNew: false,\n            distance: 4.2,\n            deliveryTime: '30-40 min'\n        },\n        {\n            id: '47',\n            name: 'Goyaves Roses 1kg',\n            price: 1500,\n            category: 'Fruits',\n            seller: 'Vergers Tropicaux',\n            location: 'Casamance',\n            rating: 4.5,\n            image: 'https://images.unsplash.com/photo-1536511132770-e5058c4e1d93?w=400',\n            inStock: true,\n            description: 'Goyaves roses parfumées, riches en vitamine C et antioxydants naturels',\n            unit: 'kg',\n            badges: [\n                'Frais',\n                'Local',\n                'Bio'\n            ],\n            isNew: true,\n            isBio: true,\n            distance: 8.2,\n            deliveryTime: '60-75 min'\n        },\n        {\n            id: '48',\n            name: 'Avocats Hass 4 pièces',\n            price: 2800,\n            category: 'Fruits',\n            seller: 'Plantation d\\'Avocats Bio',\n            location: 'Niayes',\n            rating: 4.8,\n            image: 'https://images.unsplash.com/photo-1523049673857-eb18f1d7b578?w=400',\n            inStock: true,\n            description: 'Avocats Hass biologiques, crémeux et nutritifs, parfaits pour vos salades et smoothies',\n            unit: 'lot de 4',\n            badges: [\n                'Bio',\n                'Premium',\n                'Local'\n            ],\n            isNew: false,\n            isBio: true,\n            distance: 3.7,\n            deliveryTime: '25-35 min'\n        }\n    ];\n    // Fonctions utilitaires\n    const formatPrice = (price)=>{\n        return new Intl.NumberFormat('fr-FR', {\n            style: 'currency',\n            currency: 'XOF',\n            minimumFractionDigits: 0\n        }).format(price).replace('XOF', 'FCFA');\n    };\n    const toggleFavorite = (productId)=>{\n        setFavorites((prev)=>{\n            const newFavorites = new Set(prev);\n            if (newFavorites.has(productId)) {\n                newFavorites.delete(productId);\n            } else {\n                newFavorites.add(productId);\n            }\n            return newFavorites;\n        });\n    };\n    const updateCart = (productId, quantity)=>{\n        setCart((prev)=>({\n                ...prev,\n                [productId]: Math.max(0, quantity)\n            }));\n    };\n    const toggleTag = (tagName)=>{\n        setSelectedTags((prev)=>prev.includes(tagName) ? prev.filter((tag)=>tag !== tagName) : [\n                ...prev,\n                tagName\n            ]);\n    };\n    const clearAllFilters = ()=>{\n        setSelectedCategory('all');\n        setSearchQuery('');\n        setSelectedTags([]);\n        setShowOnlyPromo(false);\n        setShowOnlyBio(false);\n        setMinRating(0);\n        setPriceRange([\n            0,\n            50000\n        ]);\n    };\n    const addNotification = (message, type)=>{\n        const id = Date.now().toString();\n        setNotifications((prev)=>[\n                ...prev,\n                {\n                    id,\n                    message,\n                    type\n                }\n            ]);\n        setTimeout(()=>{\n            setNotifications((prev)=>prev.filter((n)=>n.id !== id));\n        }, 3000);\n    };\n    const scrollToTop = ()=>{\n        window.scrollTo({\n            top: 0,\n            behavior: 'smooth'\n        });\n    };\n    // Filtrage et tri\n    const filteredAndSortedProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductsPage.useMemo[filteredAndSortedProducts]\": ()=>{\n            let filtered = products.filter({\n                \"ProductsPage.useMemo[filteredAndSortedProducts].filtered\": (product)=>{\n                    const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) || product.seller.toLowerCase().includes(searchQuery.toLowerCase()) || product.category.toLowerCase().includes(searchQuery.toLowerCase()) || product.description.toLowerCase().includes(searchQuery.toLowerCase());\n                    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;\n                    const matchesPrice = product.price >= priceRange[0] && product.price <= priceRange[1];\n                    const matchesRating = product.rating >= minRating;\n                    const matchesPromo = !showOnlyPromo || product.isPromo;\n                    const matchesBio = !showOnlyBio || product.isBio;\n                    const matchesTags = selectedTags.length === 0 || selectedTags.every({\n                        \"ProductsPage.useMemo[filteredAndSortedProducts].filtered\": (tag)=>{\n                            var _product_badges;\n                            return (_product_badges = product.badges) === null || _product_badges === void 0 ? void 0 : _product_badges.includes(tag);\n                        }\n                    }[\"ProductsPage.useMemo[filteredAndSortedProducts].filtered\"]);\n                    return matchesSearch && matchesCategory && matchesPrice && matchesRating && matchesPromo && matchesBio && matchesTags;\n                }\n            }[\"ProductsPage.useMemo[filteredAndSortedProducts].filtered\"]);\n            filtered.sort({\n                \"ProductsPage.useMemo[filteredAndSortedProducts]\": (a, b)=>{\n                    switch(sortBy){\n                        case 'price':\n                            return (a.promoPrice || a.price) - (b.promoPrice || b.price);\n                        case 'rating':\n                            return (b.rating || 0) - (a.rating || 0);\n                        case 'distance':\n                            return (a.distance || 0) - (b.distance || 0);\n                        default:\n                            return a.name.localeCompare(b.name);\n                    }\n                }\n            }[\"ProductsPage.useMemo[filteredAndSortedProducts]\"]);\n            return filtered;\n        }\n    }[\"ProductsPage.useMemo[filteredAndSortedProducts]\"], [\n        products,\n        searchQuery,\n        selectedCategory,\n        priceRange,\n        minRating,\n        showOnlyPromo,\n        showOnlyBio,\n        sortBy,\n        selectedTags\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductsPage.useEffect\": ()=>{\n            const fetchProducts = {\n                \"ProductsPage.useEffect.fetchProducts\": async ()=>{\n                    setIsLoading(true);\n                    await new Promise({\n                        \"ProductsPage.useEffect.fetchProducts\": (resolve)=>setTimeout(resolve, 1000)\n                    }[\"ProductsPage.useEffect.fetchProducts\"]);\n                    setProducts(demoProducts);\n                    setIsLoading(false);\n                }\n            }[\"ProductsPage.useEffect.fetchProducts\"];\n            fetchProducts();\n        }\n    }[\"ProductsPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductsPage.useEffect\": ()=>{\n            setFilteredProducts(filteredAndSortedProducts);\n        }\n    }[\"ProductsPage.useEffect\"], [\n        filteredAndSortedProducts\n    ]);\n    // Gestion du scroll pour le bouton retour en haut\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductsPage.useEffect\": ()=>{\n            const handleScroll = {\n                \"ProductsPage.useEffect.handleScroll\": ()=>{\n                    setShowScrollTop(window.scrollY > 400);\n                }\n            }[\"ProductsPage.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"ProductsPage.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"ProductsPage.useEffect\"];\n        }\n    }[\"ProductsPage.useEffect\"], []);\n    // Skeleton Loader Component inspiré de TastyDaily\n    const SkeletonLoader = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-900\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative h-80 bg-gradient-to-r from-gray-300 to-gray-400 dark:from-gray-700 dark:to-gray-600 animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-black/20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 819,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-10 h-full flex items-center justify-center text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto px-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-16 bg-white/20 rounded-2xl mb-4 animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 822,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 bg-white/20 rounded-xl mb-6 animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 823,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-10 w-32 bg-white/20 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 825,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-10 w-32 bg-white/20 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 826,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 824,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 821,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 820,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                    lineNumber: 818,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"sticky top-0 z-40 bg-white/95 dark:bg-gray-800/95 backdrop-blur-md border-b border-gray-200 dark:border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 py-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col lg:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 h-14 bg-gray-200 dark:bg-gray-700 rounded-2xl animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 836,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-14 w-32 bg-gray-200 dark:bg-gray-700 rounded-2xl animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 838,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-14 w-20 bg-gray-200 dark:bg-gray-700 rounded-2xl animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 839,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 837,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 835,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3 overflow-x-auto pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 844,\n                                        columnNumber: 13\n                                    }, this),\n                                    [\n                                        ...Array(8)\n                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-10 w-24 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse flex-shrink-0\"\n                                        }, i, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 846,\n                                            columnNumber: 15\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 843,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 834,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                    lineNumber: 833,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 py-8\",\n                    children: [\n                        [\n                            ...Array(3)\n                        ].map((_, sectionIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-2xl animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 859,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-6 w-40 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 861,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-4 w-60 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 862,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 860,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 858,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-6 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 865,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 857,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                        children: [\n                                            ...Array(4)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-48 bg-gray-200 dark:bg-gray-700 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 871,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-5 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 873,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-6 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 875,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 876,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 874,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 872,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, i, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 870,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 868,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, sectionIndex, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 856,\n                                columnNumber: 11\n                            }, this)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-48 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 887,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                                    children: [\n                                        ...Array(12)\n                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white dark:bg-gray-800 rounded-3xl shadow-lg overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-56 bg-gray-200 dark:bg-gray-700 animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 891,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-start mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 895,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-4 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 896,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 894,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-6 w-12 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 898,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 893,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 900,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-4 w-3/4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 901,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-8 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 903,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-10 w-24 bg-gray-200 dark:bg-gray-700 rounded-2xl animate-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 904,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 902,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 892,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, i, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 890,\n                                            columnNumber: 15\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 888,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 886,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                    lineNumber: 853,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n            lineNumber: 816,\n            columnNumber: 5\n        }, this);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonLoader, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n            lineNumber: 916,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative h-80 bg-gradient-to-r from-green-600 to-blue-600 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/30\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 923,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-cover bg-center filter blur-sm\",\n                        style: {\n                            backgroundImage: 'url(https://images.unsplash.com/photo-1542838132-92c53300491e?w=1200)'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 924,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 h-full flex items-center justify-center text-center text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            className: \"max-w-4xl mx-auto px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl md:text-6xl font-bold mb-4\",\n                                    children: \"\\uD83D\\uDED2 LocaFresh Market\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 937,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl md:text-2xl opacity-90 mb-6\",\n                                    children: \"D\\xe9couvrez les meilleurs produits locaux pr\\xe8s de chez vous\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 940,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center gap-4 text-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full\",\n                                            children: [\n                                                \"\\uD83C\\uDF31 \",\n                                                filteredAndSortedProducts.length,\n                                                \" produits frais\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 944,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full\",\n                                            children: \"\\uD83D\\uDE9A Livraison rapide\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 947,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 943,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 931,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 930,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                lineNumber: 922,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                className: \"sticky top-0 z-40 bg-white/95 dark:bg-gray-800/95 backdrop-blur-md border-b border-gray-200 dark:border-gray-700 shadow-lg\",\n                initial: {\n                    opacity: 0,\n                    y: -20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    delay: 0.3\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col lg:flex-row gap-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaSearch, {\n                                            className: \"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 966,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Rechercher fruits, l\\xe9gumes, viandes, poissons...\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            className: \"w-full pl-12 pr-4 py-4 bg-white dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-2xl focus:outline-none focus:ring-4 focus:ring-green-100 dark:focus:ring-green-900 focus:border-green-400 transition-all duration-300 text-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 967,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 965,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: sortBy,\n                                            onChange: (e)=>setSortBy(e.target.value),\n                                            className: \"px-4 py-4 bg-white dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-2xl focus:outline-none focus:ring-2 focus:ring-green-500 text-gray-900 dark:text-white\",\n                                            title: \"Trier par\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"name\",\n                                                    children: \"\\uD83D\\uDCDD Nom\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 984,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"price\",\n                                                    children: \"\\uD83D\\uDCB0 Prix\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 985,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"rating\",\n                                                    children: \"⭐ Note\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 986,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"distance\",\n                                                    children: \"\\uD83D\\uDCCD Distance\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 987,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 978,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex bg-gray-100 dark:bg-gray-700 rounded-2xl p-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setViewMode('grid'),\n                                                    className: \"px-4 py-3 rounded-xl transition-all duration-300 \".concat(viewMode === 'grid' ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-md' : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'),\n                                                    title: \"Vue grille\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaTh, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1001,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 991,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setViewMode('list'),\n                                                    className: \"px-4 py-3 rounded-xl transition-all duration-300 \".concat(viewMode === 'list' ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-md' : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'),\n                                                    title: \"Vue liste\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaList, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1013,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1003,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 990,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 977,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 964,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 overflow-x-auto pb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-gray-700 dark:text-gray-300 whitespace-nowrap\",\n                                    children: \"Cat\\xe9gories :\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 1021,\n                                    columnNumber: 13\n                                }, this),\n                                locaFreshCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                                        type: \"button\",\n                                        onClick: ()=>setSelectedCategory(category.id),\n                                        className: \"flex items-center gap-2 px-4 py-2 rounded-full whitespace-nowrap transition-all duration-300 \".concat(selectedCategory === category.id ? \"bg-gradient-to-r \".concat(category.color, \" text-white shadow-lg scale-105\") : 'bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'),\n                                        whileHover: {\n                                            scale: selectedCategory === category.id ? 1.05 : 1.02\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg\",\n                                                children: category.emoji\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1035,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: category.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1036,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, category.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 1023,\n                                        columnNumber: 15\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 1020,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 overflow-x-auto pb-2 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-gray-700 dark:text-gray-300 whitespace-nowrap\",\n                                    children: \"Tags populaires :\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 1043,\n                                    columnNumber: 13\n                                }, this),\n                                popularTags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                                        type: \"button\",\n                                        onClick: ()=>toggleTag(tag.name),\n                                        className: \"flex items-center gap-2 px-3 py-1.5 rounded-full whitespace-nowrap text-sm font-medium transition-all duration-300 \".concat(selectedTags.includes(tag.name) ? \"\".concat(tag.color, \" shadow-md scale-105 ring-2 ring-offset-2 ring-blue-500 dark:ring-offset-gray-800\") : \"\".concat(tag.color, \" hover:shadow-md hover:scale-102 opacity-70 hover:opacity-100\")),\n                                        whileHover: {\n                                            scale: selectedTags.includes(tag.name) ? 1.05 : 1.02\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: tag.emoji\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1057,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: tag.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1058,\n                                                columnNumber: 17\n                                            }, this),\n                                            selectedTags.includes(tag.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.span, {\n                                                initial: {\n                                                    scale: 0\n                                                },\n                                                animate: {\n                                                    scale: 1\n                                                },\n                                                className: \"text-xs\",\n                                                children: \"✓\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1060,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, tag.name, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 1045,\n                                        columnNumber: 15\n                                    }, this)),\n                                (selectedTags.length > 0 || selectedCategory !== 'all' || searchQuery || showOnlyPromo || showOnlyBio || minRating > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                                    type: \"button\",\n                                    onClick: clearAllFilters,\n                                    className: \"flex items-center gap-2 px-3 py-1.5 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-all duration-300\",\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaTimes, {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 1080,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Effacer tout\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 1081,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 1073,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 1042,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mt-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setShowFilters(!showFilters),\n                                        className: \"flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-xl transition-all duration-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaFilter, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1094,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Filtres\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1095,\n                                                columnNumber: 17\n                                            }, this),\n                                            (showOnlyPromo || showOnlyBio || minRating > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-red-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1097,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 1089,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: filteredAndSortedProducts.length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1102,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" produit\",\n                                            filteredAndSortedProducts.length > 1 ? 's' : '',\n                                            \" trouv\\xe9\",\n                                            filteredAndSortedProducts.length > 1 ? 's' : ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 1101,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 1088,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 1087,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                            children: showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    height: 0\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    height: 'auto'\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    height: 0\n                                },\n                                transition: {\n                                    duration: 0.3\n                                },\n                                className: \"mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                    children: [\n                                                        \"Prix maximum: \",\n                                                        priceRange[1].toLocaleString(),\n                                                        \" FCFA\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1119,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"range\",\n                                                    min: \"0\",\n                                                    max: \"50000\",\n                                                    value: priceRange[1],\n                                                    onChange: (e)=>setPriceRange([\n                                                            0,\n                                                            Number(e.target.value)\n                                                        ]),\n                                                    className: \"w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1122,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 1118,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                    children: [\n                                                        \"Note minimum: \",\n                                                        minRating,\n                                                        \"⭐\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1132,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"range\",\n                                                    min: \"0\",\n                                                    max: \"5\",\n                                                    step: \"0.5\",\n                                                    value: minRating,\n                                                    onChange: (e)=>setMinRating(Number(e.target.value)),\n                                                    className: \"w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1135,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 1131,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: showOnlyPromo,\n                                                            onChange: (e)=>setShowOnlyPromo(e.target.checked),\n                                                            className: \"rounded border-gray-300 text-green-600 focus:ring-green-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1147,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 text-sm text-gray-700 dark:text-gray-300\",\n                                                            children: \"\\uD83C\\uDFF7️ Promotions uniquement\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1153,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1146,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: showOnlyBio,\n                                                            onChange: (e)=>setShowOnlyBio(e.target.checked),\n                                                            className: \"rounded border-gray-300 text-green-600 focus:ring-green-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1156,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 text-sm text-gray-700 dark:text-gray-300\",\n                                                            children: \"\\uD83C\\uDF31 Bio uniquement\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1162,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1155,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 1145,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 1117,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 1110,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 1108,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                    lineNumber: 962,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                lineNumber: 956,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.section, {\n                        className: \"mb-12\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.4\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-r from-red-500 to-orange-500 rounded-2xl flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaFire, {\n                                                    className: \"text-white text-xl\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1184,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1183,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: \"Offres du jour\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1187,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                        children: \"Profitez de nos promotions exceptionnelles\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1188,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1186,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 1182,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/fr/products?filter=promo\",\n                                        className: \"text-blue-600 hover:text-blue-700 font-medium\",\n                                        children: \"Voir tout →\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 1191,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 1181,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                children: filteredAndSortedProducts.filter((p)=>p.isPromo).slice(0, 4).map((product, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        className: \"group bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 border border-red-100 dark:border-red-900\",\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.9\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            delay: index * 0.1\n                                        },\n                                        whileHover: {\n                                            y: -4\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/fr/products/\".concat(product.id),\n                                            className: \"block\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative h-48 overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            src: product.image,\n                                                            alt: product.name,\n                                                            fill: true,\n                                                            className: \"object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1208,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-3 left-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg\",\n                                                                children: [\n                                                                    \"\\uD83C\\uDFF7️ -\",\n                                                                    Math.round((product.price - (product.promoPrice || product.price)) / product.price * 100),\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1215,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1214,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: (e)=>{\n                                                                e.preventDefault();\n                                                                e.stopPropagation();\n                                                                toggleFavorite(product.id);\n                                                            },\n                                                            className: \"absolute top-3 right-3 w-8 h-8 rounded-full shadow-lg transition-all duration-300 \".concat(favorites.has(product.id) ? 'bg-red-500 text-white' : 'bg-white/90 text-gray-600 hover:bg-red-500 hover:text-white'),\n                                                            title: favorites.has(product.id) ? 'Retirer des favoris' : 'Ajouter aux favoris',\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaHeart, {\n                                                                className: \"w-3 h-3 mx-auto\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1233,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1219,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1207,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-bold text-gray-900 dark:text-white mb-2 line-clamp-1\",\n                                                            children: product.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1238,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg font-bold text-red-600 dark:text-red-400\",\n                                                                            children: formatPrice(product.promoPrice || product.price)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 1241,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        product.promoPrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-500 line-through\",\n                                                                            children: formatPrice(product.price)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 1245,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 1240,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: (e)=>{\n                                                                        e.preventDefault();\n                                                                        e.stopPropagation();\n                                                                        updateCart(product.id, (cart[product.id] || 0) + 1);\n                                                                        addNotification(\"\".concat(product.name, \" ajout\\xe9 au panier\"), 'success');\n                                                                    },\n                                                                    className: \"w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-full flex items-center justify-center hover:from-green-600 hover:to-emerald-600 transition-all duration-300\",\n                                                                    title: \"Ajouter au panier\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaPlus, {\n                                                                        className: \"w-3 h-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 1261,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 1250,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1239,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1237,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 1206,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, \"promo-\".concat(product.id), false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 1198,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 1196,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 1175,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.section, {\n                        className: \"mb-12\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.5\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-xl\",\n                                                    children: \"\\uD83D\\uDD25\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1281,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1280,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: \"Produits populaires\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1284,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                        children: \"Les plus appr\\xe9ci\\xe9s par nos clients\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1285,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1283,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 1279,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/fr/products?sort=rating\",\n                                        className: \"text-blue-600 hover:text-blue-700 font-medium\",\n                                        children: \"Voir tout →\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 1288,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 1278,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 gap-6\",\n                                children: filteredAndSortedProducts.filter((p)=>p.rating >= 4.5).slice(0, 6).map((product, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        className: \"group bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500 border border-orange-100 dark:border-orange-900\",\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.9\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            delay: index * 0.1\n                                        },\n                                        whileHover: {\n                                            y: -8,\n                                            scale: 1.02\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative h-48 md:h-56 overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        src: product.image,\n                                                        alt: product.name,\n                                                        fill: true,\n                                                        className: \"object-cover group-hover:scale-110 transition-transform duration-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1307,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-3 left-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg\",\n                                                            children: [\n                                                                \"⭐ \",\n                                                                product.rating\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1314,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1313,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>toggleFavorite(product.id),\n                                                        className: \"absolute top-3 right-3 w-8 h-8 rounded-full shadow-lg transition-all duration-300 \".concat(favorites.has(product.id) ? 'bg-red-500 text-white' : 'bg-white/90 text-gray-600 hover:bg-red-500 hover:text-white'),\n                                                        title: favorites.has(product.id) ? 'Retirer des favoris' : 'Ajouter aux favoris',\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaHeart, {\n                                                            className: \"w-3 h-3 mx-auto\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1328,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1318,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1306,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-bold text-gray-900 dark:text-white mb-2 line-clamp-1\",\n                                                        children: product.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1333,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-600 dark:text-gray-400 mb-3 line-clamp-2\",\n                                                        children: product.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1334,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col\",\n                                                                children: product.isPromo && product.promoPrice ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg font-bold text-red-600 dark:text-red-400\",\n                                                                            children: formatPrice(product.promoPrice)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 1339,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-500 line-through\",\n                                                                            children: formatPrice(product.price)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 1342,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg font-bold text-gray-900 dark:text-white\",\n                                                                    children: formatPrice(product.price)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 1347,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1336,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>{\n                                                                    updateCart(product.id, (cart[product.id] || 0) + 1);\n                                                                    addNotification(\"\".concat(product.name, \" ajout\\xe9 au panier\"), 'success');\n                                                                },\n                                                                className: \"px-4 py-2 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-xl text-sm font-medium hover:from-orange-600 hover:to-red-600 transition-all duration-300 shadow-md hover:shadow-lg\",\n                                                                title: \"Ajouter au panier\",\n                                                                children: \"Ajouter\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1352,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1335,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1332,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, \"popular-\".concat(product.id), true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 1298,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 1293,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 1272,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.section, {\n                        className: \"mb-12\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.6\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-xl\",\n                                                    children: \"\\uD83E\\uDD6C\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1380,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1379,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: \"L\\xe9gumes Frais du Jour\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1383,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                        children: \"Fra\\xeecheur garantie, directement des producteurs locaux\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1384,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1382,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 1378,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/fr/products?category=L\\xe9gumes\",\n                                        className: \"text-blue-600 hover:text-blue-700 font-medium\",\n                                        children: \"Voir tout →\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 1387,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 1377,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                children: filteredAndSortedProducts.filter((p)=>p.category === 'Légumes').slice(0, 8).map((product, index)=>{\n                                    var _product_badges;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        className: \"group bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500 border border-green-100 dark:border-green-900\",\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.9\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            delay: index * 0.1\n                                        },\n                                        whileHover: {\n                                            y: -8,\n                                            scale: 1.02\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/fr/products/\".concat(product.id),\n                                            className: \"block\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative h-48 overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            src: product.image,\n                                                            alt: product.name,\n                                                            fill: true,\n                                                            className: \"object-cover group-hover:scale-110 transition-transform duration-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1407,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        product.isNew && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-3 left-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-gradient-to-r from-green-500 to-emerald-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg\",\n                                                                children: \"✨ Nouveau\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1415,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1414,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        product.isBio && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-3 left-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-gradient-to-r from-green-600 to-green-700 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg\",\n                                                                children: \"\\uD83C\\uDF31 Bio\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1422,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1421,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: (e)=>{\n                                                                e.preventDefault();\n                                                                e.stopPropagation();\n                                                                toggleFavorite(product.id);\n                                                            },\n                                                            className: \"absolute top-3 right-3 w-8 h-8 rounded-full shadow-lg transition-all duration-300 \".concat(favorites.has(product.id) ? 'bg-red-500 text-white' : 'bg-white/90 text-gray-600 hover:bg-red-500 hover:text-white'),\n                                                            title: favorites.has(product.id) ? 'Retirer des favoris' : 'Ajouter aux favoris',\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaHeart, {\n                                                                className: \"w-3 h-3 mx-auto\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1441,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1427,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1406,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-start mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-bold text-gray-900 dark:text-white mb-1 line-clamp-1\",\n                                                                            children: product.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 1448,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                            children: product.seller\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 1449,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 1447,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-1 bg-yellow-100 dark:bg-yellow-900 px-2 py-1 rounded-full\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaStar, {\n                                                                            className: \"w-3 h-3 text-yellow-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 1452,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs font-medium text-yellow-700 dark:text-yellow-300\",\n                                                                            children: product.rating\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 1453,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 1451,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1446,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-600 dark:text-gray-400 mb-3 line-clamp-2\",\n                                                            children: product.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1457,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-1 mb-3\",\n                                                            children: (_product_badges = product.badges) === null || _product_badges === void 0 ? void 0 : _product_badges.slice(0, 2).map((badge)=>{\n                                                                var _popularTags_find;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs px-2 py-1 rounded-full \".concat(((_popularTags_find = popularTags.find((tag)=>tag.name === badge)) === null || _popularTags_find === void 0 ? void 0 : _popularTags_find.color) || 'bg-gray-100 text-gray-800'),\n                                                                    children: badge\n                                                                }, badge, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 1461,\n                                                                    columnNumber: 25\n                                                                }, this);\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1459,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg font-bold text-green-600 dark:text-green-400\",\n                                                                            children: formatPrice(product.price)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 1474,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: [\n                                                                                \"par \",\n                                                                                product.unit\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 1477,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 1473,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: (e)=>{\n                                                                        e.preventDefault();\n                                                                        e.stopPropagation();\n                                                                        updateCart(product.id, (cart[product.id] || 0) + 1);\n                                                                        addNotification(\"\".concat(product.name, \" ajout\\xe9 au panier\"), 'success');\n                                                                    },\n                                                                    className: \"px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-xl text-sm font-medium hover:from-green-600 hover:to-emerald-600 transition-all duration-300 shadow-md hover:shadow-lg\",\n                                                                    title: \"Ajouter au panier\",\n                                                                    children: \"Ajouter\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 1479,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1472,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1445,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 1405,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, \"vegetables-\".concat(product.id), false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 1397,\n                                        columnNumber: 15\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 1392,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 1371,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.section, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.7\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                            children: \"Tous nos produits\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 1508,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-400\",\n                                            children: \"D\\xe9couvrez notre s\\xe9lection compl\\xe8te\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 1509,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 1507,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 1506,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                                children: filteredAndSortedProducts.map((product, index)=>{\n                                    var _product_badges;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        className: \"group bg-white dark:bg-gray-800 rounded-3xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500\",\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.9\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            delay: index * 0.05\n                                        },\n                                        whileHover: {\n                                            y: -8\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative h-56 overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        src: product.image,\n                                                        alt: product.name,\n                                                        fill: true,\n                                                        className: \"object-cover group-hover:scale-110 transition-transform duration-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1524,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    product.isPromo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-3 left-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg\",\n                                                            children: [\n                                                                \"\\uD83C\\uDFF7️ -\",\n                                                                Math.round((product.price - (product.promoPrice || product.price)) / product.price * 100),\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1532,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1531,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    product.isNew && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-3 left-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-gradient-to-r from-green-500 to-emerald-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg\",\n                                                            children: \"✨ Nouveau\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1539,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1538,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>toggleFavorite(product.id),\n                                                        className: \"absolute top-3 right-3 w-10 h-10 rounded-full shadow-lg transition-all duration-300 \".concat(favorites.has(product.id) ? 'bg-red-500 text-white' : 'bg-white/90 text-gray-600 hover:bg-red-500 hover:text-white'),\n                                                        title: favorites.has(product.id) ? 'Retirer des favoris' : 'Ajouter aux favoris',\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaHeart, {\n                                                            className: \"w-4 h-4 mx-auto\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1554,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1544,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1523,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-bold text-gray-900 dark:text-white mb-1 line-clamp-1\",\n                                                                        children: product.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 1561,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                        children: product.seller\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 1562,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1560,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1 bg-yellow-100 dark:bg-yellow-900 px-2 py-1 rounded-full\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaStar, {\n                                                                        className: \"w-3 h-3 text-yellow-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 1565,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-medium text-yellow-700 dark:text-yellow-300\",\n                                                                        children: product.rating\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 1566,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1564,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1559,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-2\",\n                                                        children: product.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1570,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1 mb-4\",\n                                                        children: (_product_badges = product.badges) === null || _product_badges === void 0 ? void 0 : _product_badges.slice(0, 2).map((badge)=>{\n                                                            var _popularTags_find;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs px-2 py-1 rounded-full \".concat(((_popularTags_find = popularTags.find((tag)=>tag.name === badge)) === null || _popularTags_find === void 0 ? void 0 : _popularTags_find.color) || 'bg-gray-100 text-gray-800'),\n                                                                children: badge\n                                                            }, badge, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1574,\n                                                                columnNumber: 23\n                                                            }, this);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1572,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col\",\n                                                                children: [\n                                                                    product.isPromo && product.promoPrice ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xl font-bold text-red-600 dark:text-red-400\",\n                                                                                children: formatPrice(product.promoPrice)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 1589,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-500 line-through\",\n                                                                                children: formatPrice(product.price)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 1592,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xl font-bold text-gray-900 dark:text-white\",\n                                                                        children: formatPrice(product.price)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 1597,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: [\n                                                                            \"par \",\n                                                                            product.unit\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 1601,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1586,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    cart[product.id] > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2 bg-gray-100 dark:bg-gray-700 rounded-xl px-3 py-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>updateCart(product.id, cart[product.id] - 1),\n                                                                                className: \"w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaMinus, {\n                                                                                    className: \"w-2 h-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                                    lineNumber: 1612,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 1607,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-gray-900 dark:text-white min-w-[20px] text-center\",\n                                                                                children: cart[product.id]\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 1614,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>updateCart(product.id, cart[product.id] + 1),\n                                                                                className: \"w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center hover:bg-green-600 transition-colors\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaPlus, {\n                                                                                    className: \"w-2 h-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                                    lineNumber: 1622,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 1617,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 1606,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: ()=>{\n                                                                            updateCart(product.id, (cart[product.id] || 0) + 1);\n                                                                            addNotification(\"\".concat(product.name, \" ajout\\xe9 au panier\"), 'success');\n                                                                        },\n                                                                        className: \"px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-2xl font-medium hover:from-green-600 hover:to-emerald-600 transition-all duration-300 shadow-md hover:shadow-lg\",\n                                                                        title: \"Ajouter au panier\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaShoppingCart, {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 1636,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 1627,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1604,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1585,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1558,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, product.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 1515,\n                                        columnNumber: 15\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 1513,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 1501,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                lineNumber: 1173,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: 300\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            x: 300\n                        },\n                        className: \"fixed top-4 right-4 z-50 px-6 py-4 rounded-2xl shadow-lg \".concat(notification.type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl\",\n                                    children: notification.type === 'success' ? '✅' : '❌'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 1662,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: notification.message\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 1665,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 1661,\n                            columnNumber: 13\n                        }, this)\n                    }, notification.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 1650,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                lineNumber: 1648,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                children: showScrollTop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                    type: \"button\",\n                    initial: {\n                        opacity: 0,\n                        scale: 0\n                    },\n                    animate: {\n                        opacity: 1,\n                        scale: 1\n                    },\n                    exit: {\n                        opacity: 0,\n                        scale: 0\n                    },\n                    onClick: scrollToTop,\n                    className: \"fixed bottom-8 right-8 z-40 w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center\",\n                    title: \"Retour en haut\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaArrowRight, {\n                        className: \"w-4 h-4 transform -rotate-90\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 1683,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                    lineNumber: 1674,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                lineNumber: 1672,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n        lineNumber: 920,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductsPage, \"V+Jl/P+ygj6BT4LsLoLG6xnxcC8=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_5__.useTranslations,\n        _contexts_OfflineModeContext__WEBPACK_IMPORTED_MODULE_4__.useOfflineMode\n    ];\n});\n_c = ProductsPage;\nvar _c;\n$RefreshReg$(_c, \"ProductsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/products/page.tsx\n"));

/***/ })

});