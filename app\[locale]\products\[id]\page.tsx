"use client";

import React, { useState, useEffect, useRef } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import { motion, AnimatePresence } from "framer-motion";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/contexts/AuthContext";
import { useCart } from "@/contexts/CartContext";
import { Button } from "@/components/ui/Button";
import { Badge } from "@/components/ui/Badge";
import { Card, CardContent } from "@/components/ui/Card";
import { FaStar, FaHeart, FaRegHeart, FaShoppingCart, FaMapMarkerAlt, FaPhone, FaStore, FaArrowLeft, FaShare, FaTruck, FaUtensils, FaLeaf, FaHeart as FaHeartHealth, FaLightbulb, FaClock, FaThermometerHalf } from "react-icons/fa";
import AddToCartAnimation from "@/components/cart/AddToCartAnimation";
import RelatedProducts from "@/components/products/RelatedProducts";
import ProductReviews from "@/components/products/ProductReviews";

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  stock_quantity: number;
  category: string;
  image_url: string;
  created_at: string;
  seller_id: string;
  seller?: {
    id: string;
    name: string;
    avatar_url?: string;
    seller_profiles?: {
      rating?: number;
      review_count?: number;
      address?: string;
    }[];
  };
  is_organic?: boolean;
  is_local?: boolean;
  unit?: string;
  discount?: number;
  rating?: number;
  review_count?: number;
}

// Données enrichies pour les produits - Inspiré de TastyDaily
interface ProductEnrichment {
  recipes: Array<{
    name: string;
    description: string;
    difficulty: 'Facile' | 'Moyen' | 'Difficile';
    time: string;
    icon: string;
  }>;
  tips: Array<{
    title: string;
    description: string;
    icon: string;
    type: 'cooking' | 'storage' | 'preparation';
  }>;
  healthBenefits: Array<{
    title: string;
    description: string;
    icon: string;
    category: 'nutrition' | 'health' | 'wellness';
  }>;
}

// Base de données enrichie des produits locaux sénégalais
const getProductEnrichment = (productName: string, category: string): ProductEnrichment => {
  const defaultEnrichment: ProductEnrichment = {
    recipes: [
      {
        name: "Recette traditionnelle",
        description: "Une délicieuse préparation locale avec ce produit",
        difficulty: 'Facile',
        time: "30 min",
        icon: "🍽️"
      }
    ],
    tips: [
      {
        title: "Conservation optimale",
        description: "Conserver dans un endroit frais et sec",
        icon: "❄️",
        type: 'storage'
      }
    ],
    healthBenefits: [
      {
        title: "Riche en nutriments",
        description: "Source naturelle de vitamines et minéraux",
        icon: "💪",
        category: 'nutrition'
      }
    ]
  };

  // Enrichissement spécifique par catégorie et produit
  if (category === 'Fruits') {
    if (productName.toLowerCase().includes('mangue')) {
      return {
        recipes: [
          {
            name: "Tarte à la mangue",
            description: "Délicieuse tarte tropicale avec mangues fraîches et crème pâtissière",
            difficulty: 'Moyen',
            time: "1h 30min",
            icon: "🥧"
          },
          {
            name: "Smoothie mangue-gingembre",
            description: "Boisson rafraîchissante parfaite pour les chaudes journées",
            difficulty: 'Facile',
            time: "10 min",
            icon: "🥤"
          },
          {
            name: "Salade de fruits exotiques",
            description: "Mélange tropical avec mangue, papaye et ananas",
            difficulty: 'Facile',
            time: "15 min",
            icon: "🥗"
          }
        ],
        tips: [
          {
            title: "Choisir une mangue mûre",
            description: "Pressez délicatement, elle doit céder légèrement sous la pression",
            icon: "👆",
            type: 'preparation'
          },
          {
            title: "Conservation",
            description: "Mangue mûre : 2-3 jours au frigo. Pas mûre : température ambiante",
            icon: "🌡️",
            type: 'storage'
          },
          {
            title: "Préparation",
            description: "Coupez en dés autour du noyau pour éviter le gaspillage",
            icon: "🔪",
            type: 'preparation'
          }
        ],
        healthBenefits: [
          {
            title: "Riche en vitamine C",
            description: "Une mangue couvre 100% des besoins quotidiens en vitamine C",
            icon: "🍊",
            category: 'nutrition'
          },
          {
            title: "Antioxydants puissants",
            description: "Bêta-carotène et polyphénols pour protéger les cellules",
            icon: "🛡️",
            category: 'health'
          },
          {
            title: "Digestion facilitée",
            description: "Enzymes naturelles qui aident à la digestion des protéines",
            icon: "💚",
            category: 'wellness'
          }
        ]
      };
    }

    if (productName.toLowerCase().includes('avocat')) {
      return {
        recipes: [
          {
            name: "Guacamole sénégalais",
            description: "Avocat écrasé avec tomates, oignons et piment local",
            difficulty: 'Facile',
            time: "15 min",
            icon: "🥑"
          },
          {
            name: "Salade d'avocat au thiof",
            description: "Salade fraîche avec avocat et poisson grillé",
            difficulty: 'Moyen',
            time: "25 min",
            icon: "🐟"
          }
        ],
        tips: [
          {
            title: "Test de maturité",
            description: "L'avocat doit céder sous une légère pression près de la tige",
            icon: "✋",
            type: 'preparation'
          },
          {
            title: "Éviter l'oxydation",
            description: "Ajoutez du citron pour éviter que l'avocat noircisse",
            icon: "🍋",
            type: 'preparation'
          }
        ],
        healthBenefits: [
          {
            title: "Bonnes graisses",
            description: "Riche en acides gras mono-insaturés bons pour le cœur",
            icon: "❤️",
            category: 'health'
          },
          {
            title: "Potassium",
            description: "Plus de potassium qu'une banane, bon pour la tension",
            icon: "⚡",
            category: 'nutrition'
          }
        ]
      };
    }
  }

  if (category === 'Poissons') {
    if (productName.toLowerCase().includes('tilapia') || productName.toLowerCase().includes('poisson')) {
      return {
        recipes: [
          {
            name: "Tilapia braisé",
            description: "Poisson grillé avec sauce tomate et légumes locaux",
            difficulty: 'Moyen',
            time: "45 min",
            icon: "🔥"
          },
          {
            name: "Soupe de poisson",
            description: "Bouillon riche avec légumes et épices sénégalaises",
            difficulty: 'Moyen',
            time: "1h",
            icon: "🍲"
          },
          {
            name: "Poisson yassa",
            description: "Tilapia mariné aux oignons et citron, spécialité casamançaise",
            difficulty: 'Facile',
            time: "40 min",
            icon: "🧅"
          }
        ],
        tips: [
          {
            title: "Fraîcheur du poisson",
            description: "Yeux clairs, branchies rouges, chair ferme au toucher",
            icon: "👁️",
            type: 'preparation'
          },
          {
            title: "Marinade",
            description: "Mariner 30 min minimum avec citron et épices pour plus de saveur",
            icon: "⏰",
            type: 'cooking'
          },
          {
            title: "Conservation",
            description: "Consommer dans les 24h, conserver au frais avec glace",
            icon: "🧊",
            type: 'storage'
          }
        ],
        healthBenefits: [
          {
            title: "Protéines complètes",
            description: "Tous les acides aminés essentiels pour les muscles",
            icon: "💪",
            category: 'nutrition'
          },
          {
            title: "Oméga-3",
            description: "Acides gras essentiels pour le cerveau et le cœur",
            icon: "🧠",
            category: 'health'
          },
          {
            title: "Faible en calories",
            description: "Source de protéines maigres, idéal pour la ligne",
            icon: "⚖️",
            category: 'wellness'
          }
        ]
      };
    }
  }

  if (category === 'Artisanat') {
    if (productName.toLowerCase().includes('miel')) {
      return {
        recipes: [
          {
            name: "Thé au miel et gingembre",
            description: "Boisson chaude réconfortante aux vertus apaisantes",
            difficulty: 'Facile',
            time: "10 min",
            icon: "🍵"
          },
          {
            name: "Pain d'épices au miel",
            description: "Gâteau moelleux parfumé au miel et épices locales",
            difficulty: 'Moyen',
            time: "1h 15min",
            icon: "🍰"
          }
        ],
        tips: [
          {
            title: "Conservation du miel",
            description: "Le miel ne périme jamais ! Conserver à température ambiante",
            icon: "🏺",
            type: 'storage'
          },
          {
            title: "Cristallisation normale",
            description: "Si le miel cristallise, chauffez doucement au bain-marie",
            icon: "🔥",
            type: 'preparation'
          }
        ],
        healthBenefits: [
          {
            title: "Antibactérien naturel",
            description: "Propriétés antiseptiques et cicatrisantes reconnues",
            icon: "🛡️",
            category: 'health'
          },
          {
            title: "Énergie naturelle",
            description: "Sucres naturels pour un boost d'énergie immédiat",
            icon: "⚡",
            category: 'wellness'
          }
        ]
      };
    }

    if (productName.toLowerCase().includes('bissap')) {
      return {
        recipes: [
          {
            name: "Bissap glacé à la menthe",
            description: "Boisson rafraîchissante traditionnelle avec menthe fraîche",
            difficulty: 'Facile',
            time: "20 min",
            icon: "🧊"
          },
          {
            name: "Cocktail bissap-gingembre",
            description: "Mélange pétillant et épicé pour les occasions spéciales",
            difficulty: 'Facile',
            time: "15 min",
            icon: "🍹"
          }
        ],
        tips: [
          {
            title: "Infusion parfaite",
            description: "Laisser infuser 15-20 min pour extraire toute la saveur",
            icon: "⏱️",
            type: 'preparation'
          },
          {
            title: "Conservation",
            description: "Se conserve 3-4 jours au réfrigérateur une fois préparé",
            icon: "❄️",
            type: 'storage'
          }
        ],
        healthBenefits: [
          {
            title: "Riche en antioxydants",
            description: "Anthocyanes qui protègent contre le vieillissement",
            icon: "🌺",
            category: 'health'
          },
          {
            title: "Hydratation naturelle",
            description: "Excellente alternative aux sodas, sans sucre ajouté",
            icon: "💧",
            category: 'wellness'
          }
        ]
      };
    }

    if (productName.toLowerCase().includes('confiture')) {
      return {
        recipes: [
          {
            name: "Tartines gourmandes",
            description: "Pain grillé avec confiture de mangue et fromage frais",
            difficulty: 'Facile',
            time: "5 min",
            icon: "🍞"
          },
          {
            name: "Yaourt à la confiture",
            description: "Yaourt nature mélangé avec confiture pour un dessert sain",
            difficulty: 'Facile',
            time: "2 min",
            icon: "🥛"
          },
          {
            name: "Gâteau marbré",
            description: "Gâteau moelleux avec des tourbillons de confiture",
            difficulty: 'Moyen',
            time: "1h",
            icon: "🍰"
          }
        ],
        tips: [
          {
            title: "Conservation optimale",
            description: "Conserver au réfrigérateur après ouverture, consommer dans les 3 semaines",
            icon: "❄️",
            type: 'storage'
          },
          {
            title: "Utilisation en pâtisserie",
            description: "Parfaite pour garnir tartes, gâteaux et viennoiseries",
            icon: "🎂",
            type: 'cooking'
          }
        ],
        healthBenefits: [
          {
            title: "Vitamines naturelles",
            description: "Conserve une partie des vitamines des fruits frais",
            icon: "🍊",
            category: 'nutrition'
          },
          {
            title: "Énergie rapide",
            description: "Sucres naturels pour un boost d'énergie matinal",
            icon: "⚡",
            category: 'wellness'
          }
        ]
      };
    }

    if (productName.toLowerCase().includes('savon')) {
      return {
        recipes: [
          {
            name: "Masque hydratant",
            description: "Utiliser la mousse comme masque hydratant pour le visage",
            difficulty: 'Facile',
            time: "10 min",
            icon: "🧴"
          },
          {
            name: "Bain relaxant",
            description: "Ajouter des copeaux de savon dans le bain pour un moment détente",
            difficulty: 'Facile',
            time: "20 min",
            icon: "🛁"
          }
        ],
        tips: [
          {
            title: "Conservation du savon",
            description: "Laisser sécher entre les utilisations sur un porte-savon aéré",
            icon: "🌬️",
            type: 'storage'
          },
          {
            title: "Utilisation optimale",
            description: "Faire mousser dans les mains avant application pour plus d'efficacité",
            icon: "🤲",
            type: 'preparation'
          },
          {
            title: "Peau sensible",
            description: "Tester sur une petite zone avant la première utilisation",
            icon: "⚠️",
            type: 'preparation'
          }
        ],
        healthBenefits: [
          {
            title: "Hydratation naturelle",
            description: "Le karité nourrit et protège la peau naturellement",
            icon: "💧",
            category: 'wellness'
          },
          {
            title: "Sans produits chimiques",
            description: "Formulation naturelle sans parabènes ni sulfates",
            icon: "🌿",
            category: 'health'
          },
          {
            title: "Anti-âge naturel",
            description: "Propriétés antioxydantes du karité pour une peau jeune",
            icon: "✨",
            category: 'wellness'
          }
        ]
      };
    }

    if (productName.toLowerCase().includes('panier') || productName.toLowerCase().includes('raphia')) {
      return {
        recipes: [
          {
            name: "Panier de courses écologique",
            description: "Alternative durable aux sacs plastiques pour vos achats",
            difficulty: 'Facile',
            time: "Usage quotidien",
            icon: "🛒"
          },
          {
            name: "Décoration d'intérieur",
            description: "Utiliser comme cache-pot ou rangement décoratif",
            difficulty: 'Facile',
            time: "Instantané",
            icon: "🏠"
          },
          {
            name: "Panier pique-nique",
            description: "Parfait pour transporter vos repas en plein air",
            difficulty: 'Facile',
            time: "Selon besoin",
            icon: "🧺"
          }
        ],
        tips: [
          {
            title: "Entretien du raphia",
            description: "Nettoyer avec un chiffon humide, éviter l'eau stagnante",
            icon: "🧽",
            type: 'storage'
          },
          {
            title: "Durabilité",
            description: "Éviter les charges trop lourdes pour préserver le tressage",
            icon: "⚖️",
            type: 'preparation'
          },
          {
            title: "Séchage",
            description: "Laisser sécher à l'air libre si humide, éviter le soleil direct",
            icon: "🌤️",
            type: 'storage'
          }
        ],
        healthBenefits: [
          {
            title: "Écologique",
            description: "Matériau 100% naturel et biodégradable",
            icon: "🌍",
            category: 'wellness'
          },
          {
            title: "Artisanat local",
            description: "Soutient l'économie locale et les savoir-faire traditionnels",
            icon: "🤝",
            category: 'wellness'
          },
          {
            title: "Durable",
            description: "Alternative écologique aux sacs plastiques",
            icon: "♻️",
            category: 'health'
          }
        ]
      };
    }

    if (productName.toLowerCase().includes('gingembre')) {
      return {
        recipes: [
          {
            name: "Thé au gingembre",
            description: "Diluer avec de l'eau chaude et ajouter du miel",
            difficulty: 'Facile',
            time: "5 min",
            icon: "🍵"
          },
          {
            name: "Cocktail énergisant",
            description: "Mélanger avec du citron vert et de l'eau pétillante",
            difficulty: 'Facile',
            time: "3 min",
            icon: "🍹"
          },
          {
            name: "Marinade épicée",
            description: "Utiliser comme base pour mariner viandes et poissons",
            difficulty: 'Moyen',
            time: "30 min",
            icon: "🥩"
          }
        ],
        tips: [
          {
            title: "Conservation",
            description: "Se conserve 5 jours au réfrigérateur une fois ouvert",
            icon: "❄️",
            type: 'storage'
          },
          {
            title: "Dosage",
            description: "Commencer par de petites quantités, le gingembre est puissant",
            icon: "🥄",
            type: 'preparation'
          },
          {
            title: "Moment idéal",
            description: "Boire le matin pour un boost d'énergie naturel",
            icon: "🌅",
            type: 'preparation'
          }
        ],
        healthBenefits: [
          {
            title: "Anti-inflammatoire",
            description: "Propriétés anti-inflammatoires naturelles reconnues",
            icon: "🔥",
            category: 'health'
          },
          {
            title: "Digestion",
            description: "Facilite la digestion et soulage les nausées",
            icon: "💚",
            category: 'wellness'
          },
          {
            title: "Immunité",
            description: "Renforce le système immunitaire naturellement",
            icon: "🛡️",
            category: 'health'
          }
        ]
      };
    }

    // Enrichissement spécifique pour les légumes sénégalais
    if (productName.toLowerCase().includes('oignon')) {
      return {
        recipes: [
          {
            name: "Thieboudienne aux oignons",
            description: "Riz au poisson traditionnel avec oignons violets de Galmi",
            difficulty: 'Moyen',
            time: "1h30",
            icon: "🍚"
          },
          {
            name: "Yassa au poulet",
            description: "Poulet mariné aux oignons, citron et moutarde",
            difficulty: 'Moyen',
            time: "1h",
            icon: "🐔"
          },
          {
            name: "Salade d'oignons",
            description: "Salade fraîche aux oignons violets et tomates",
            difficulty: 'Facile',
            time: "15 min",
            icon: "🥗"
          }
        ],
        tips: [
          {
            title: "Conservation des oignons",
            description: "Conserver dans un endroit sec et aéré, éviter le réfrigérateur",
            icon: "🏠",
            type: 'storage'
          },
          {
            title: "Réduire les larmes",
            description: "Refroidir l'oignon 30 min avant de le couper ou utiliser un couteau très tranchant",
            icon: "🔪",
            type: 'preparation'
          },
          {
            title: "Cuisson parfaite",
            description: "Cuire à feu doux pour développer la douceur naturelle",
            icon: "🔥",
            type: 'cooking'
          }
        ],
        healthBenefits: [
          {
            title: "Antioxydants puissants",
            description: "Riches en quercétine, un antioxydant anti-inflammatoire",
            icon: "🛡️",
            category: 'health'
          },
          {
            title: "Santé cardiovasculaire",
            description: "Aide à réduire le cholestérol et la pression artérielle",
            icon: "❤️",
            category: 'health'
          },
          {
            title: "Digestion",
            description: "Stimule la digestion et possède des propriétés antibactériennes",
            icon: "💚",
            category: 'wellness'
          }
        ]
      };
    }

    if (productName.toLowerCase().includes('piment')) {
      return {
        recipes: [
          {
            name: "Sauce pimentée maison",
            description: "Sauce traditionnelle sénégalaise aux piments Scotch Bonnet",
            difficulty: 'Facile',
            time: "20 min",
            icon: "🌶️"
          },
          {
            name: "Dibi épicé",
            description: "Viande grillée marinée aux piments et épices",
            difficulty: 'Moyen',
            time: "45 min",
            icon: "🥩"
          },
          {
            name: "Attiéké pimenté",
            description: "Semoule de manioc relevée aux piments frais",
            difficulty: 'Facile',
            time: "15 min",
            icon: "🍽️"
          }
        ],
        tips: [
          {
            title: "Manipulation sécurisée",
            description: "Porter des gants et éviter de toucher les yeux après manipulation",
            icon: "🧤",
            type: 'preparation'
          },
          {
            title: "Dosage progressif",
            description: "Commencer par de petites quantités, les Scotch Bonnet sont très forts",
            icon: "⚠️",
            type: 'preparation'
          },
          {
            title: "Conservation",
            description: "Se conservent 1 semaine au réfrigérateur, congélation possible",
            icon: "❄️",
            type: 'storage'
          }
        ],
        healthBenefits: [
          {
            title: "Capsaïcine",
            description: "Stimule le métabolisme et possède des propriétés anti-inflammatoires",
            icon: "🔥",
            category: 'health'
          },
          {
            title: "Vitamine C",
            description: "Très riche en vitamine C, plus que les agrumes",
            icon: "🍊",
            category: 'nutrition'
          },
          {
            title: "Endorphines",
            description: "Stimule la production d'endorphines, améliore l'humeur",
            icon: "😊",
            category: 'wellness'
          }
        ]
      };
    }

    if (productName.toLowerCase().includes('chou')) {
      return {
        recipes: [
          {
            name: "Chou braisé sénégalais",
            description: "Chou mijoté avec tomates, oignons et épices locales",
            difficulty: 'Facile',
            time: "30 min",
            icon: "🥬"
          },
          {
            name: "Salade de chou cru",
            description: "Salade fraîche et croquante avec vinaigrette au citron",
            difficulty: 'Facile',
            time: "10 min",
            icon: "🥗"
          },
          {
            name: "Soupe de chou",
            description: "Soupe nutritive aux légumes et chou des Niayes",
            difficulty: 'Facile',
            time: "25 min",
            icon: "🍲"
          }
        ],
        tips: [
          {
            title: "Préparation du chou",
            description: "Retirer les feuilles extérieures et laver soigneusement",
            icon: "🚿",
            type: 'preparation'
          },
          {
            title: "Cuisson optimale",
            description: "Ne pas trop cuire pour préserver les vitamines et le croquant",
            icon: "⏰",
            type: 'cooking'
          },
          {
            title: "Conservation",
            description: "Se conserve 1 semaine au réfrigérateur dans un sac perforé",
            icon: "❄️",
            type: 'storage'
          }
        ],
        healthBenefits: [
          {
            title: "Vitamines K et C",
            description: "Excellente source de vitamines K et C pour l'immunité",
            icon: "💪",
            category: 'nutrition'
          },
          {
            title: "Fibres digestives",
            description: "Riche en fibres, favorise une bonne digestion",
            icon: "🌿",
            category: 'wellness'
          },
          {
            title: "Antioxydants",
            description: "Contient des composés soufrés aux propriétés anticancer",
            icon: "🛡️",
            category: 'health'
          }
        ]
      };
    }

    if (productName.toLowerCase().includes('carotte')) {
      return {
        recipes: [
          {
            name: "Carottes à la sénégalaise",
            description: "Carottes sautées aux oignons et épices du terroir",
            difficulty: 'Facile',
            time: "20 min",
            icon: "🥕"
          },
          {
            name: "Jus de carotte frais",
            description: "Jus vitaminé aux carottes de Saint-Louis",
            difficulty: 'Facile',
            time: "10 min",
            icon: "🥤"
          },
          {
            name: "Salade de carottes râpées",
            description: "Salade croquante avec vinaigrette au citron vert",
            difficulty: 'Facile',
            time: "15 min",
            icon: "🥗"
          }
        ],
        tips: [
          {
            title: "Préparation",
            description: "Gratter ou éplucher finement, les jeunes carottes n'ont pas besoin d'épluchage",
            icon: "🔪",
            type: 'preparation'
          },
          {
            title: "Cuisson vapeur",
            description: "Cuire à la vapeur pour préserver les vitamines",
            icon: "💨",
            type: 'cooking'
          },
          {
            title: "Conservation",
            description: "Retirer les fanes et conserver au réfrigérateur jusqu'à 2 semaines",
            icon: "❄️",
            type: 'storage'
          }
        ],
        healthBenefits: [
          {
            title: "Bêta-carotène",
            description: "Très riche en bêta-carotène, précurseur de la vitamine A",
            icon: "👁️",
            category: 'nutrition'
          },
          {
            title: "Vision",
            description: "Excellente pour la santé des yeux et la vision nocturne",
            icon: "🌙",
            category: 'health'
          },
          {
            title: "Peau saine",
            description: "Favorise une peau saine et protège du vieillissement",
            icon: "✨",
            category: 'wellness'
          }
        ]
      };
    }

    if (productName.toLowerCase().includes('gombo')) {
      return {
        recipes: [
          {
            name: "Gombo à l'huile de palme",
            description: "Plat traditionnel sénégalais au gombo et huile rouge",
            difficulty: 'Moyen',
            time: "45 min",
            icon: "🍲"
          },
          {
            name: "Soupe de gombo",
            description: "Soupe épaisse et nutritive aux gombos frais",
            difficulty: 'Facile',
            time: "30 min",
            icon: "🥣"
          },
          {
            name: "Gombo grillé",
            description: "Gombos grillés aux épices et citron vert",
            difficulty: 'Facile',
            time: "15 min",
            icon: "🔥"
          }
        ],
        tips: [
          {
            title: "Réduire le mucilage",
            description: "Faire tremper dans du vinaigre 30 min ou griller légèrement avant cuisson",
            icon: "💧",
            type: 'preparation'
          },
          {
            title: "Cuisson rapide",
            description: "Cuire rapidement pour éviter qu'ils deviennent trop visqueux",
            icon: "⚡",
            type: 'cooking'
          },
          {
            title: "Fraîcheur",
            description: "Choisir des gombos fermes et verts, sans taches brunes",
            icon: "🌿",
            type: 'preparation'
          }
        ],
        healthBenefits: [
          {
            title: "Fibres solubles",
            description: "Riche en fibres solubles, aide à réguler le cholestérol",
            icon: "💚",
            category: 'health'
          },
          {
            title: "Vitamines B",
            description: "Excellente source de folates et vitamines B",
            icon: "🧠",
            category: 'nutrition'
          },
          {
            title: "Antioxydants",
            description: "Contient des antioxydants qui protègent les cellules",
            icon: "🛡️",
            category: 'wellness'
          }
        ]
      };
    }

    if (productName.toLowerCase().includes('épinard')) {
      return {
        recipes: [
          {
            name: "Épinards à la sénégalaise",
            description: "Épinards sautés à l'ail et aux épices locales",
            difficulty: 'Facile',
            time: "15 min",
            icon: "🥬"
          },
          {
            name: "Smoothie vert énergisant",
            description: "Smoothie aux épinards, mangue et gingembre",
            difficulty: 'Facile',
            time: "5 min",
            icon: "🥤"
          },
          {
            name: "Omelette aux épinards",
            description: "Omelette nutritive aux épinards frais et fromage",
            difficulty: 'Facile',
            time: "10 min",
            icon: "🍳"
          }
        ],
        tips: [
          {
            title: "Lavage minutieux",
            description: "Laver plusieurs fois à l'eau froide pour éliminer le sable",
            icon: "🚿",
            type: 'preparation'
          },
          {
            title: "Cuisson rapide",
            description: "Cuire rapidement à feu vif pour préserver les vitamines",
            icon: "⚡",
            type: 'cooking'
          },
          {
            title: "Consommation rapide",
            description: "Consommer dans les 2-3 jours, très périssable",
            icon: "⏰",
            type: 'storage'
          }
        ],
        healthBenefits: [
          {
            title: "Fer et folates",
            description: "Très riche en fer et folates, excellent contre l'anémie",
            icon: "🩸",
            category: 'health'
          },
          {
            title: "Vitamines A et K",
            description: "Source importante de vitamines A et K",
            icon: "💪",
            category: 'nutrition'
          },
          {
            title: "Nitrates naturels",
            description: "Contient des nitrates qui améliorent les performances sportives",
            icon: "🏃",
            category: 'wellness'
          }
        ]
      };
    }

    // Enrichissement spécifique pour les fruits tropicaux sénégalais
    if (productName.toLowerCase().includes('orange')) {
      return {
        recipes: [
          {
            name: "Jus d'orange frais",
            description: "Jus d'orange pressé à froid, riche en vitamine C",
            difficulty: 'Facile',
            time: "10 min",
            icon: "🥤"
          },
          {
            name: "Salade de fruits tropicaux",
            description: "Mélange d'oranges, mangues et ananas avec menthe fraîche",
            difficulty: 'Facile',
            time: "15 min",
            icon: "🥗"
          },
          {
            name: "Confiture d'oranges",
            description: "Confiture maison aux oranges de Valencia",
            difficulty: 'Moyen',
            time: "45 min",
            icon: "🍯"
          }
        ],
        tips: [
          {
            title: "Choisir les meilleures oranges",
            description: "Privilégier les oranges lourdes et fermes, sans taches molles",
            icon: "👁️",
            type: 'preparation'
          },
          {
            title: "Conservation optimale",
            description: "Se conservent 1 semaine à température ambiante, 3 semaines au frais",
            icon: "❄️",
            type: 'storage'
          },
          {
            title: "Extraction du jus",
            description: "Rouler l'orange avant de presser pour extraire plus de jus",
            icon: "🤲",
            type: 'preparation'
          }
        ],
        healthBenefits: [
          {
            title: "Vitamine C",
            description: "Une orange couvre 100% des besoins quotidiens en vitamine C",
            icon: "🍊",
            category: 'nutrition'
          },
          {
            title: "Fibres digestives",
            description: "Riches en fibres solubles, favorisent une bonne digestion",
            icon: "🌿",
            category: 'wellness'
          },
          {
            title: "Antioxydants",
            description: "Flavonoïdes qui protègent contre les maladies cardiovasculaires",
            icon: "❤️",
            category: 'health'
          }
        ]
      };
    }

    if (productName.toLowerCase().includes('papaye')) {
      return {
        recipes: [
          {
            name: "Salade de papaye verte",
            description: "Salade traditionnelle aux papayes vertes, tomates et piments",
            difficulty: 'Moyen',
            time: "20 min",
            icon: "🥗"
          },
          {
            name: "Smoothie papaye-mangue",
            description: "Smoothie tropical rafraîchissant aux fruits locaux",
            difficulty: 'Facile',
            time: "5 min",
            icon: "🥤"
          },
          {
            name: "Papaye au citron vert",
            description: "Papaye mûre arrosée de citron vert et piment",
            difficulty: 'Facile',
            time: "5 min",
            icon: "🍋"
          }
        ],
        tips: [
          {
            title: "Test de maturité",
            description: "La papaye est mûre quand elle cède légèrement sous la pression",
            icon: "🤏",
            type: 'preparation'
          },
          {
            title: "Accélérer la maturation",
            description: "Placer dans un sac papier avec une banane pour accélérer",
            icon: "📦",
            type: 'storage'
          },
          {
            title: "Préparation",
            description: "Retirer les graines noires au centre avant consommation",
            icon: "🔪",
            type: 'preparation'
          }
        ],
        healthBenefits: [
          {
            title: "Enzymes digestives",
            description: "Papaïne aide à digérer les protéines naturellement",
            icon: "💚",
            category: 'wellness'
          },
          {
            title: "Vitamines A et C",
            description: "Excellente source de vitamines A et C pour l'immunité",
            icon: "🛡️",
            category: 'nutrition'
          },
          {
            title: "Anti-inflammatoire",
            description: "Propriétés anti-inflammatoires naturelles reconnues",
            icon: "🔥",
            category: 'health'
          }
        ]
      };
    }

    if (productName.toLowerCase().includes('citron')) {
      return {
        recipes: [
          {
            name: "Citronnade maison",
            description: "Boisson rafraîchissante aux citrons verts et menthe",
            difficulty: 'Facile',
            time: "10 min",
            icon: "🥤"
          },
          {
            name: "Marinade au citron",
            description: "Marinade pour poissons et viandes aux agrumes",
            difficulty: 'Facile',
            time: "15 min",
            icon: "🐟"
          },
          {
            name: "Vinaigrette citronnée",
            description: "Vinaigrette légère pour salades et crudités",
            difficulty: 'Facile',
            time: "5 min",
            icon: "🥗"
          }
        ],
        tips: [
          {
            title: "Extraction maximale",
            description: "Réchauffer légèrement ou rouler avant de presser",
            icon: "🤲",
            type: 'preparation'
          },
          {
            title: "Conservation du jus",
            description: "Le jus se conserve 3 jours au réfrigérateur",
            icon: "❄️",
            type: 'storage'
          },
          {
            title: "Utilisation du zeste",
            description: "Le zeste parfume délicieusement pâtisseries et plats",
            icon: "✨",
            type: 'cooking'
          }
        ],
        healthBenefits: [
          {
            title: "Vitamine C concentrée",
            description: "Plus de vitamine C que les oranges, boost immunitaire",
            icon: "💪",
            category: 'nutrition'
          },
          {
            title: "Détoxification",
            description: "Aide à purifier l'organisme et stimule le foie",
            icon: "🌿",
            category: 'wellness'
          },
          {
            title: "Absorption du fer",
            description: "Améliore l'absorption du fer des autres aliments",
            icon: "🩸",
            category: 'health'
          }
        ]
      };
    }

    if (productName.toLowerCase().includes('pastèque')) {
      return {
        recipes: [
          {
            name: "Salade de pastèque à la feta",
            description: "Salade fraîche pastèque, feta et menthe",
            difficulty: 'Facile',
            time: "10 min",
            icon: "🥗"
          },
          {
            name: "Agua fresca de pastèque",
            description: "Boisson mexicaine rafraîchissante à la pastèque",
            difficulty: 'Facile',
            time: "15 min",
            icon: "🥤"
          },
          {
            name: "Sorbet à la pastèque",
            description: "Sorbet maison sans sorbetière",
            difficulty: 'Moyen',
            time: "4h",
            icon: "🍧"
          }
        ],
        tips: [
          {
            title: "Test de maturité",
            description: "Son creux quand on tape dessus, tache jaune au sol",
            icon: "👂",
            type: 'preparation'
          },
          {
            title: "Conservation",
            description: "Entière : 1 semaine, coupée : 3-4 jours au frais",
            icon: "❄️",
            type: 'storage'
          },
          {
            title: "Découpe optimale",
            description: "Couper en tranches puis en cubes pour faciliter la dégustation",
            icon: "🔪",
            type: 'preparation'
          }
        ],
        healthBenefits: [
          {
            title: "Hydratation",
            description: "92% d'eau, excellente pour l'hydratation naturelle",
            icon: "💧",
            category: 'wellness'
          },
          {
            title: "Lycopène",
            description: "Antioxydant puissant qui protège la peau du soleil",
            icon: "☀️",
            category: 'health'
          },
          {
            title: "Faible en calories",
            description: "Très peu calorique, parfaite pour les régimes",
            icon: "⚖️",
            category: 'nutrition'
          }
        ]
      };
    }

    if (productName.toLowerCase().includes('goyave')) {
      return {
        recipes: [
          {
            name: "Jus de goyave maison",
            description: "Jus de goyave frais, riche en vitamines",
            difficulty: 'Facile',
            time: "15 min",
            icon: "🥤"
          },
          {
            name: "Confiture de goyaves",
            description: "Confiture traditionnelle aux goyaves roses",
            difficulty: 'Moyen',
            time: "1h",
            icon: "🍯"
          },
          {
            name: "Goyaves au sirop",
            description: "Dessert traditionnel aux goyaves pochées",
            difficulty: 'Moyen',
            time: "30 min",
            icon: "🍮"
          }
        ],
        tips: [
          {
            title: "Maturité parfaite",
            description: "Goyave mûre quand elle dégage un parfum intense",
            icon: "👃",
            type: 'preparation'
          },
          {
            title: "Préparation",
            description: "Retirer les graines dures au centre si désiré",
            icon: "🔪",
            type: 'preparation'
          },
          {
            title: "Conservation",
            description: "Se conserve 3-4 jours à température ambiante",
            icon: "🏠",
            type: 'storage'
          }
        ],
        healthBenefits: [
          {
            title: "Super vitamine C",
            description: "4 fois plus de vitamine C qu'une orange",
            icon: "🍊",
            category: 'nutrition'
          },
          {
            title: "Fibres abondantes",
            description: "Très riche en fibres, excellent pour le transit",
            icon: "🌿",
            category: 'wellness'
          },
          {
            title: "Antioxydants",
            description: "Lycopène et autres antioxydants protecteurs",
            icon: "🛡️",
            category: 'health'
          }
        ]
      };
    }

    if (productName.toLowerCase().includes('avocat')) {
      return {
        recipes: [
          {
            name: "Guacamole traditionnel",
            description: "Guacamole aux avocats Hass, citron vert et coriandre",
            difficulty: 'Facile',
            time: "10 min",
            icon: "🥑"
          },
          {
            name: "Toast à l'avocat",
            description: "Tartine healthy à l'avocat écrasé et graines",
            difficulty: 'Facile',
            time: "5 min",
            icon: "🍞"
          },
          {
            name: "Smoothie avocat-banane",
            description: "Smoothie crémeux et nutritif pour le petit-déjeuner",
            difficulty: 'Facile',
            time: "5 min",
            icon: "🥤"
          }
        ],
        tips: [
          {
            title: "Test de maturité",
            description: "Avocat mûr quand il cède légèrement sous la pression",
            icon: "🤏",
            type: 'preparation'
          },
          {
            title: "Éviter l'oxydation",
            description: "Arroser de citron pour éviter que la chair noircisse",
            icon: "🍋",
            type: 'preparation'
          },
          {
            title: "Accélérer la maturation",
            description: "Placer avec une banane dans un sac papier",
            icon: "📦",
            type: 'storage'
          }
        ],
        healthBenefits: [
          {
            title: "Bonnes graisses",
            description: "Riche en acides gras mono-insaturés bénéfiques",
            icon: "💚",
            category: 'nutrition'
          },
          {
            title: "Potassium",
            description: "Plus de potassium qu'une banane, bon pour le cœur",
            icon: "❤️",
            category: 'health'
          },
          {
            title: "Absorption vitamines",
            description: "Améliore l'absorption des vitamines liposolubles",
            icon: "🌟",
            category: 'wellness'
          }
        ]
      };
    }
  }

  return defaultEnrichment;
};

export default function ProductDetailPage() {
  const t = useTranslations("product");
  const params = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const { addItem } = useCart();
  const [product, setProduct] = useState<Product | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [quantity, setQuantity] = useState(1);
  const [activeImageIndex, setActiveImageIndex] = useState(0);
  const [isFavorite, setIsFavorite] = useState(false);
  const [showAnimation, setShowAnimation] = useState(false);
  const [relatedProducts, setRelatedProducts] = useState<Product[]>([]);
  const [activeTab, setActiveTab] = useState('description');
  const [productEnrichment, setProductEnrichment] = useState<ProductEnrichment | null>(null);
  const cartIconRef = useRef<HTMLDivElement>(null);

  // Images de démonstration (à remplacer par les vraies images du produit)
  const productImages = [
    product?.image_url || "/images/placeholder.jpg",
    "/images/placeholder.jpg",
    "/images/placeholder.jpg",
  ];

  // Données produits locales (même structure que dans la page produits)
  const localProducts = [
    {
      id: '1',
      name: 'Mangues Kent Bio 1kg',
      description: 'Mangues biologiques fraîches de la région de Casamance, cultivées sans pesticides',
      price: 2500,
      stock_quantity: 15,
      category: 'Fruits',
      image_url: 'https://images.unsplash.com/photo-1553279768-865429fa0078?w=400',
      created_at: new Date().toISOString(),
      seller_id: 'seller1',
      seller: {
        id: 'seller1',
        name: 'Ferme Bio Casamance',
        avatar_url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100',
        seller_profiles: [{
          rating: 4.8,
          review_count: 127,
          address: 'Casamance, Sénégal'
        }]
      },
      is_organic: true,
      is_local: true,
      unit: 'kg',
      discount: 10,
      rating: 4.8,
      review_count: 42
    },
    {
      id: '2',
      name: 'Tilapia Frais 1kg',
      description: 'Tilapia frais pêché dans les eaux locales, qualité premium',
      price: 3500,
      stock_quantity: 8,
      category: 'Poissons',
      image_url: 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=400',
      created_at: new Date().toISOString(),
      seller_id: 'seller2',
      seller: {
        id: 'seller2',
        name: 'Pêcheurs de Joal',
        avatar_url: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100',
        seller_profiles: [{
          rating: 4.6,
          review_count: 89,
          address: 'Joal-Fadiouth, Sénégal'
        }]
      },
      is_organic: false,
      is_local: true,
      unit: 'kg',
      rating: 4.6,
      review_count: 28
    },
    {
      id: '11',
      name: 'Miel Pur Local 500g',
      description: 'Miel pur et naturel récolté dans les ruches traditionnelles de Casamance',
      price: 4500,
      stock_quantity: 12,
      category: 'Artisanat',
      image_url: 'https://images.unsplash.com/photo-1587049352846-4a222e784d38?w=400',
      created_at: new Date().toISOString(),
      seller_id: 'seller3',
      seller: {
        id: 'seller3',
        name: 'Apiculteurs de Casamance',
        avatar_url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100',
        seller_profiles: [{
          rating: 4.9,
          review_count: 156,
          address: 'Casamance, Sénégal'
        }]
      },
      is_organic: true,
      is_local: true,
      unit: 'pot',
      rating: 4.9,
      review_count: 67
    },
    {
      id: '30',
      name: 'Jus de Bissap Artisanal 1L',
      description: 'Jus de bissap artisanal, préparé selon la tradition sénégalaise avec des fleurs d\'hibiscus locales',
      price: 1500,
      stock_quantity: 20,
      category: 'Artisanat',
      image_url: 'https://images.unsplash.com/photo-1544145945-f90425340c7e?w=400',
      created_at: new Date().toISOString(),
      seller_id: 'seller4',
      seller: {
        id: 'seller4',
        name: 'Boissons Naturelles Dakar',
        avatar_url: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100',
        seller_profiles: [{
          rating: 4.7,
          review_count: 93,
          address: 'Dakar, Sénégal'
        }]
      },
      is_organic: true,
      is_local: true,
      unit: 'bouteille',
      rating: 4.7,
      review_count: 34
    },
    {
      id: '31',
      name: 'Confiture de Mangue Artisanale 250g',
      description: 'Confiture artisanale de mangues Kent, préparée avec des fruits locaux et du sucre de canne',
      price: 2800,
      stock_quantity: 15,
      category: 'Artisanat',
      image_url: 'https://images.unsplash.com/photo-1484723091739-30a097e8f929?w=400',
      created_at: new Date().toISOString(),
      seller_id: 'seller5',
      seller: {
        id: 'seller5',
        name: 'Confitures de Casamance',
        avatar_url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100',
        seller_profiles: [{
          rating: 4.8,
          review_count: 67,
          address: 'Casamance, Sénégal'
        }]
      },
      is_organic: true,
      is_local: true,
      unit: 'pot',
      rating: 4.8,
      review_count: 23
    },
    {
      id: '32',
      name: 'Savon Naturel au Karité 100g',
      description: 'Savon artisanal au beurre de karité pur, fabriqué selon les méthodes traditionnelles',
      price: 1200,
      stock_quantity: 25,
      category: 'Artisanat',
      image_url: 'https://images.unsplash.com/photo-1556228720-195a672e8a03?w=400',
      created_at: new Date().toISOString(),
      seller_id: 'seller6',
      seller: {
        id: 'seller6',
        name: 'Savonnerie Traditionnelle',
        avatar_url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100',
        seller_profiles: [{
          rating: 4.9,
          review_count: 134,
          address: 'Thiès, Sénégal'
        }]
      },
      is_organic: true,
      is_local: true,
      unit: 'pièce',
      rating: 4.9,
      review_count: 45
    },
    {
      id: '33',
      name: 'Panier Tressé en Raphia',
      description: 'Panier artisanal tressé à la main en raphia naturel, idéal pour les courses ou la décoration',
      price: 3500,
      stock_quantity: 8,
      category: 'Artisanat',
      image_url: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400',
      created_at: new Date().toISOString(),
      seller_id: 'seller7',
      seller: {
        id: 'seller7',
        name: 'Artisans de Kaolack',
        avatar_url: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100',
        seller_profiles: [{
          rating: 4.6,
          review_count: 89,
          address: 'Kaolack, Sénégal'
        }]
      },
      is_organic: false,
      is_local: true,
      unit: 'pièce',
      rating: 4.6,
      review_count: 31
    },
    {
      id: '34',
      name: 'Jus de Gingembre Frais 500ml',
      description: 'Jus de gingembre frais artisanal, énergisant et rafraîchissant, préparé quotidiennement',
      price: 1000,
      stock_quantity: 18,
      category: 'Artisanat',
      image_url: 'https://images.unsplash.com/photo-1570197788417-0e82375c9371?w=400',
      created_at: new Date().toISOString(),
      seller_id: 'seller4',
      seller: {
        id: 'seller4',
        name: 'Boissons Naturelles Dakar',
        avatar_url: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100',
        seller_profiles: [{
          rating: 4.7,
          review_count: 93,
          address: 'Dakar, Sénégal'
        }]
      },
      is_organic: true,
      is_local: true,
      unit: 'bouteille',
      rating: 4.5,
      review_count: 28
    }
  ];

  useEffect(() => {
    const fetchProduct = async () => {
      if (!params.id) return;

      setIsLoading(true);
      try {
        // Simuler un délai de chargement pour l'UX
        await new Promise(resolve => setTimeout(resolve, 500));

        // Trouver le produit dans les données locales
        const foundProduct = localProducts.find(p => p.id === params.id);

        if (!foundProduct) {
          throw new Error("Produit non trouvé");
        }

        setProduct(foundProduct);

        // Charger l'enrichissement du produit
        const enrichment = getProductEnrichment(foundProduct.name, foundProduct.category);
        setProductEnrichment(enrichment);

        // Simuler la vérification des favoris (toujours false pour la démo)
        setIsFavorite(false);

        // Récupérer les produits similaires (même catégorie)
        const relatedData = localProducts
          .filter(p => p.category === foundProduct.category && p.id !== foundProduct.id)
          .slice(0, 4);

        setRelatedProducts(relatedData);
      } catch (err) {
        console.error("Erreur lors du chargement du produit:", err);
        setError("Impossible de charger les détails du produit");
      } finally {
        setIsLoading(false);
      }
    };

    fetchProduct();
  }, [params.id]);

  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity >= 1 && newQuantity <= (product?.stock_quantity || 10)) {
      setQuantity(newQuantity);
    }
  };

  const handleAddToCart = () => {
    if (!product) return;

    addItem({
      product_id: product.id,
      seller_id: product.seller_id,
      quantity,
      price: product.price,
      name: product.name,
      image_url: product.image_url,
    });

    setShowAnimation(true);
  };

  const toggleFavorite = async () => {
    if (!product) return;

    try {
      // Simuler la mise à jour des favoris (pour la démo)
      setIsFavorite(!isFavorite);

      // Dans une vraie application, ici on ferait l'appel API
      console.log(`Produit ${product.name} ${!isFavorite ? 'ajouté aux' : 'retiré des'} favoris`);
    } catch (err) {
      console.error("Erreur lors de la mise à jour des favoris:", err);
    }
  };

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="aspect-square bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
            <div>
              <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-6"></div>
              <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-8"></div>
              <div className="h-24 bg-gray-200 dark:bg-gray-700 rounded mb-6"></div>
              <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-full mb-4"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            {error || "Produit non trouvé"}
          </h1>
          <Button onClick={() => router.back()}>
            <FaArrowLeft className="mr-2" /> Retour
          </Button>
        </div>
      </div>
    );
  }

  // Calculer le prix avec remise si applicable
  const discountedPrice = product.discount
    ? product.price - (product.price * product.discount / 100)
    : product.price;

  // Formater le prix
  const formattedPrice = new Intl.NumberFormat("fr-SN", {
    style: "currency",
    currency: "XOF",
    minimumFractionDigits: 0,
  }).format(discountedPrice);

  // Formater le prix original en cas de remise
  const formattedOriginalPrice = product.discount
    ? new Intl.NumberFormat("fr-SN", {
        style: "currency",
        currency: "XOF",
        minimumFractionDigits: 0,
      }).format(product.price)
    : null;

  return (
    <>
      {showAnimation && cartIconRef.current && (
        <AddToCartAnimation
          productImage={product.image_url || "/images/placeholder.jpg"}
          productName={product.name}
          targetRef={cartIconRef}
          onAnimationComplete={() => setShowAnimation(false)}
        />
      )}

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Fil d'Ariane */}
        <nav className="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-6">
          <Link href="/" className="hover:text-primary dark:hover:text-primary-400 transition-colors">
            Accueil
          </Link>
          <span className="mx-2">/</span>
          <Link href="/categories" className="hover:text-primary dark:hover:text-primary-400 transition-colors">
            Catégories
          </Link>
          <span className="mx-2">/</span>
          <Link
            href={`/categories/${encodeURIComponent(product.category)}`}
            className="hover:text-primary dark:hover:text-primary-400 transition-colors"
          >
            {product.category}
          </Link>
          <span className="mx-2">/</span>
          <span className="text-gray-700 dark:text-gray-300 font-medium truncate">
            {product.name}
          </span>
        </nav>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          {/* Galerie d'images */}
          <div>
            <div className="relative aspect-square bg-white dark:bg-gray-800 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700 shadow-md mb-4">
              <AnimatePresence mode="wait">
                <motion.div
                  key={activeImageIndex}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.3 }}
                  className="absolute inset-0"
                >
                  <Image
                    src={productImages[activeImageIndex]}
                    alt={product.name}
                    fill
                    className="object-contain"
                    sizes="(max-width: 768px) 100vw, 50vw"
                  />
                </motion.div>
              </AnimatePresence>

              {/* Badges */}
              <div className="absolute top-4 left-4 flex flex-col gap-2">
                {product.discount && (
                  <Badge variant="destructive" animation="pulse">-{product.discount}%</Badge>
                )}
                {product.is_organic && (
                  <Badge variant="accent">Bio</Badge>
                )}
                {product.is_local && (
                  <Badge variant="primary">Local</Badge>
                )}
                {product.stock_quantity <= 5 && product.stock_quantity > 0 && (
                  <Badge variant="warning">Stock limité</Badge>
                )}
                {product.stock_quantity <= 0 && (
                  <Badge variant="destructive">Rupture de stock</Badge>
                )}
              </div>

              {/* Boutons d'action */}
              <div className="absolute top-4 right-4 flex flex-col gap-2">
                <button
                  type="button"
                  onClick={toggleFavorite}
                  className="w-10 h-10 rounded-full bg-white dark:bg-gray-800 shadow-md flex items-center justify-center text-gray-700 dark:text-gray-300 hover:text-secondary transition-colors"
                  aria-label={isFavorite ? "Retirer des favoris" : "Ajouter aux favoris"}
                >
                  {isFavorite ? <FaHeart className="text-secondary" /> : <FaRegHeart />}
                </button>
                <button
                  type="button"
                  onClick={() => {}}
                  className="w-10 h-10 rounded-full bg-white dark:bg-gray-800 shadow-md flex items-center justify-center text-gray-700 dark:text-gray-300 hover:text-primary transition-colors"
                  aria-label="Partager"
                >
                  <FaShare />
                </button>
              </div>
            </div>

            {/* Miniatures */}
            <div className="flex gap-2 overflow-x-auto pb-2">
              {productImages.map((image, index) => (
                <button
                  type="button"
                  key={index}
                  onClick={() => setActiveImageIndex(index)}
                  className={`relative w-20 h-20 rounded-md overflow-hidden border-2 transition-all ${
                    activeImageIndex === index
                      ? "border-primary dark:border-primary-400 shadow-md"
                      : "border-gray-200 dark:border-gray-700"
                  }`}
                  title={`Voir l'image ${index + 1}`}
                  aria-label={`Voir l'image ${index + 1} du produit ${product.name}`}
                >
                  <Image
                    src={image}
                    alt={`${product.name} - image ${index + 1}`}
                    fill
                    className="object-cover"
                  />
                </button>
              ))}
            </div>
          </div>

          {/* Détails du produit */}
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white font-heading mb-2">
              {product.name}
            </h1>

            {/* Évaluations */}
            <div className="flex items-center mb-4">
              <div className="flex items-center text-yellow-500">
                <FaStar />
                <span className="ml-1 font-medium text-gray-700 dark:text-gray-300">
                  {product.rating || "4.5"}
                </span>
              </div>
              <span className="mx-2 text-gray-400">•</span>
              <span className="text-gray-600 dark:text-gray-400">
                {product.review_count || "42"} avis
              </span>
              <span className="mx-2 text-gray-400">•</span>
              <span className="text-gray-600 dark:text-gray-400">
                {product.stock_quantity > 0 ? `${product.stock_quantity} en stock` : "Rupture de stock"}
              </span>
            </div>

            {/* Prix */}
            <div className="flex items-baseline mb-6">
              <span className="text-3xl font-bold text-primary dark:text-primary-400 font-heading">
                {formattedPrice}
              </span>
              {formattedOriginalPrice && (
                <span className="ml-2 text-lg text-gray-500 dark:text-gray-400 line-through">
                  {formattedOriginalPrice}
                </span>
              )}
              <span className="ml-2 text-gray-600 dark:text-gray-400">
                / {product.unit || "kg"}
              </span>
            </div>

            {/* Description */}
            <div className="mb-6">
              <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                {product.description}
              </p>
            </div>

            {/* Sélecteur de quantité et bouton d'ajout au panier */}
            <div className="mb-8">
              <div className="flex items-center mb-4">
                <label htmlFor="quantity" className="text-gray-700 dark:text-gray-300 mr-4">Quantité:</label>
                <div className="flex items-center border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
                  <button
                    type="button"
                    onClick={() => handleQuantityChange(quantity - 1)}
                    disabled={quantity <= 1}
                    className="w-10 h-10 flex items-center justify-center text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 disabled:opacity-50"
                    aria-label="Diminuer la quantité"
                  >
                    -
                  </button>
                  <input
                    id="quantity"
                    type="number"
                    value={quantity}
                    onChange={(e) => handleQuantityChange(parseInt(e.target.value) || 1)}
                    className="w-12 h-10 text-center border-x border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300"
                    min="1"
                    max={product.stock_quantity || 10}
                    aria-label="Quantité"
                    title="Quantité"
                  />
                  <button
                    type="button"
                    onClick={() => handleQuantityChange(quantity + 1)}
                    disabled={quantity >= (product.stock_quantity || 10)}
                    className="w-10 h-10 flex items-center justify-center text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 disabled:opacity-50"
                    aria-label="Augmenter la quantité"
                  >
                    +
                  </button>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  variant="primary"
                  size="lg"
                  withShadow
                  withAnimation
                  onClick={handleAddToCart}
                  disabled={product.stock_quantity <= 0}
                  className="flex-1"
                >
                  <FaShoppingCart className="mr-2" />
                  Ajouter au panier
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  withAnimation
                  onClick={() => {}}
                  className="flex-1"
                >
                  <FaStore className="mr-2" />
                  Voir le vendeur
                </Button>
              </div>
            </div>

            {/* Informations sur le vendeur */}
            <Card className="mb-6">
              <CardContent className="p-4">
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-full overflow-hidden bg-gray-200 dark:bg-gray-700 mr-4">
                    {product.seller?.avatar_url ? (
                      <Image
                        src={product.seller.avatar_url}
                        alt={product.seller.name}
                        width={48}
                        height={48}
                        className="object-cover w-full h-full"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-primary-50 dark:bg-primary-900/30 text-primary dark:text-primary-400 text-lg font-bold">
                        {product.seller?.name?.charAt(0) || "V"}
                      </div>
                    )}
                  </div>
                  <div>
                    <h3 className="font-bold text-gray-900 dark:text-white">
                      {product.seller?.name || "Vendeur"}
                    </h3>
                    {product.seller?.seller_profiles?.[0]?.rating && (
                      <div className="flex items-center text-sm">
                        <div className="flex items-center text-yellow-500">
                          <FaStar className="h-4 w-4" />
                          <span className="ml-1 text-gray-700 dark:text-gray-300">
                            {product.seller.seller_profiles[0].rating.toFixed(1)}
                          </span>
                        </div>
                        <span className="mx-1 text-gray-400">•</span>
                        <span className="text-gray-600 dark:text-gray-400">
                          {product.seller.seller_profiles[0].review_count} avis
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="mt-4 flex flex-wrap gap-2">
                  {product.seller?.seller_profiles?.[0]?.address && (
                    <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                      <FaMapMarkerAlt className="mr-1 text-primary dark:text-primary-400" />
                      {product.seller.seller_profiles[0].address}
                    </div>
                  )}
                  <div className="flex items-center text-sm text-gray-600 dark:text-gray-400 ml-auto">
                    <Link
                      href={`/sellers/${product.seller_id}`}
                      className="text-primary dark:text-primary-400 hover:underline font-medium"
                    >
                      Voir le profil
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Référence pour l'animation d'ajout au panier */}
            <div ref={cartIconRef} className="hidden" />
          </div>
        </div>

        {/* Section enrichie "À découvrir avec ce produit" - Inspiré de TastyDaily */}
        <div className="mb-16">
          {/* Navigation des onglets */}
          <div className="flex flex-wrap gap-2 mb-6 border-b border-gray-200 dark:border-gray-700">
            {[
              { id: 'description', label: 'Description', icon: '📝' },
              { id: 'recipes', label: 'Recettes', icon: '🍴' },
              { id: 'tips', label: 'Conseils', icon: '💡' },
              { id: 'health', label: 'Bienfaits', icon: '🌿' },
              { id: 'reviews', label: 'Avis', icon: '⭐' },
              { id: 'shipping', label: 'Livraison', icon: '🚚' }
            ].map((tab) => (
              <button
                key={tab.id}
                type="button"
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 px-4 py-3 rounded-t-lg font-medium transition-all duration-300 ${
                  activeTab === tab.id
                    ? 'bg-white dark:bg-gray-800 text-primary dark:text-primary-400 border-b-2 border-primary dark:border-primary-400 shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-700/50'
                }`}
              >
                <span>{tab.icon}</span>
                <span>{tab.label}</span>
              </button>
            ))}
          </div>

          {/* Contenu des onglets */}
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700"
          >
            {/* Onglet Description */}
            {activeTab === 'description' && (
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white font-heading mb-4 flex items-center gap-3">
                  <span className="text-3xl">📝</span>
                  À propos de ce produit
                </h2>
                <p className="text-gray-700 dark:text-gray-300 mb-6 leading-relaxed text-lg">
                  {product.description}
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
                  <motion.div
                    className="flex items-start"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.1 }}
                  >
                    <div className="w-12 h-12 rounded-full bg-green-50 dark:bg-green-900/30 flex items-center justify-center text-green-600 dark:text-green-400 mr-4">
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-bold text-gray-900 dark:text-white text-lg">Qualité garantie</h3>
                      <p className="text-gray-600 dark:text-gray-400">Tous nos produits sont soigneusement sélectionnés par nos producteurs locaux</p>
                    </div>
                  </motion.div>
                  <motion.div
                    className="flex items-start"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.2 }}
                  >
                    <div className="w-12 h-12 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center text-blue-600 dark:text-blue-400 mr-4">
                      <FaClock className="w-6 h-6" />
                    </div>
                    <div>
                      <h3 className="font-bold text-gray-900 dark:text-white text-lg">Livraison rapide</h3>
                      <p className="text-gray-600 dark:text-gray-400">Livraison le jour même ou le lendemain pour préserver la fraîcheur</p>
                    </div>
                  </motion.div>
                </div>
              </div>
            )}

            {/* Onglet Recettes */}
            {activeTab === 'recipes' && productEnrichment && (
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white font-heading mb-6 flex items-center gap-3">
                  <span className="text-3xl">🍴</span>
                  Propositions de recettes
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {productEnrichment.recipes.map((recipe, index) => (
                    <motion.div
                      key={index}
                      className="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-xl p-6 border border-orange-200 dark:border-orange-800 hover:shadow-lg transition-all duration-300"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      whileHover={{ y: -4 }}
                    >
                      <div className="text-4xl mb-4">{recipe.icon}</div>
                      <h3 className="font-bold text-gray-900 dark:text-white text-lg mb-2">{recipe.name}</h3>
                      <p className="text-gray-600 dark:text-gray-400 mb-4 leading-relaxed">{recipe.description}</p>
                      <div className="flex items-center justify-between text-sm">
                        <span className={`px-3 py-1 rounded-full font-medium ${
                          recipe.difficulty === 'Facile' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' :
                          recipe.difficulty === 'Moyen' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400' :
                          'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                        }`}>
                          {recipe.difficulty}
                        </span>
                        <span className="flex items-center gap-1 text-gray-600 dark:text-gray-400">
                          <FaClock className="w-3 h-3" />
                          {recipe.time}
                        </span>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            )}

            {/* Onglet Conseils */}
            {activeTab === 'tips' && productEnrichment && (
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white font-heading mb-6 flex items-center gap-3">
                  <span className="text-3xl">💡</span>
                  Conseils culinaires
                </h2>
                <div className="space-y-6">
                  {productEnrichment.tips.map((tip, index) => (
                    <motion.div
                      key={index}
                      className={`flex items-start p-6 rounded-xl border-l-4 ${
                        tip.type === 'cooking' ? 'bg-red-50 dark:bg-red-900/20 border-red-500' :
                        tip.type === 'storage' ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-500' :
                        'bg-green-50 dark:bg-green-900/20 border-green-500'
                      }`}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <div className="text-3xl mr-4">{tip.icon}</div>
                      <div>
                        <h3 className="font-bold text-gray-900 dark:text-white text-lg mb-2">{tip.title}</h3>
                        <p className="text-gray-600 dark:text-gray-400 leading-relaxed">{tip.description}</p>
                        <span className={`inline-block mt-3 px-3 py-1 rounded-full text-xs font-medium ${
                          tip.type === 'cooking' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400' :
                          tip.type === 'storage' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400' :
                          'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                        }`}>
                          {tip.type === 'cooking' ? '🔥 Cuisson' : tip.type === 'storage' ? '❄️ Conservation' : '🔪 Préparation'}
                        </span>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            )}

            {/* Onglet Bienfaits santé */}
            {activeTab === 'health' && productEnrichment && (
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white font-heading mb-6 flex items-center gap-3">
                  <span className="text-3xl">🌿</span>
                  Bienfaits pour la santé
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {productEnrichment.healthBenefits.map((benefit, index) => (
                    <motion.div
                      key={index}
                      className={`p-6 rounded-xl border ${
                        benefit.category === 'nutrition' ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800' :
                        benefit.category === 'health' ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800' :
                        'bg-purple-50 dark:bg-purple-900/20 border-purple-200 dark:border-purple-800'
                      }`}
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: index * 0.1 }}
                      whileHover={{ scale: 1.02 }}
                    >
                      <div className="flex items-start">
                        <div className="text-3xl mr-4">{benefit.icon}</div>
                        <div>
                          <h3 className="font-bold text-gray-900 dark:text-white text-lg mb-2">{benefit.title}</h3>
                          <p className="text-gray-600 dark:text-gray-400 leading-relaxed">{benefit.description}</p>
                          <span className={`inline-block mt-3 px-3 py-1 rounded-full text-xs font-medium ${
                            benefit.category === 'nutrition' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' :
                            benefit.category === 'health' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400' :
                            'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400'
                          }`}>
                            {benefit.category === 'nutrition' ? '🥗 Nutrition' : benefit.category === 'health' ? '❤️ Santé' : '✨ Bien-être'}
                          </span>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            )}

            {/* Onglet Avis clients */}
            {activeTab === 'reviews' && (
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white font-heading mb-6 flex items-center gap-3">
                  <span className="text-3xl">⭐</span>
                  Avis clients
                </h2>
                <ProductReviews productId={product.id} />
              </div>
            )}

            {/* Onglet Livraison */}
            {activeTab === 'shipping' && (
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white font-heading mb-6 flex items-center gap-3">
                  <span className="text-3xl">🚚</span>
                  Informations de livraison
                </h2>
                <div className="space-y-6">
                  <motion.div
                    className="flex items-start p-6 bg-blue-50 dark:bg-blue-900/20 rounded-xl border border-blue-200 dark:border-blue-800"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 }}
                  >
                    <div className="w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center text-blue-600 dark:text-blue-400 mr-4">
                      <FaTruck className="w-6 h-6" />
                    </div>
                    <div>
                      <h3 className="font-bold text-gray-900 dark:text-white text-lg">Livraison standard</h3>
                      <p className="text-gray-600 dark:text-gray-400 mb-2">Livraison en 24-48h dans la région de Dakar</p>
                      <p className="text-lg font-bold text-blue-600 dark:text-blue-400">1000 XOF</p>
                    </div>
                  </motion.div>
                  <motion.div
                    className="flex items-start p-6 bg-green-50 dark:bg-green-900/20 rounded-xl border border-green-200 dark:border-green-800"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                  >
                    <div className="w-12 h-12 rounded-full bg-green-100 dark:bg-green-900/50 flex items-center justify-center text-green-600 dark:text-green-400 mr-4">
                      <FaClock className="w-6 h-6" />
                    </div>
                    <div>
                      <h3 className="font-bold text-gray-900 dark:text-white text-lg">Livraison express</h3>
                      <p className="text-gray-600 dark:text-gray-400 mb-2">Livraison en 2-4h pour les produits frais</p>
                      <p className="text-lg font-bold text-green-600 dark:text-green-400">2500 XOF</p>
                    </div>
                  </motion.div>
                </div>
              </div>
            )}
          </motion.div>
        </div>

        {/* Produits similaires */}
        <div className="mb-16">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white font-heading mb-6">
            Produits similaires
          </h2>
          <RelatedProducts products={relatedProducts} currentProductId={product.id} />
        </div>
      </div>
    </>
  );
}
