import React, { useState, useCallback, useRef } from 'react';
import { GoogleMap, <PERSON>er, InfoWindow } from '@react-google-maps/api';
import { Seller } from '../../services/sellerService';
import { useGoogleMaps } from '@/contexts/GoogleMapsContext';

const containerStyle = {
  width: '100%',
  height: '100%'
};

// Coordonnées par défaut (Dakar, Sénégal)
const defaultCenter = {
  lat: 14.7167,
  lng: -17.4677
};

interface MapProps {
  sellers: Seller[];
  onSelectSeller: (seller: Seller) => void;
  selectedSeller?: Seller | null;
  userLocation?: { lat: number; lng: number } | null;
  zoom?: number;
}

const GoogleMapComponent: React.FC<MapProps> = ({
  sellers,
  onSelectSeller,
  selectedSeller,
  userLocation,
  zoom = 13
}) => {
  const { isLoaded } = useGoogleMaps();

  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [infoWindowSeller, setInfoWindowSeller] = useState<Seller | null>(null);
  const mapRef = useRef<google.maps.Map | null>(null);

  const onLoad = useCallback((map: google.maps.Map) => {
    mapRef.current = map;
    setMap(map);
  }, []);

  const onUnmount = useCallback(() => {
    mapRef.current = null;
    setMap(null);
  }, []);

  const handleMarkerClick = (seller: Seller) => {
    setInfoWindowSeller(seller);
    onSelectSeller(seller);
  };

  const getMarkerIcon = (category: string, isSelected: boolean) => {
    const scale = isSelected ? 1.2 : 1;
    const color =
      category === 'Aliments' ? '#1E3A8A' :  // primary
      category === 'Artisanat' ? '#F97316' : // secondary
      '#0D9488';  // accent (Services)

    return {
      path: google.maps.SymbolPath.CIRCLE,
      fillColor: color,
      fillOpacity: 0.9,
      strokeWeight: 2,
      strokeColor: '#ffffff',
      scale: 10 * scale,
    };
  };

  if (!isLoaded) {
    return <div className="w-full h-full flex items-center justify-center bg-gray-100">Chargement de la carte...</div>;
  }

  const center = userLocation || defaultCenter;

  return (
    <GoogleMap
      mapContainerStyle={containerStyle}
      center={center}
      zoom={zoom}
      onLoad={onLoad}
      onUnmount={onUnmount}
      options={{
        fullscreenControl: false,
        streetViewControl: false,
        mapTypeControl: false,
        zoomControl: true,
        styles: [
          {
            featureType: 'poi',
            elementType: 'labels',
            stylers: [{ visibility: 'off' }]
          }
        ]
      }}
    >
      {/* User location marker */}
      {userLocation && (
        <Marker
          position={userLocation}
          icon={{
            path: google.maps.SymbolPath.CIRCLE,
            fillColor: '#4F46E5',
            fillOpacity: 1,
            strokeWeight: 2,
            strokeColor: '#ffffff',
            scale: 8,
          }}
          title="Votre position"
        />
      )}

      {/* Seller markers */}
      {sellers.map((seller) => (
        <Marker
          key={seller.id}
          position={{ lat: seller.latitude, lng: seller.longitude }}
          onClick={() => handleMarkerClick(seller)}
          icon={getMarkerIcon(seller.category, selectedSeller?.id === seller.id)}
          animation={selectedSeller?.id === seller.id ? google.maps.Animation.BOUNCE : undefined}
        />
      ))}

      {/* Info window */}
      {infoWindowSeller && (
        <InfoWindow
          position={{ lat: infoWindowSeller.latitude, lng: infoWindowSeller.longitude }}
          onCloseClick={() => setInfoWindowSeller(null)}
        >
          <div className="p-2 max-w-xs">
            <h3 className="font-semibold text-gray-900">{infoWindowSeller.name}</h3>
            <p className="text-sm text-gray-600 mt-1 line-clamp-2">{infoWindowSeller.description}</p>
            <div className="flex items-center mt-2">
              <div className="flex items-center">
                <span className="text-yellow-500 mr-1">★</span>
                <span className="text-sm font-medium">{infoWindowSeller.rating}</span>
                <span className="text-xs text-gray-500 ml-1">({infoWindowSeller.reviews_count})</span>
              </div>
              <div className="ml-3 flex items-center">
                <div className={`w-2 h-2 rounded-full ${infoWindowSeller.is_open ? 'bg-green-500' : 'bg-red-500'} mr-1`}></div>
                <span className="text-xs font-medium">{infoWindowSeller.is_open ? 'OUVERT' : 'FERMÉ'}</span>
              </div>
            </div>
            <button
              onClick={() => onSelectSeller(infoWindowSeller)}
              className="mt-2 text-sm text-primary font-medium hover:underline"
            >
              Voir détails
            </button>
          </div>
        </InfoWindow>
      )}
    </GoogleMap>
  );
};

export default React.memo(GoogleMapComponent);
