"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/products/page",{

/***/ "(app-pages-browser)/./app/[locale]/products/page.tsx":
/*!****************************************!*\
  !*** ./app/[locale]/products/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/esm/development/react-client/index.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FaArrowRight,FaFilter,FaFire,FaHeart,FaList,FaMinus,FaPlus,FaSearch,FaShoppingCart,FaStar,FaTh,FaTimes!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _contexts_OfflineModeContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/OfflineModeContext */ \"(app-pages-browser)/./contexts/OfflineModeContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ProductsPage() {\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_5__.useTranslations)();\n    const { isOnline, offlineData } = (0,_contexts_OfflineModeContext__WEBPACK_IMPORTED_MODULE_4__.useOfflineMode)();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredProducts, setFilteredProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('grid');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('name');\n    const [priceRange, setPriceRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        0,\n        50000\n    ]);\n    const [minRating, setMinRating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showOnlyPromo, setShowOnlyPromo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showOnlyBio, setShowOnlyBio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [favorites, setFavorites] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [cart, setCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [showScrollTop, setShowScrollTop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTags, setSelectedTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showSuggestions, setShowSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const productsPerPage = 12;\n    // Tags populaires inspirés de TastyDaily\n    const popularTags = [\n        {\n            name: 'Bio',\n            emoji: '🌱',\n            color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'\n        },\n        {\n            name: 'Promo',\n            emoji: '🏷️',\n            color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'\n        },\n        {\n            name: 'Local',\n            emoji: '📍',\n            color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'\n        },\n        {\n            name: 'Frais',\n            emoji: '❄️',\n            color: 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-200'\n        },\n        {\n            name: 'Premium',\n            emoji: '⭐',\n            color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'\n        },\n        {\n            name: 'Traditionnel',\n            emoji: '🏛️',\n            color: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'\n        },\n        {\n            name: 'Artisanal',\n            emoji: '🎨',\n            color: 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200'\n        },\n        {\n            name: 'Nouveau',\n            emoji: '✨',\n            color: 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200'\n        }\n    ];\n    // Catégories LocaFresh avec emojis - Inspiré de TastyDaily\n    const locaFreshCategories = [\n        {\n            id: 'all',\n            name: 'Tout',\n            emoji: '🛒',\n            color: 'from-gray-400 to-gray-500'\n        },\n        {\n            id: 'Fruits',\n            name: 'Fruits',\n            emoji: '🍎',\n            color: 'from-red-400 to-orange-500'\n        },\n        {\n            id: 'Légumes',\n            name: 'Légumes',\n            emoji: '🥬',\n            color: 'from-green-400 to-green-500'\n        },\n        {\n            id: 'Viandes',\n            name: 'Viandes',\n            emoji: '🥩',\n            color: 'from-red-500 to-red-600'\n        },\n        {\n            id: 'Volaille',\n            name: 'Volaille',\n            emoji: '🐔',\n            color: 'from-yellow-400 to-orange-500'\n        },\n        {\n            id: 'Poissons',\n            name: 'Poissons',\n            emoji: '🐟',\n            color: 'from-blue-400 to-blue-500'\n        },\n        {\n            id: 'Boulangerie',\n            name: 'Boulangerie',\n            emoji: '🍞',\n            color: 'from-amber-400 to-amber-500'\n        },\n        {\n            id: 'Boissons',\n            name: 'Boissons',\n            emoji: '🥤',\n            color: 'from-cyan-400 to-blue-500'\n        },\n        {\n            id: 'Artisanat',\n            name: 'Artisanat',\n            emoji: '🎨',\n            color: 'from-purple-400 to-pink-500'\n        }\n    ];\n    // Données de démonstration enrichies pour LocaFresh - 27 produits variés\n    const demoProducts = [\n        {\n            id: '1',\n            name: 'Mangues Bio Kent',\n            price: 2500,\n            category: 'Fruits',\n            seller: 'Ferme Bio Diallo',\n            location: 'Thiès',\n            rating: 4.8,\n            image: 'https://images.unsplash.com/photo-1553279768-865429fa0078?w=400',\n            inStock: true,\n            description: 'Mangues biologiques fraîches, cultivées sans pesticides',\n            unit: 'kg',\n            weight: '1kg',\n            isPromo: true,\n            promoPrice: 2000,\n            badges: [\n                'Bio',\n                'Promo',\n                'Local'\n            ],\n            isNew: false,\n            isBio: true,\n            distance: 2.5,\n            deliveryTime: '20-30 min'\n        },\n        {\n            id: '2',\n            name: 'Tomates Cerises Bio',\n            price: 1800,\n            category: 'Légumes',\n            seller: 'Jardin de Fatou',\n            location: 'Rufisque',\n            rating: 4.6,\n            image: 'https://images.unsplash.com/photo-1546470427-e5b89b618b84?w=400',\n            inStock: true,\n            description: 'Tomates cerises fraîches du jour, cultivées en agriculture biologique',\n            unit: 'barquette',\n            weight: '250g',\n            badges: [\n                'Bio',\n                'Frais',\n                'Local'\n            ],\n            isNew: true,\n            isBio: true,\n            distance: 1.8,\n            deliveryTime: '15-25 min'\n        },\n        {\n            id: '3',\n            name: 'Pain Traditionnel au Feu de Bois',\n            price: 500,\n            category: 'Boulangerie',\n            seller: 'Boulangerie Artisanale',\n            location: 'Dakar',\n            rating: 4.9,\n            image: 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400',\n            inStock: true,\n            description: 'Pain traditionnel sénégalais cuit au feu de bois selon la recette ancestrale',\n            unit: 'pièce',\n            badges: [\n                'Artisanal',\n                'Traditionnel'\n            ],\n            isNew: false,\n            distance: 1.2,\n            deliveryTime: '10-20 min'\n        },\n        {\n            id: '4',\n            name: 'Bissap Artisanal aux Épices',\n            price: 1200,\n            category: 'Boissons',\n            seller: 'Les Délices de Khadija',\n            location: 'Saint-Louis',\n            rating: 4.7,\n            image: 'https://images.unsplash.com/photo-1622597467836-f3285f2131b8?w=400',\n            inStock: true,\n            description: 'Bissap artisanal aux épices naturelles, sans conservateurs',\n            unit: 'bouteille',\n            weight: '500ml',\n            badges: [\n                'Artisanal',\n                'Traditionnel'\n            ],\n            isNew: false,\n            distance: 4.2,\n            deliveryTime: '30-40 min'\n        },\n        {\n            id: '5',\n            name: 'Thiof Frais du Matin',\n            price: 3500,\n            category: 'Poissons',\n            seller: 'Pêcheurs de Soumbédioune',\n            location: 'Dakar',\n            rating: 4.5,\n            image: 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=400',\n            inStock: true,\n            description: 'Thiof fraîchement pêché ce matin, qualité premium',\n            unit: 'kg',\n            badges: [\n                'Frais',\n                'Premium',\n                'Local'\n            ],\n            isNew: false,\n            distance: 3.2,\n            deliveryTime: '25-35 min'\n        },\n        {\n            id: '6',\n            name: 'Sac Artisanal en Raphia',\n            price: 8000,\n            category: 'Artisanat',\n            seller: 'Atelier Sénégal Authentique',\n            location: 'Kaolack',\n            rating: 4.8,\n            image: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400',\n            inStock: true,\n            description: 'Sac artisanal en raphia tressé à la main, design traditionnel',\n            unit: 'pièce',\n            badges: [\n                'Artisanal',\n                'Traditionnel'\n            ],\n            isNew: true,\n            distance: 5.1,\n            deliveryTime: '40-50 min'\n        },\n        {\n            id: '7',\n            name: 'Bananes Bio Plantain',\n            price: 1500,\n            category: 'Fruits',\n            seller: 'Coopérative Fruits Bio',\n            location: 'Ziguinchor',\n            rating: 4.4,\n            image: 'https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=400',\n            inStock: true,\n            description: 'Bananes plantain biologiques, parfaites pour la cuisine',\n            unit: 'régime',\n            weight: '2kg',\n            isPromo: true,\n            promoPrice: 1200,\n            badges: [\n                'Bio',\n                'Promo'\n            ],\n            isNew: false,\n            isBio: true,\n            distance: 6.8,\n            deliveryTime: '45-60 min'\n        },\n        {\n            id: '8',\n            name: 'Poulet Fermier Bio',\n            price: 12000,\n            category: 'Volaille',\n            seller: 'Ferme Avicole Bio',\n            location: 'Mbour',\n            rating: 4.7,\n            image: 'https://images.unsplash.com/photo-1604503468506-a8da13d82791?w=400',\n            inStock: true,\n            description: 'Poulet fermier élevé en liberté, nourri aux grains bio',\n            unit: 'kg',\n            badges: [\n                'Bio',\n                'Premium'\n            ],\n            isNew: false,\n            isBio: true,\n            distance: 3.8,\n            deliveryTime: '30-40 min'\n        },\n        {\n            id: '9',\n            name: 'Ananas Victoria',\n            price: 3000,\n            category: 'Fruits',\n            seller: 'Plantation Tropicale',\n            location: 'Casamance',\n            rating: 4.9,\n            image: 'https://images.unsplash.com/photo-1550258987-190a2d41a8ba?w=400',\n            inStock: true,\n            description: 'Ananas Victoria extra sucré, cultivé en Casamance',\n            unit: 'pièce',\n            weight: '1.5kg',\n            badges: [\n                'Premium',\n                'Local'\n            ],\n            isNew: false,\n            distance: 8.2,\n            deliveryTime: '60-75 min'\n        },\n        {\n            id: '10',\n            name: 'Crevettes Fraîches',\n            price: 8500,\n            category: 'Poissons',\n            seller: 'Pêcheurs de Joal',\n            location: 'Joal-Fadiouth',\n            rating: 4.6,\n            image: 'https://images.unsplash.com/photo-1565680018434-b513d5e5fd47?w=400',\n            inStock: true,\n            description: 'Crevettes fraîches pêchées dans les eaux sénégalaises',\n            unit: 'kg',\n            badges: [\n                'Frais',\n                'Premium'\n            ],\n            isNew: true,\n            distance: 4.5,\n            deliveryTime: '35-45 min'\n        },\n        {\n            id: '11',\n            name: 'Miel Pur Local 500g',\n            price: 4500,\n            category: 'Artisanat',\n            seller: 'Apiculteurs de Casamance',\n            location: 'Casamance',\n            rating: 4.9,\n            image: 'https://images.unsplash.com/photo-1587049352846-4a222e784d38?w=400',\n            inStock: true,\n            description: 'Miel pur et naturel récolté dans les ruches traditionnelles',\n            unit: 'pot',\n            weight: '500g',\n            badges: [\n                'Traditionnel',\n                'Local'\n            ],\n            isNew: false,\n            distance: 8.2,\n            deliveryTime: '60-75 min'\n        },\n        {\n            id: '12',\n            name: 'Jus de Gingembre',\n            price: 800,\n            category: 'Boissons',\n            seller: 'Boissons Naturelles Dakar',\n            location: 'Dakar',\n            rating: 4.3,\n            image: 'https://images.unsplash.com/photo-1570197788417-0e82375c9371?w=400',\n            inStock: true,\n            description: 'Jus de gingembre frais, énergisant et rafraîchissant',\n            unit: 'bouteille',\n            weight: '330ml',\n            isPromo: true,\n            promoPrice: 600,\n            badges: [\n                'Promo',\n                'Frais'\n            ],\n            isNew: false,\n            distance: 2.1,\n            deliveryTime: '15-25 min'\n        },\n        {\n            id: '13',\n            name: 'Croissants Artisanaux',\n            price: 1500,\n            category: 'Boulangerie',\n            seller: 'Pâtisserie Française',\n            location: 'Dakar',\n            rating: 4.7,\n            image: 'https://images.unsplash.com/photo-1555507036-ab794f4afe5a?w=400',\n            inStock: true,\n            description: 'Croissants pur beurre, préparés selon la tradition française',\n            unit: 'lot de 6',\n            badges: [\n                'Artisanal',\n                'Nouveau'\n            ],\n            isNew: true,\n            distance: 1.5,\n            deliveryTime: '10-20 min'\n        },\n        {\n            id: '14',\n            name: 'Oignons Rouges',\n            price: 900,\n            category: 'Légumes',\n            seller: 'Maraîchers de Niayes',\n            location: 'Niayes',\n            rating: 4.2,\n            image: 'https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=400',\n            inStock: true,\n            description: 'Oignons rouges frais des Niayes, parfaits pour vos plats',\n            unit: 'kg',\n            badges: [\n                'Local',\n                'Frais'\n            ],\n            isNew: false,\n            distance: 3.7,\n            deliveryTime: '25-35 min'\n        },\n        {\n            id: '15',\n            name: 'Œufs de Poules Élevées au Sol',\n            price: 2200,\n            category: 'Volaille',\n            seller: 'Ferme Avicole Naturelle',\n            location: 'Thiès',\n            rating: 4.8,\n            image: 'https://images.unsplash.com/photo-1582722872445-44dc5f7e3c8f?w=400',\n            inStock: true,\n            description: 'Œufs frais de poules élevées au sol, riches en oméga-3',\n            unit: 'douzaine',\n            badges: [\n                'Bio',\n                'Premium'\n            ],\n            isNew: false,\n            isBio: true,\n            distance: 2.8,\n            deliveryTime: '20-30 min'\n        },\n        {\n            id: '30',\n            name: 'Jus de Bissap Artisanal 1L',\n            price: 1500,\n            category: 'Artisanat',\n            seller: 'Boissons Naturelles Dakar',\n            location: 'Dakar',\n            rating: 4.7,\n            image: 'https://images.unsplash.com/photo-1544145945-f90425340c7e?w=400',\n            inStock: true,\n            description: 'Jus de bissap artisanal, préparé selon la tradition sénégalaise avec des fleurs d\\'hibiscus locales',\n            unit: 'bouteille',\n            badges: [\n                'Artisanal',\n                'Traditionnel'\n            ],\n            isNew: false,\n            distance: 2.1,\n            deliveryTime: '15-25 min'\n        },\n        {\n            id: '31',\n            name: 'Confiture de Mangue Artisanale 250g',\n            price: 2800,\n            category: 'Artisanat',\n            seller: 'Confitures de Casamance',\n            location: 'Casamance',\n            rating: 4.8,\n            image: 'https://images.unsplash.com/photo-1484723091739-30a097e8f929?w=400',\n            inStock: true,\n            description: 'Confiture artisanale de mangues Kent, préparée avec des fruits locaux et du sucre de canne',\n            unit: 'pot',\n            badges: [\n                'Artisanal',\n                'Bio',\n                'Local'\n            ],\n            isNew: true,\n            isBio: true,\n            distance: 8.2,\n            deliveryTime: '60-75 min'\n        },\n        {\n            id: '32',\n            name: 'Savon Naturel au Karité 100g',\n            price: 1200,\n            category: 'Artisanat',\n            seller: 'Savonnerie Traditionnelle',\n            location: 'Thiès',\n            rating: 4.9,\n            image: 'https://images.unsplash.com/photo-1556228720-195a672e8a03?w=400',\n            inStock: true,\n            description: 'Savon artisanal au beurre de karité pur, fabriqué selon les méthodes traditionnelles',\n            unit: 'pièce',\n            badges: [\n                'Artisanal',\n                'Bio',\n                'Traditionnel'\n            ],\n            isNew: false,\n            isBio: true,\n            distance: 2.8,\n            deliveryTime: '20-30 min'\n        },\n        {\n            id: '33',\n            name: 'Panier Tressé en Raphia',\n            price: 3500,\n            category: 'Artisanat',\n            seller: 'Artisans de Kaolack',\n            location: 'Kaolack',\n            rating: 4.6,\n            image: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400',\n            inStock: true,\n            description: 'Panier artisanal tressé à la main en raphia naturel, idéal pour les courses ou la décoration',\n            unit: 'pièce',\n            badges: [\n                'Artisanal',\n                'Traditionnel',\n                'Local'\n            ],\n            isNew: true,\n            distance: 5.1,\n            deliveryTime: '40-50 min'\n        },\n        {\n            id: '34',\n            name: 'Jus de Gingembre Frais 500ml',\n            price: 1000,\n            category: 'Artisanat',\n            seller: 'Boissons Naturelles Dakar',\n            location: 'Dakar',\n            rating: 4.5,\n            image: 'https://images.unsplash.com/photo-1570197788417-0e82375c9371?w=400',\n            inStock: true,\n            description: 'Jus de gingembre frais artisanal, énergisant et rafraîchissant, préparé quotidiennement',\n            unit: 'bouteille',\n            badges: [\n                'Artisanal',\n                'Frais'\n            ],\n            isNew: false,\n            distance: 2.1,\n            deliveryTime: '15-25 min'\n        }\n    ];\n    // Fonctions utilitaires\n    const formatPrice = (price)=>{\n        return new Intl.NumberFormat('fr-FR', {\n            style: 'currency',\n            currency: 'XOF',\n            minimumFractionDigits: 0\n        }).format(price).replace('XOF', 'FCFA');\n    };\n    const toggleFavorite = (productId)=>{\n        setFavorites((prev)=>{\n            const newFavorites = new Set(prev);\n            if (newFavorites.has(productId)) {\n                newFavorites.delete(productId);\n            } else {\n                newFavorites.add(productId);\n            }\n            return newFavorites;\n        });\n    };\n    const updateCart = (productId, quantity)=>{\n        setCart((prev)=>({\n                ...prev,\n                [productId]: Math.max(0, quantity)\n            }));\n    };\n    const toggleTag = (tagName)=>{\n        setSelectedTags((prev)=>prev.includes(tagName) ? prev.filter((tag)=>tag !== tagName) : [\n                ...prev,\n                tagName\n            ]);\n    };\n    const clearAllFilters = ()=>{\n        setSelectedCategory('all');\n        setSearchQuery('');\n        setSelectedTags([]);\n        setShowOnlyPromo(false);\n        setShowOnlyBio(false);\n        setMinRating(0);\n        setPriceRange([\n            0,\n            50000\n        ]);\n    };\n    const addNotification = (message, type)=>{\n        const id = Date.now().toString();\n        setNotifications((prev)=>[\n                ...prev,\n                {\n                    id,\n                    message,\n                    type\n                }\n            ]);\n        setTimeout(()=>{\n            setNotifications((prev)=>prev.filter((n)=>n.id !== id));\n        }, 3000);\n    };\n    const scrollToTop = ()=>{\n        window.scrollTo({\n            top: 0,\n            behavior: 'smooth'\n        });\n    };\n    // Filtrage et tri\n    const filteredAndSortedProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductsPage.useMemo[filteredAndSortedProducts]\": ()=>{\n            let filtered = products.filter({\n                \"ProductsPage.useMemo[filteredAndSortedProducts].filtered\": (product)=>{\n                    const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) || product.seller.toLowerCase().includes(searchQuery.toLowerCase()) || product.category.toLowerCase().includes(searchQuery.toLowerCase()) || product.description.toLowerCase().includes(searchQuery.toLowerCase());\n                    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;\n                    const matchesPrice = product.price >= priceRange[0] && product.price <= priceRange[1];\n                    const matchesRating = product.rating >= minRating;\n                    const matchesPromo = !showOnlyPromo || product.isPromo;\n                    const matchesBio = !showOnlyBio || product.isBio;\n                    const matchesTags = selectedTags.length === 0 || selectedTags.every({\n                        \"ProductsPage.useMemo[filteredAndSortedProducts].filtered\": (tag)=>{\n                            var _product_badges;\n                            return (_product_badges = product.badges) === null || _product_badges === void 0 ? void 0 : _product_badges.includes(tag);\n                        }\n                    }[\"ProductsPage.useMemo[filteredAndSortedProducts].filtered\"]);\n                    return matchesSearch && matchesCategory && matchesPrice && matchesRating && matchesPromo && matchesBio && matchesTags;\n                }\n            }[\"ProductsPage.useMemo[filteredAndSortedProducts].filtered\"]);\n            filtered.sort({\n                \"ProductsPage.useMemo[filteredAndSortedProducts]\": (a, b)=>{\n                    switch(sortBy){\n                        case 'price':\n                            return (a.promoPrice || a.price) - (b.promoPrice || b.price);\n                        case 'rating':\n                            return (b.rating || 0) - (a.rating || 0);\n                        case 'distance':\n                            return (a.distance || 0) - (b.distance || 0);\n                        default:\n                            return a.name.localeCompare(b.name);\n                    }\n                }\n            }[\"ProductsPage.useMemo[filteredAndSortedProducts]\"]);\n            return filtered;\n        }\n    }[\"ProductsPage.useMemo[filteredAndSortedProducts]\"], [\n        products,\n        searchQuery,\n        selectedCategory,\n        priceRange,\n        minRating,\n        showOnlyPromo,\n        showOnlyBio,\n        sortBy,\n        selectedTags\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductsPage.useEffect\": ()=>{\n            const fetchProducts = {\n                \"ProductsPage.useEffect.fetchProducts\": async ()=>{\n                    setIsLoading(true);\n                    await new Promise({\n                        \"ProductsPage.useEffect.fetchProducts\": (resolve)=>setTimeout(resolve, 1000)\n                    }[\"ProductsPage.useEffect.fetchProducts\"]);\n                    setProducts(demoProducts);\n                    setIsLoading(false);\n                }\n            }[\"ProductsPage.useEffect.fetchProducts\"];\n            fetchProducts();\n        }\n    }[\"ProductsPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductsPage.useEffect\": ()=>{\n            setFilteredProducts(filteredAndSortedProducts);\n        }\n    }[\"ProductsPage.useEffect\"], [\n        filteredAndSortedProducts\n    ]);\n    // Gestion du scroll pour le bouton retour en haut\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductsPage.useEffect\": ()=>{\n            const handleScroll = {\n                \"ProductsPage.useEffect.handleScroll\": ()=>{\n                    setShowScrollTop(window.scrollY > 400);\n                }\n            }[\"ProductsPage.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"ProductsPage.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"ProductsPage.useEffect\"];\n        }\n    }[\"ProductsPage.useEffect\"], []);\n    // Skeleton Loader Component inspiré de TastyDaily\n    const SkeletonLoader = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-900\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative h-80 bg-gradient-to-r from-gray-300 to-gray-400 dark:from-gray-700 dark:to-gray-600 animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-black/20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 569,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-10 h-full flex items-center justify-center text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto px-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-16 bg-white/20 rounded-2xl mb-4 animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 572,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 bg-white/20 rounded-xl mb-6 animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 573,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-10 w-32 bg-white/20 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 575,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-10 w-32 bg-white/20 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 576,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 574,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 571,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 570,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                    lineNumber: 568,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"sticky top-0 z-40 bg-white/95 dark:bg-gray-800/95 backdrop-blur-md border-b border-gray-200 dark:border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 py-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col lg:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 h-14 bg-gray-200 dark:bg-gray-700 rounded-2xl animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 586,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-14 w-32 bg-gray-200 dark:bg-gray-700 rounded-2xl animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 588,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-14 w-20 bg-gray-200 dark:bg-gray-700 rounded-2xl animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 589,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 587,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 585,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3 overflow-x-auto pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 594,\n                                        columnNumber: 13\n                                    }, this),\n                                    [\n                                        ...Array(8)\n                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-10 w-24 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse flex-shrink-0\"\n                                        }, i, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 15\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 593,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 584,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                    lineNumber: 583,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 py-8\",\n                    children: [\n                        [\n                            ...Array(3)\n                        ].map((_, sectionIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-2xl animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 609,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-6 w-40 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 611,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-4 w-60 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 612,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 610,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 608,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-6 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 615,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 607,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                        children: [\n                                            ...Array(4)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-48 bg-gray-200 dark:bg-gray-700 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 621,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-5 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 623,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-6 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 625,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 626,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 624,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 622,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, i, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 620,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 618,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, sectionIndex, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 606,\n                                columnNumber: 11\n                            }, this)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-48 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 637,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                                    children: [\n                                        ...Array(12)\n                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white dark:bg-gray-800 rounded-3xl shadow-lg overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-56 bg-gray-200 dark:bg-gray-700 animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 641,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-start mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 645,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-4 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 646,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 644,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-6 w-12 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 648,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 643,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 650,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-4 w-3/4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 651,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-8 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 653,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-10 w-24 bg-gray-200 dark:bg-gray-700 rounded-2xl animate-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 654,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 652,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 642,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, i, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 640,\n                                            columnNumber: 15\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 638,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 636,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                    lineNumber: 603,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n            lineNumber: 566,\n            columnNumber: 5\n        }, this);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonLoader, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n            lineNumber: 666,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative h-80 bg-gradient-to-r from-green-600 to-blue-600 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/30\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 673,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-cover bg-center filter blur-sm\",\n                        style: {\n                            backgroundImage: 'url(https://images.unsplash.com/photo-1542838132-92c53300491e?w=1200)'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 674,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 h-full flex items-center justify-center text-center text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            className: \"max-w-4xl mx-auto px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl md:text-6xl font-bold mb-4\",\n                                    children: \"\\uD83D\\uDED2 LocaFresh Market\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 687,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl md:text-2xl opacity-90 mb-6\",\n                                    children: \"D\\xe9couvrez les meilleurs produits locaux pr\\xe8s de chez vous\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 690,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center gap-4 text-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full\",\n                                            children: [\n                                                \"\\uD83C\\uDF31 \",\n                                                filteredAndSortedProducts.length,\n                                                \" produits frais\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 694,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full\",\n                                            children: \"\\uD83D\\uDE9A Livraison rapide\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 697,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 693,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 681,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 680,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                lineNumber: 672,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                className: \"sticky top-0 z-40 bg-white/95 dark:bg-gray-800/95 backdrop-blur-md border-b border-gray-200 dark:border-gray-700 shadow-lg\",\n                initial: {\n                    opacity: 0,\n                    y: -20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    delay: 0.3\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col lg:flex-row gap-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaSearch, {\n                                            className: \"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 716,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Rechercher fruits, l\\xe9gumes, viandes, poissons...\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            className: \"w-full pl-12 pr-4 py-4 bg-white dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-2xl focus:outline-none focus:ring-4 focus:ring-green-100 dark:focus:ring-green-900 focus:border-green-400 transition-all duration-300 text-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 717,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 715,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: sortBy,\n                                            onChange: (e)=>setSortBy(e.target.value),\n                                            className: \"px-4 py-4 bg-white dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-2xl focus:outline-none focus:ring-2 focus:ring-green-500 text-gray-900 dark:text-white\",\n                                            title: \"Trier par\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"name\",\n                                                    children: \"\\uD83D\\uDCDD Nom\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 734,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"price\",\n                                                    children: \"\\uD83D\\uDCB0 Prix\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 735,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"rating\",\n                                                    children: \"⭐ Note\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 736,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"distance\",\n                                                    children: \"\\uD83D\\uDCCD Distance\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 737,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 728,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex bg-gray-100 dark:bg-gray-700 rounded-2xl p-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setViewMode('grid'),\n                                                    className: \"px-4 py-3 rounded-xl transition-all duration-300 \".concat(viewMode === 'grid' ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-md' : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'),\n                                                    title: \"Vue grille\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaTh, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 751,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 741,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setViewMode('list'),\n                                                    className: \"px-4 py-3 rounded-xl transition-all duration-300 \".concat(viewMode === 'list' ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-md' : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'),\n                                                    title: \"Vue liste\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaList, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 763,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 753,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 740,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 727,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 714,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 overflow-x-auto pb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-gray-700 dark:text-gray-300 whitespace-nowrap\",\n                                    children: \"Cat\\xe9gories :\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 771,\n                                    columnNumber: 13\n                                }, this),\n                                locaFreshCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                                        type: \"button\",\n                                        onClick: ()=>setSelectedCategory(category.id),\n                                        className: \"flex items-center gap-2 px-4 py-2 rounded-full whitespace-nowrap transition-all duration-300 \".concat(selectedCategory === category.id ? \"bg-gradient-to-r \".concat(category.color, \" text-white shadow-lg scale-105\") : 'bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'),\n                                        whileHover: {\n                                            scale: selectedCategory === category.id ? 1.05 : 1.02\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg\",\n                                                children: category.emoji\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 785,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: category.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 786,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, category.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 773,\n                                        columnNumber: 15\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 770,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 overflow-x-auto pb-2 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-gray-700 dark:text-gray-300 whitespace-nowrap\",\n                                    children: \"Tags populaires :\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 793,\n                                    columnNumber: 13\n                                }, this),\n                                popularTags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                                        type: \"button\",\n                                        onClick: ()=>toggleTag(tag.name),\n                                        className: \"flex items-center gap-2 px-3 py-1.5 rounded-full whitespace-nowrap text-sm font-medium transition-all duration-300 \".concat(selectedTags.includes(tag.name) ? \"\".concat(tag.color, \" shadow-md scale-105 ring-2 ring-offset-2 ring-blue-500 dark:ring-offset-gray-800\") : \"\".concat(tag.color, \" hover:shadow-md hover:scale-102 opacity-70 hover:opacity-100\")),\n                                        whileHover: {\n                                            scale: selectedTags.includes(tag.name) ? 1.05 : 1.02\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: tag.emoji\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 807,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: tag.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 808,\n                                                columnNumber: 17\n                                            }, this),\n                                            selectedTags.includes(tag.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.span, {\n                                                initial: {\n                                                    scale: 0\n                                                },\n                                                animate: {\n                                                    scale: 1\n                                                },\n                                                className: \"text-xs\",\n                                                children: \"✓\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 810,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, tag.name, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 795,\n                                        columnNumber: 15\n                                    }, this)),\n                                (selectedTags.length > 0 || selectedCategory !== 'all' || searchQuery || showOnlyPromo || showOnlyBio || minRating > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                                    type: \"button\",\n                                    onClick: clearAllFilters,\n                                    className: \"flex items-center gap-2 px-3 py-1.5 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-all duration-300\",\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaTimes, {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 830,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Effacer tout\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 831,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 823,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 792,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mt-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setShowFilters(!showFilters),\n                                        className: \"flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-xl transition-all duration-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaFilter, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 844,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Filtres\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 845,\n                                                columnNumber: 17\n                                            }, this),\n                                            (showOnlyPromo || showOnlyBio || minRating > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-red-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 847,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 839,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: filteredAndSortedProducts.length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 852,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" produit\",\n                                            filteredAndSortedProducts.length > 1 ? 's' : '',\n                                            \" trouv\\xe9\",\n                                            filteredAndSortedProducts.length > 1 ? 's' : ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 851,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 838,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 837,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                            children: showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    height: 0\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    height: 'auto'\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    height: 0\n                                },\n                                transition: {\n                                    duration: 0.3\n                                },\n                                className: \"mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                    children: [\n                                                        \"Prix maximum: \",\n                                                        priceRange[1].toLocaleString(),\n                                                        \" FCFA\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 869,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"range\",\n                                                    min: \"0\",\n                                                    max: \"50000\",\n                                                    value: priceRange[1],\n                                                    onChange: (e)=>setPriceRange([\n                                                            0,\n                                                            Number(e.target.value)\n                                                        ]),\n                                                    className: \"w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 872,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 868,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                    children: [\n                                                        \"Note minimum: \",\n                                                        minRating,\n                                                        \"⭐\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 882,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"range\",\n                                                    min: \"0\",\n                                                    max: \"5\",\n                                                    step: \"0.5\",\n                                                    value: minRating,\n                                                    onChange: (e)=>setMinRating(Number(e.target.value)),\n                                                    className: \"w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 885,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 881,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: showOnlyPromo,\n                                                            onChange: (e)=>setShowOnlyPromo(e.target.checked),\n                                                            className: \"rounded border-gray-300 text-green-600 focus:ring-green-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 897,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 text-sm text-gray-700 dark:text-gray-300\",\n                                                            children: \"\\uD83C\\uDFF7️ Promotions uniquement\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 903,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 896,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: showOnlyBio,\n                                                            onChange: (e)=>setShowOnlyBio(e.target.checked),\n                                                            className: \"rounded border-gray-300 text-green-600 focus:ring-green-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 906,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 text-sm text-gray-700 dark:text-gray-300\",\n                                                            children: \"\\uD83C\\uDF31 Bio uniquement\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 912,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 905,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 895,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 867,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 860,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 858,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                    lineNumber: 712,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                lineNumber: 706,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.section, {\n                        className: \"mb-12\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.4\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-r from-red-500 to-orange-500 rounded-2xl flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaFire, {\n                                                    className: \"text-white text-xl\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 934,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 933,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: \"Offres du jour\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 937,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                        children: \"Profitez de nos promotions exceptionnelles\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 938,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 936,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 932,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/fr/products?filter=promo\",\n                                        className: \"text-blue-600 hover:text-blue-700 font-medium\",\n                                        children: \"Voir tout →\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 941,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 931,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                children: filteredAndSortedProducts.filter((p)=>p.isPromo).slice(0, 4).map((product, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        className: \"group bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 border border-red-100 dark:border-red-900\",\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.9\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            delay: index * 0.1\n                                        },\n                                        whileHover: {\n                                            y: -4\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/fr/products/\".concat(product.id),\n                                            className: \"block\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative h-48 overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            src: product.image,\n                                                            alt: product.name,\n                                                            fill: true,\n                                                            className: \"object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 958,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-3 left-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg\",\n                                                                children: [\n                                                                    \"\\uD83C\\uDFF7️ -\",\n                                                                    Math.round((product.price - (product.promoPrice || product.price)) / product.price * 100),\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 965,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 964,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: (e)=>{\n                                                                e.preventDefault();\n                                                                e.stopPropagation();\n                                                                toggleFavorite(product.id);\n                                                            },\n                                                            className: \"absolute top-3 right-3 w-8 h-8 rounded-full shadow-lg transition-all duration-300 \".concat(favorites.has(product.id) ? 'bg-red-500 text-white' : 'bg-white/90 text-gray-600 hover:bg-red-500 hover:text-white'),\n                                                            title: favorites.has(product.id) ? 'Retirer des favoris' : 'Ajouter aux favoris',\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaHeart, {\n                                                                className: \"w-3 h-3 mx-auto\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 983,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 969,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 957,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-bold text-gray-900 dark:text-white mb-2 line-clamp-1\",\n                                                            children: product.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 988,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg font-bold text-red-600 dark:text-red-400\",\n                                                                            children: formatPrice(product.promoPrice || product.price)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 991,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        product.promoPrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-500 line-through\",\n                                                                            children: formatPrice(product.price)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 995,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 990,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: (e)=>{\n                                                                        e.preventDefault();\n                                                                        e.stopPropagation();\n                                                                        updateCart(product.id, (cart[product.id] || 0) + 1);\n                                                                        addNotification(\"\".concat(product.name, \" ajout\\xe9 au panier\"), 'success');\n                                                                    },\n                                                                    className: \"w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-full flex items-center justify-center hover:from-green-600 hover:to-emerald-600 transition-all duration-300\",\n                                                                    title: \"Ajouter au panier\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaPlus, {\n                                                                        className: \"w-3 h-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 1011,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 1000,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 989,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 987,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 956,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, \"promo-\".concat(product.id), false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 948,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 946,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 925,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.section, {\n                        className: \"mb-12\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.5\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-xl\",\n                                                    children: \"\\uD83D\\uDD25\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1031,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1030,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: \"Produits populaires\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1034,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                        children: \"Les plus appr\\xe9ci\\xe9s par nos clients\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1035,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1033,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 1029,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/fr/products?sort=rating\",\n                                        className: \"text-blue-600 hover:text-blue-700 font-medium\",\n                                        children: \"Voir tout →\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 1038,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 1028,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 gap-6\",\n                                children: filteredAndSortedProducts.filter((p)=>p.rating >= 4.5).slice(0, 6).map((product, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        className: \"group bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500 border border-orange-100 dark:border-orange-900\",\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.9\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            delay: index * 0.1\n                                        },\n                                        whileHover: {\n                                            y: -8,\n                                            scale: 1.02\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative h-48 md:h-56 overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        src: product.image,\n                                                        alt: product.name,\n                                                        fill: true,\n                                                        className: \"object-cover group-hover:scale-110 transition-transform duration-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1057,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-3 left-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg\",\n                                                            children: [\n                                                                \"⭐ \",\n                                                                product.rating\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1064,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1063,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>toggleFavorite(product.id),\n                                                        className: \"absolute top-3 right-3 w-8 h-8 rounded-full shadow-lg transition-all duration-300 \".concat(favorites.has(product.id) ? 'bg-red-500 text-white' : 'bg-white/90 text-gray-600 hover:bg-red-500 hover:text-white'),\n                                                        title: favorites.has(product.id) ? 'Retirer des favoris' : 'Ajouter aux favoris',\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaHeart, {\n                                                            className: \"w-3 h-3 mx-auto\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1078,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1068,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1056,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-bold text-gray-900 dark:text-white mb-2 line-clamp-1\",\n                                                        children: product.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1083,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-600 dark:text-gray-400 mb-3 line-clamp-2\",\n                                                        children: product.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1084,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col\",\n                                                                children: product.isPromo && product.promoPrice ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg font-bold text-red-600 dark:text-red-400\",\n                                                                            children: formatPrice(product.promoPrice)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 1089,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-500 line-through\",\n                                                                            children: formatPrice(product.price)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 1092,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg font-bold text-gray-900 dark:text-white\",\n                                                                    children: formatPrice(product.price)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 1097,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1086,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>{\n                                                                    updateCart(product.id, (cart[product.id] || 0) + 1);\n                                                                    addNotification(\"\".concat(product.name, \" ajout\\xe9 au panier\"), 'success');\n                                                                },\n                                                                className: \"px-4 py-2 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-xl text-sm font-medium hover:from-orange-600 hover:to-red-600 transition-all duration-300 shadow-md hover:shadow-lg\",\n                                                                title: \"Ajouter au panier\",\n                                                                children: \"Ajouter\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1102,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1085,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1082,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, \"popular-\".concat(product.id), true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 1048,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 1043,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 1022,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.section, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.6\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                            children: \"Tous nos produits\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 1128,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-400\",\n                                            children: \"D\\xe9couvrez notre s\\xe9lection compl\\xe8te\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 1129,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 1127,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 1126,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                                children: filteredAndSortedProducts.map((product, index)=>{\n                                    var _product_badges;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        className: \"group bg-white dark:bg-gray-800 rounded-3xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500\",\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.9\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            delay: index * 0.05\n                                        },\n                                        whileHover: {\n                                            y: -8\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative h-56 overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        src: product.image,\n                                                        alt: product.name,\n                                                        fill: true,\n                                                        className: \"object-cover group-hover:scale-110 transition-transform duration-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1144,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    product.isPromo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-3 left-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg\",\n                                                            children: [\n                                                                \"\\uD83C\\uDFF7️ -\",\n                                                                Math.round((product.price - (product.promoPrice || product.price)) / product.price * 100),\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1152,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1151,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    product.isNew && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-3 left-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-gradient-to-r from-green-500 to-emerald-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg\",\n                                                            children: \"✨ Nouveau\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1159,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1158,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>toggleFavorite(product.id),\n                                                        className: \"absolute top-3 right-3 w-10 h-10 rounded-full shadow-lg transition-all duration-300 \".concat(favorites.has(product.id) ? 'bg-red-500 text-white' : 'bg-white/90 text-gray-600 hover:bg-red-500 hover:text-white'),\n                                                        title: favorites.has(product.id) ? 'Retirer des favoris' : 'Ajouter aux favoris',\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaHeart, {\n                                                            className: \"w-4 h-4 mx-auto\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1174,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1164,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1143,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-bold text-gray-900 dark:text-white mb-1 line-clamp-1\",\n                                                                        children: product.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 1181,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                        children: product.seller\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 1182,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1180,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1 bg-yellow-100 dark:bg-yellow-900 px-2 py-1 rounded-full\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaStar, {\n                                                                        className: \"w-3 h-3 text-yellow-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 1185,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-medium text-yellow-700 dark:text-yellow-300\",\n                                                                        children: product.rating\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 1186,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1184,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1179,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-2\",\n                                                        children: product.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1190,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1 mb-4\",\n                                                        children: (_product_badges = product.badges) === null || _product_badges === void 0 ? void 0 : _product_badges.slice(0, 2).map((badge)=>{\n                                                            var _popularTags_find;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs px-2 py-1 rounded-full \".concat(((_popularTags_find = popularTags.find((tag)=>tag.name === badge)) === null || _popularTags_find === void 0 ? void 0 : _popularTags_find.color) || 'bg-gray-100 text-gray-800'),\n                                                                children: badge\n                                                            }, badge, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1194,\n                                                                columnNumber: 23\n                                                            }, this);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1192,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col\",\n                                                                children: [\n                                                                    product.isPromo && product.promoPrice ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xl font-bold text-red-600 dark:text-red-400\",\n                                                                                children: formatPrice(product.promoPrice)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 1209,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-500 line-through\",\n                                                                                children: formatPrice(product.price)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 1212,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xl font-bold text-gray-900 dark:text-white\",\n                                                                        children: formatPrice(product.price)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 1217,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: [\n                                                                            \"par \",\n                                                                            product.unit\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 1221,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1206,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    cart[product.id] > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2 bg-gray-100 dark:bg-gray-700 rounded-xl px-3 py-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>updateCart(product.id, cart[product.id] - 1),\n                                                                                className: \"w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaMinus, {\n                                                                                    className: \"w-2 h-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                                    lineNumber: 1232,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 1227,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-gray-900 dark:text-white min-w-[20px] text-center\",\n                                                                                children: cart[product.id]\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 1234,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>updateCart(product.id, cart[product.id] + 1),\n                                                                                className: \"w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center hover:bg-green-600 transition-colors\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaPlus, {\n                                                                                    className: \"w-2 h-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                                    lineNumber: 1242,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 1237,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 1226,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: ()=>{\n                                                                            updateCart(product.id, (cart[product.id] || 0) + 1);\n                                                                            addNotification(\"\".concat(product.name, \" ajout\\xe9 au panier\"), 'success');\n                                                                        },\n                                                                        className: \"px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-2xl font-medium hover:from-green-600 hover:to-emerald-600 transition-all duration-300 shadow-md hover:shadow-lg\",\n                                                                        title: \"Ajouter au panier\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaShoppingCart, {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 1256,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 1247,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1224,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1205,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1178,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, product.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 1135,\n                                        columnNumber: 15\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 1133,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 1121,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                lineNumber: 923,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: 300\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            x: 300\n                        },\n                        className: \"fixed top-4 right-4 z-50 px-6 py-4 rounded-2xl shadow-lg \".concat(notification.type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl\",\n                                    children: notification.type === 'success' ? '✅' : '❌'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 1282,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: notification.message\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 1285,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 1281,\n                            columnNumber: 13\n                        }, this)\n                    }, notification.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 1270,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                lineNumber: 1268,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                children: showScrollTop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                    type: \"button\",\n                    initial: {\n                        opacity: 0,\n                        scale: 0\n                    },\n                    animate: {\n                        opacity: 1,\n                        scale: 1\n                    },\n                    exit: {\n                        opacity: 0,\n                        scale: 0\n                    },\n                    onClick: scrollToTop,\n                    className: \"fixed bottom-8 right-8 z-40 w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center\",\n                    title: \"Retour en haut\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaArrowRight, {\n                        className: \"w-4 h-4 transform -rotate-90\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 1303,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                    lineNumber: 1294,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                lineNumber: 1292,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n        lineNumber: 670,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductsPage, \"V+Jl/P+ygj6BT4LsLoLG6xnxcC8=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_5__.useTranslations,\n        _contexts_OfflineModeContext__WEBPACK_IMPORTED_MODULE_4__.useOfflineMode\n    ];\n});\n_c = ProductsPage;\nvar _c;\n$RefreshReg$(_c, \"ProductsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/products/page.tsx\n"));

/***/ })

});