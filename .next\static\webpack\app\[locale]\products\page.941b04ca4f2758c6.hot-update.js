"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/products/page",{

/***/ "(app-pages-browser)/./app/[locale]/products/page.tsx":
/*!****************************************!*\
  !*** ./app/[locale]/products/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/esm/development/react-client/index.js\");\n/* harmony import */ var _contexts_OfflineModeContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/OfflineModeContext */ \"(app-pages-browser)/./contexts/OfflineModeContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ProductsPage() {\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_3__.useTranslations)();\n    const { isOnline, offlineData } = (0,_contexts_OfflineModeContext__WEBPACK_IMPORTED_MODULE_2__.useOfflineMode)();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredProducts, setFilteredProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('grid');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('name');\n    const [priceRange, setPriceRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        0,\n        50000\n    ]);\n    const [minRating, setMinRating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showOnlyPromo, setShowOnlyPromo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showOnlyBio, setShowOnlyBio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [favorites, setFavorites] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [cart, setCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [showScrollTop, setShowScrollTop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTags, setSelectedTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showSuggestions, setShowSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const productsPerPage = 12;\n    // Tags populaires inspirés de TastyDaily\n    const popularTags = [\n        {\n            name: 'Bio',\n            emoji: '🌱',\n            color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'\n        },\n        {\n            name: 'Promo',\n            emoji: '🏷️',\n            color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'\n        },\n        {\n            name: 'Local',\n            emoji: '📍',\n            color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'\n        },\n        {\n            name: 'Frais',\n            emoji: '❄️',\n            color: 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-200'\n        },\n        {\n            name: 'Premium',\n            emoji: '⭐',\n            color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'\n        },\n        {\n            name: 'Traditionnel',\n            emoji: '🏛️',\n            color: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'\n        },\n        {\n            name: 'Artisanal',\n            emoji: '🎨',\n            color: 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200'\n        },\n        {\n            name: 'Nouveau',\n            emoji: '✨',\n            color: 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200'\n        }\n    ];\n    // Catégories LocaFresh avec emojis - Inspiré de TastyDaily\n    const locaFreshCategories = [\n        {\n            id: 'all',\n            name: 'Tout',\n            emoji: '🛒',\n            color: 'from-gray-400 to-gray-500'\n        },\n        {\n            id: 'Fruits',\n            name: 'Fruits',\n            emoji: '🍎',\n            color: 'from-red-400 to-orange-500'\n        },\n        {\n            id: 'Légumes',\n            name: 'Légumes',\n            emoji: '🥬',\n            color: 'from-green-400 to-green-500'\n        },\n        {\n            id: 'Viandes',\n            name: 'Viandes',\n            emoji: '🥩',\n            color: 'from-red-500 to-red-600'\n        },\n        {\n            id: 'Volaille',\n            name: 'Volaille',\n            emoji: '🐔',\n            color: 'from-yellow-400 to-orange-500'\n        },\n        {\n            id: 'Poissons',\n            name: 'Poissons',\n            emoji: '🐟',\n            color: 'from-blue-400 to-blue-500'\n        },\n        {\n            id: 'Boulangerie',\n            name: 'Boulangerie',\n            emoji: '🍞',\n            color: 'from-amber-400 to-amber-500'\n        },\n        {\n            id: 'Boissons',\n            name: 'Boissons',\n            emoji: '🥤',\n            color: 'from-cyan-400 to-blue-500'\n        },\n        {\n            id: 'Artisanat',\n            name: 'Artisanat',\n            emoji: '🎨',\n            color: 'from-purple-400 to-pink-500'\n        }\n    ];\n    // Données de démonstration enrichies pour LocaFresh - 27 produits variés\n    const demoProducts = [\n        {\n            id: '1',\n            name: 'Mangues Bio Kent',\n            price: 2500,\n            category: 'Fruits',\n            seller: 'Ferme Bio Diallo',\n            location: 'Thiès',\n            rating: 4.8,\n            image: 'https://images.unsplash.com/photo-1553279768-865429fa0078?w=400',\n            inStock: true,\n            description: 'Mangues biologiques fraîches, cultivées sans pesticides',\n            unit: 'kg',\n            weight: '1kg',\n            isPromo: true,\n            promoPrice: 2000,\n            badges: [\n                'Bio',\n                'Promo',\n                'Local'\n            ],\n            isNew: false,\n            isBio: true,\n            distance: 2.5,\n            deliveryTime: '20-30 min'\n        },\n        {\n            id: '2',\n            name: 'Tomates Cerises Bio',\n            price: 1800,\n            category: 'Légumes',\n            seller: 'Jardin de Fatou',\n            location: 'Rufisque',\n            rating: 4.6,\n            image: 'https://images.unsplash.com/photo-1546470427-e5b89b618b84?w=400',\n            inStock: true,\n            description: 'Tomates cerises fraîches du jour, cultivées en agriculture biologique',\n            unit: 'barquette',\n            weight: '250g',\n            badges: [\n                'Bio',\n                'Frais',\n                'Local'\n            ],\n            isNew: true,\n            isBio: true,\n            distance: 1.8,\n            deliveryTime: '15-25 min'\n        },\n        {\n            id: '3',\n            name: 'Pain Traditionnel au Feu de Bois',\n            price: 500,\n            category: 'Boulangerie',\n            seller: 'Boulangerie Artisanale',\n            location: 'Dakar',\n            rating: 4.9,\n            image: 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400',\n            inStock: true,\n            description: 'Pain traditionnel sénégalais cuit au feu de bois selon la recette ancestrale',\n            unit: 'pièce',\n            badges: [\n                'Artisanal',\n                'Traditionnel'\n            ],\n            isNew: false,\n            distance: 1.2,\n            deliveryTime: '10-20 min'\n        },\n        {\n            id: '4',\n            name: 'Bissap Artisanal aux Épices',\n            price: 1200,\n            category: 'Boissons',\n            seller: 'Les Délices de Khadija',\n            location: 'Saint-Louis',\n            rating: 4.7,\n            image: 'https://images.unsplash.com/photo-1622597467836-f3285f2131b8?w=400',\n            inStock: true,\n            description: 'Bissap artisanal aux épices naturelles, sans conservateurs',\n            unit: 'bouteille',\n            weight: '500ml',\n            badges: [\n                'Artisanal',\n                'Traditionnel'\n            ],\n            isNew: false,\n            distance: 4.2,\n            deliveryTime: '30-40 min'\n        },\n        {\n            id: '5',\n            name: 'Thiof Frais du Matin',\n            price: 3500,\n            category: 'Poissons',\n            seller: 'Pêcheurs de Soumbédioune',\n            location: 'Dakar',\n            rating: 4.5,\n            image: 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=400',\n            inStock: true,\n            description: 'Thiof fraîchement pêché ce matin, qualité premium',\n            unit: 'kg',\n            badges: [\n                'Frais',\n                'Premium',\n                'Local'\n            ],\n            isNew: false,\n            distance: 3.2,\n            deliveryTime: '25-35 min'\n        },\n        {\n            id: '6',\n            name: 'Sac Artisanal en Raphia',\n            price: 8000,\n            category: 'Artisanat',\n            seller: 'Atelier Sénégal Authentique',\n            location: 'Kaolack',\n            rating: 4.8,\n            image: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400',\n            inStock: true,\n            description: 'Sac artisanal en raphia tressé à la main, design traditionnel',\n            unit: 'pièce',\n            badges: [\n                'Artisanal',\n                'Traditionnel'\n            ],\n            isNew: true,\n            distance: 5.1,\n            deliveryTime: '40-50 min'\n        },\n        {\n            id: '7',\n            name: 'Bananes Bio Plantain',\n            price: 1500,\n            category: 'Fruits',\n            seller: 'Coopérative Fruits Bio',\n            location: 'Ziguinchor',\n            rating: 4.4,\n            image: 'https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=400',\n            inStock: true,\n            description: 'Bananes plantain biologiques, parfaites pour la cuisine',\n            unit: 'régime',\n            weight: '2kg',\n            isPromo: true,\n            promoPrice: 1200,\n            badges: [\n                'Bio',\n                'Promo'\n            ],\n            isNew: false,\n            isBio: true,\n            distance: 6.8,\n            deliveryTime: '45-60 min'\n        },\n        {\n            id: '8',\n            name: 'Poulet Fermier Bio',\n            price: 12000,\n            category: 'Volaille',\n            seller: 'Ferme Avicole Bio',\n            location: 'Mbour',\n            rating: 4.7,\n            image: 'https://images.unsplash.com/photo-1604503468506-a8da13d82791?w=400',\n            inStock: true,\n            description: 'Poulet fermier élevé en liberté, nourri aux grains bio',\n            unit: 'kg',\n            badges: [\n                'Bio',\n                'Premium'\n            ],\n            isNew: false,\n            isBio: true,\n            distance: 3.8,\n            deliveryTime: '30-40 min'\n        },\n        {\n            id: '9',\n            name: 'Ananas Victoria',\n            price: 3000,\n            category: 'Fruits',\n            seller: 'Plantation Tropicale',\n            location: 'Casamance',\n            rating: 4.9,\n            image: 'https://images.unsplash.com/photo-1550258987-190a2d41a8ba?w=400',\n            inStock: true,\n            description: 'Ananas Victoria extra sucré, cultivé en Casamance',\n            unit: 'pièce',\n            weight: '1.5kg',\n            badges: [\n                'Premium',\n                'Local'\n            ],\n            isNew: false,\n            distance: 8.2,\n            deliveryTime: '60-75 min'\n        },\n        {\n            id: '10',\n            name: 'Crevettes Fraîches',\n            price: 8500,\n            category: 'Poissons',\n            seller: 'Pêcheurs de Joal',\n            location: 'Joal-Fadiouth',\n            rating: 4.6,\n            image: 'https://images.unsplash.com/photo-1565680018434-b513d5e5fd47?w=400',\n            inStock: true,\n            description: 'Crevettes fraîches pêchées dans les eaux sénégalaises',\n            unit: 'kg',\n            badges: [\n                'Frais',\n                'Premium'\n            ],\n            isNew: true,\n            distance: 4.5,\n            deliveryTime: '35-45 min'\n        }\n    ];\n    // Fonctions utilitaires\n    const formatPrice = (price)=>{\n        return new Intl.NumberFormat('fr-FR', {\n            style: 'currency',\n            currency: 'XOF',\n            minimumFractionDigits: 0\n        }).format(price).replace('XOF', 'FCFA');\n    };\n    const toggleFavorite = (productId)=>{\n        setFavorites((prev)=>{\n            const newFavorites = new Set(prev);\n            if (newFavorites.has(productId)) {\n                newFavorites.delete(productId);\n            } else {\n                newFavorites.add(productId);\n            }\n            return newFavorites;\n        });\n    };\n    const updateCart = (productId, quantity)=>{\n        setCart((prev)=>({\n                ...prev,\n                [productId]: Math.max(0, quantity)\n            }));\n    };\n    const toggleTag = (tagName)=>{\n        setSelectedTags((prev)=>prev.includes(tagName) ? prev.filter((tag)=>tag !== tagName) : [\n                ...prev,\n                tagName\n            ]);\n    };\n    const clearAllFilters = ()=>{\n        setSelectedCategory('all');\n        setSearchQuery('');\n        setSelectedTags([]);\n        setShowOnlyPromo(false);\n        setShowOnlyBio(false);\n        setMinRating(0);\n        setPriceRange([\n            0,\n            50000\n        ]);\n    };\n    // Filtrage et tri\n    const filteredAndSortedProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductsPage.useMemo[filteredAndSortedProducts]\": ()=>{\n            let filtered = products.filter({\n                \"ProductsPage.useMemo[filteredAndSortedProducts].filtered\": (product)=>{\n                    const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) || product.seller.toLowerCase().includes(searchQuery.toLowerCase()) || product.category.toLowerCase().includes(searchQuery.toLowerCase()) || product.description.toLowerCase().includes(searchQuery.toLowerCase());\n                    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;\n                    const matchesPrice = product.price >= priceRange[0] && product.price <= priceRange[1];\n                    const matchesRating = product.rating >= minRating;\n                    const matchesPromo = !showOnlyPromo || product.isPromo;\n                    const matchesBio = !showOnlyBio || product.isBio;\n                    const matchesTags = selectedTags.length === 0 || selectedTags.every({\n                        \"ProductsPage.useMemo[filteredAndSortedProducts].filtered\": (tag)=>{\n                            var _product_badges;\n                            return (_product_badges = product.badges) === null || _product_badges === void 0 ? void 0 : _product_badges.includes(tag);\n                        }\n                    }[\"ProductsPage.useMemo[filteredAndSortedProducts].filtered\"]);\n                    return matchesSearch && matchesCategory && matchesPrice && matchesRating && matchesPromo && matchesBio && matchesTags;\n                }\n            }[\"ProductsPage.useMemo[filteredAndSortedProducts].filtered\"]);\n            filtered.sort({\n                \"ProductsPage.useMemo[filteredAndSortedProducts]\": (a, b)=>{\n                    switch(sortBy){\n                        case 'price':\n                            return (a.promoPrice || a.price) - (b.promoPrice || b.price);\n                        case 'rating':\n                            return (b.rating || 0) - (a.rating || 0);\n                        case 'distance':\n                            return (a.distance || 0) - (b.distance || 0);\n                        default:\n                            return a.name.localeCompare(b.name);\n                    }\n                }\n            }[\"ProductsPage.useMemo[filteredAndSortedProducts]\"]);\n            return filtered;\n        }\n    }[\"ProductsPage.useMemo[filteredAndSortedProducts]\"], [\n        products,\n        searchQuery,\n        selectedCategory,\n        priceRange,\n        minRating,\n        showOnlyPromo,\n        showOnlyBio,\n        sortBy,\n        selectedTags\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductsPage.useEffect\": ()=>{\n            const fetchProducts = {\n                \"ProductsPage.useEffect.fetchProducts\": async ()=>{\n                    setIsLoading(true);\n                    await new Promise({\n                        \"ProductsPage.useEffect.fetchProducts\": (resolve)=>setTimeout(resolve, 1000)\n                    }[\"ProductsPage.useEffect.fetchProducts\"]);\n                    setProducts(demoProducts);\n                    setIsLoading(false);\n                }\n            }[\"ProductsPage.useEffect.fetchProducts\"];\n            fetchProducts();\n        }\n    }[\"ProductsPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductsPage.useEffect\": ()=>{\n            setFilteredProducts(filteredAndSortedProducts);\n        }\n    }[\"ProductsPage.useEffect\"], [\n        filteredAndSortedProducts\n    ]);\n    // Skeleton Loader\n    const SkeletonLoader = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-8 bg-gray-200 rounded animate-pulse mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 369,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-200 rounded animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                lineNumber: 368,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n            lineNumber: 367,\n            columnNumber: 5\n        }, this);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonLoader, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n            lineNumber: 376,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-4xl font-bold mb-4\",\n                    children: \"\\uD83D\\uDED2 LocaFresh Market\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                    lineNumber: 382,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xl mb-6\",\n                    children: \"D\\xe9couvrez les meilleurs produits locaux\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                    lineNumber: 383,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: [\n                            \"\\uD83C\\uDF31 \",\n                            filteredAndSortedProducts.length,\n                            \" produits frais\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 386,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                    lineNumber: 385,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-2 mb-4\",\n                    children: locaFreshCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setSelectedCategory(category.id),\n                            className: \"px-4 py-2 rounded \".concat(selectedCategory === category.id ? 'bg-blue-500 text-white' : 'bg-gray-200'),\n                            children: [\n                                category.emoji,\n                                \" \",\n                                category.name\n                            ]\n                        }, category.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 391,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                    lineNumber: 389,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                    children: filteredAndSortedProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white p-4 rounded shadow\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-bold\",\n                                    children: product.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: product.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg font-bold\",\n                                    children: formatPrice(product.price)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, product.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 405,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                    lineNumber: 403,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n            lineNumber: 381,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n        lineNumber: 380,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductsPage, \"hurTDdy4Sgg7oAUhXXKRbGKFv38=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_3__.useTranslations,\n        _contexts_OfflineModeContext__WEBPACK_IMPORTED_MODULE_2__.useOfflineMode\n    ];\n});\n_c = ProductsPage;\nvar _c;\n$RefreshReg$(_c, \"ProductsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/products/page.tsx\n"));

/***/ })

});