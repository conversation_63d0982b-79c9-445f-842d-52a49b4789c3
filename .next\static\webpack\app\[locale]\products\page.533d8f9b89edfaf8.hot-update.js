"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/products/page",{

/***/ "(app-pages-browser)/./app/[locale]/products/page.tsx":
/*!****************************************!*\
  !*** ./app/[locale]/products/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/esm/development/react-client/index.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=FaArrowRight,FaFilter,FaFire,FaHeart,FaList,FaMinus,FaPlus,FaSearch,FaShoppingCart,FaStar,FaTh,FaTimes!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _contexts_OfflineModeContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/OfflineModeContext */ \"(app-pages-browser)/./contexts/OfflineModeContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ProductsPage() {\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_5__.useTranslations)();\n    const { isOnline, offlineData } = (0,_contexts_OfflineModeContext__WEBPACK_IMPORTED_MODULE_4__.useOfflineMode)();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredProducts, setFilteredProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('grid');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('name');\n    const [priceRange, setPriceRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        0,\n        50000\n    ]);\n    const [minRating, setMinRating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showOnlyPromo, setShowOnlyPromo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showOnlyBio, setShowOnlyBio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [favorites, setFavorites] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [cart, setCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [showScrollTop, setShowScrollTop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTags, setSelectedTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showSuggestions, setShowSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const productsPerPage = 12;\n    // Tags populaires inspirés de TastyDaily\n    const popularTags = [\n        {\n            name: 'Bio',\n            emoji: '🌱',\n            color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'\n        },\n        {\n            name: 'Promo',\n            emoji: '🏷️',\n            color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'\n        },\n        {\n            name: 'Local',\n            emoji: '📍',\n            color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'\n        },\n        {\n            name: 'Frais',\n            emoji: '❄️',\n            color: 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-200'\n        },\n        {\n            name: 'Premium',\n            emoji: '⭐',\n            color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'\n        },\n        {\n            name: 'Traditionnel',\n            emoji: '🏛️',\n            color: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'\n        },\n        {\n            name: 'Artisanal',\n            emoji: '🎨',\n            color: 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200'\n        },\n        {\n            name: 'Nouveau',\n            emoji: '✨',\n            color: 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200'\n        }\n    ];\n    // Catégories LocaFresh avec emojis - Inspiré de TastyDaily\n    const locaFreshCategories = [\n        {\n            id: 'all',\n            name: 'Tout',\n            emoji: '🛒',\n            color: 'from-gray-400 to-gray-500'\n        },\n        {\n            id: 'Fruits',\n            name: 'Fruits',\n            emoji: '🍎',\n            color: 'from-red-400 to-orange-500'\n        },\n        {\n            id: 'Légumes',\n            name: 'Légumes',\n            emoji: '🥬',\n            color: 'from-green-400 to-green-500'\n        },\n        {\n            id: 'Viandes',\n            name: 'Viandes',\n            emoji: '🥩',\n            color: 'from-red-500 to-red-600'\n        },\n        {\n            id: 'Volaille',\n            name: 'Volaille',\n            emoji: '🐔',\n            color: 'from-yellow-400 to-orange-500'\n        },\n        {\n            id: 'Poissons',\n            name: 'Poissons',\n            emoji: '🐟',\n            color: 'from-blue-400 to-blue-500'\n        },\n        {\n            id: 'Boulangerie',\n            name: 'Boulangerie',\n            emoji: '🍞',\n            color: 'from-amber-400 to-amber-500'\n        },\n        {\n            id: 'Boissons',\n            name: 'Boissons',\n            emoji: '🥤',\n            color: 'from-cyan-400 to-blue-500'\n        },\n        {\n            id: 'Artisanat',\n            name: 'Artisanat',\n            emoji: '🎨',\n            color: 'from-purple-400 to-pink-500'\n        }\n    ];\n    // Données de démonstration enrichies pour LocaFresh - 27 produits variés\n    const demoProducts = [\n        {\n            id: '1',\n            name: 'Mangues Bio Kent',\n            price: 2500,\n            category: 'Fruits',\n            seller: 'Ferme Bio Diallo',\n            location: 'Thiès',\n            rating: 4.8,\n            image: 'https://images.unsplash.com/photo-1553279768-865429fa0078?w=400',\n            inStock: true,\n            description: 'Mangues biologiques fraîches, cultivées sans pesticides',\n            unit: 'kg',\n            weight: '1kg',\n            isPromo: true,\n            promoPrice: 2000,\n            badges: [\n                'Bio',\n                'Promo',\n                'Local'\n            ],\n            isNew: false,\n            isBio: true,\n            distance: 2.5,\n            deliveryTime: '20-30 min'\n        },\n        {\n            id: '2',\n            name: 'Tomates Cerises Bio',\n            price: 1800,\n            category: 'Légumes',\n            seller: 'Jardin de Fatou',\n            location: 'Rufisque',\n            rating: 4.6,\n            image: 'https://images.unsplash.com/photo-1546470427-e5b89b618b84?w=400',\n            inStock: true,\n            description: 'Tomates cerises fraîches du jour, cultivées en agriculture biologique',\n            unit: 'barquette',\n            weight: '250g',\n            badges: [\n                'Bio',\n                'Frais',\n                'Local'\n            ],\n            isNew: true,\n            isBio: true,\n            distance: 1.8,\n            deliveryTime: '15-25 min'\n        },\n        {\n            id: '3',\n            name: 'Pain Traditionnel au Feu de Bois',\n            price: 500,\n            category: 'Boulangerie',\n            seller: 'Boulangerie Artisanale',\n            location: 'Dakar',\n            rating: 4.9,\n            image: 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400',\n            inStock: true,\n            description: 'Pain traditionnel sénégalais cuit au feu de bois selon la recette ancestrale',\n            unit: 'pièce',\n            badges: [\n                'Artisanal',\n                'Traditionnel'\n            ],\n            isNew: false,\n            distance: 1.2,\n            deliveryTime: '10-20 min'\n        },\n        {\n            id: '4',\n            name: 'Bissap Artisanal aux Épices',\n            price: 1200,\n            category: 'Boissons',\n            seller: 'Les Délices de Khadija',\n            location: 'Saint-Louis',\n            rating: 4.7,\n            image: 'https://images.unsplash.com/photo-1622597467836-f3285f2131b8?w=400',\n            inStock: true,\n            description: 'Bissap artisanal aux épices naturelles, sans conservateurs',\n            unit: 'bouteille',\n            weight: '500ml',\n            badges: [\n                'Artisanal',\n                'Traditionnel'\n            ],\n            isNew: false,\n            distance: 4.2,\n            deliveryTime: '30-40 min'\n        },\n        {\n            id: '5',\n            name: 'Thiof Frais du Matin',\n            price: 3500,\n            category: 'Poissons',\n            seller: 'Pêcheurs de Soumbédioune',\n            location: 'Dakar',\n            rating: 4.5,\n            image: 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=400',\n            inStock: true,\n            description: 'Thiof fraîchement pêché ce matin, qualité premium',\n            unit: 'kg',\n            badges: [\n                'Frais',\n                'Premium',\n                'Local'\n            ],\n            isNew: false,\n            distance: 3.2,\n            deliveryTime: '25-35 min'\n        },\n        {\n            id: '6',\n            name: 'Sac Artisanal en Raphia',\n            price: 8000,\n            category: 'Artisanat',\n            seller: 'Atelier Sénégal Authentique',\n            location: 'Kaolack',\n            rating: 4.8,\n            image: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400',\n            inStock: true,\n            description: 'Sac artisanal en raphia tressé à la main, design traditionnel',\n            unit: 'pièce',\n            badges: [\n                'Artisanal',\n                'Traditionnel'\n            ],\n            isNew: true,\n            distance: 5.1,\n            deliveryTime: '40-50 min'\n        },\n        {\n            id: '7',\n            name: 'Bananes Bio Plantain',\n            price: 1500,\n            category: 'Fruits',\n            seller: 'Coopérative Fruits Bio',\n            location: 'Ziguinchor',\n            rating: 4.4,\n            image: 'https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=400',\n            inStock: true,\n            description: 'Bananes plantain biologiques, parfaites pour la cuisine',\n            unit: 'régime',\n            weight: '2kg',\n            isPromo: true,\n            promoPrice: 1200,\n            badges: [\n                'Bio',\n                'Promo'\n            ],\n            isNew: false,\n            isBio: true,\n            distance: 6.8,\n            deliveryTime: '45-60 min'\n        },\n        {\n            id: '8',\n            name: 'Poulet Fermier Bio',\n            price: 12000,\n            category: 'Volaille',\n            seller: 'Ferme Avicole Bio',\n            location: 'Mbour',\n            rating: 4.7,\n            image: 'https://images.unsplash.com/photo-1604503468506-a8da13d82791?w=400',\n            inStock: true,\n            description: 'Poulet fermier élevé en liberté, nourri aux grains bio',\n            unit: 'kg',\n            badges: [\n                'Bio',\n                'Premium'\n            ],\n            isNew: false,\n            isBio: true,\n            distance: 3.8,\n            deliveryTime: '30-40 min'\n        },\n        {\n            id: '9',\n            name: 'Ananas Victoria',\n            price: 3000,\n            category: 'Fruits',\n            seller: 'Plantation Tropicale',\n            location: 'Casamance',\n            rating: 4.9,\n            image: 'https://images.unsplash.com/photo-1550258987-190a2d41a8ba?w=400',\n            inStock: true,\n            description: 'Ananas Victoria extra sucré, cultivé en Casamance',\n            unit: 'pièce',\n            weight: '1.5kg',\n            badges: [\n                'Premium',\n                'Local'\n            ],\n            isNew: false,\n            distance: 8.2,\n            deliveryTime: '60-75 min'\n        },\n        {\n            id: '10',\n            name: 'Crevettes Fraîches',\n            price: 8500,\n            category: 'Poissons',\n            seller: 'Pêcheurs de Joal',\n            location: 'Joal-Fadiouth',\n            rating: 4.6,\n            image: 'https://images.unsplash.com/photo-1565680018434-b513d5e5fd47?w=400',\n            inStock: true,\n            description: 'Crevettes fraîches pêchées dans les eaux sénégalaises',\n            unit: 'kg',\n            badges: [\n                'Frais',\n                'Premium'\n            ],\n            isNew: true,\n            distance: 4.5,\n            deliveryTime: '35-45 min'\n        },\n        {\n            id: '11',\n            name: 'Miel Pur Local 500g',\n            price: 4500,\n            category: 'Artisanat',\n            seller: 'Apiculteurs de Casamance',\n            location: 'Casamance',\n            rating: 4.9,\n            image: 'https://images.unsplash.com/photo-1587049352846-4a222e784d38?w=400',\n            inStock: true,\n            description: 'Miel pur et naturel récolté dans les ruches traditionnelles',\n            unit: 'pot',\n            weight: '500g',\n            badges: [\n                'Traditionnel',\n                'Local'\n            ],\n            isNew: false,\n            distance: 8.2,\n            deliveryTime: '60-75 min'\n        },\n        {\n            id: '12',\n            name: 'Jus de Gingembre',\n            price: 800,\n            category: 'Boissons',\n            seller: 'Boissons Naturelles Dakar',\n            location: 'Dakar',\n            rating: 4.3,\n            image: 'https://images.unsplash.com/photo-1570197788417-0e82375c9371?w=400',\n            inStock: true,\n            description: 'Jus de gingembre frais, énergisant et rafraîchissant',\n            unit: 'bouteille',\n            weight: '330ml',\n            isPromo: true,\n            promoPrice: 600,\n            badges: [\n                'Promo',\n                'Frais'\n            ],\n            isNew: false,\n            distance: 2.1,\n            deliveryTime: '15-25 min'\n        },\n        {\n            id: '13',\n            name: 'Croissants Artisanaux',\n            price: 1500,\n            category: 'Boulangerie',\n            seller: 'Pâtisserie Française',\n            location: 'Dakar',\n            rating: 4.7,\n            image: 'https://images.unsplash.com/photo-1555507036-ab794f4afe5a?w=400',\n            inStock: true,\n            description: 'Croissants pur beurre, préparés selon la tradition française',\n            unit: 'lot de 6',\n            badges: [\n                'Artisanal',\n                'Nouveau'\n            ],\n            isNew: true,\n            distance: 1.5,\n            deliveryTime: '10-20 min'\n        },\n        {\n            id: '14',\n            name: 'Oignons Rouges',\n            price: 900,\n            category: 'Légumes',\n            seller: 'Maraîchers de Niayes',\n            location: 'Niayes',\n            rating: 4.2,\n            image: 'https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=400',\n            inStock: true,\n            description: 'Oignons rouges frais des Niayes, parfaits pour vos plats',\n            unit: 'kg',\n            badges: [\n                'Local',\n                'Frais'\n            ],\n            isNew: false,\n            distance: 3.7,\n            deliveryTime: '25-35 min'\n        },\n        {\n            id: '15',\n            name: 'Œufs de Poules Élevées au Sol',\n            price: 2200,\n            category: 'Volaille',\n            seller: 'Ferme Avicole Naturelle',\n            location: 'Thiès',\n            rating: 4.8,\n            image: 'https://images.unsplash.com/photo-1582722872445-44dc5f7e3c8f?w=400',\n            inStock: true,\n            description: 'Œufs frais de poules élevées au sol, riches en oméga-3',\n            unit: 'douzaine',\n            badges: [\n                'Bio',\n                'Premium'\n            ],\n            isNew: false,\n            isBio: true,\n            distance: 2.8,\n            deliveryTime: '20-30 min'\n        },\n        {\n            id: '30',\n            name: 'Jus de Bissap Artisanal 1L',\n            price: 1500,\n            category: 'Artisanat',\n            seller: 'Boissons Naturelles Dakar',\n            location: 'Dakar',\n            rating: 4.7,\n            image: 'https://images.unsplash.com/photo-1544145945-f90425340c7e?w=400',\n            inStock: true,\n            description: 'Jus de bissap artisanal, préparé selon la tradition sénégalaise avec des fleurs d\\'hibiscus locales',\n            unit: 'bouteille',\n            badges: [\n                'Artisanal',\n                'Traditionnel'\n            ],\n            isNew: false,\n            distance: 2.1,\n            deliveryTime: '15-25 min'\n        },\n        {\n            id: '31',\n            name: 'Confiture de Mangue Artisanale 250g',\n            price: 2800,\n            category: 'Artisanat',\n            seller: 'Confitures de Casamance',\n            location: 'Casamance',\n            rating: 4.8,\n            image: 'https://images.unsplash.com/photo-1484723091739-30a097e8f929?w=400',\n            inStock: true,\n            description: 'Confiture artisanale de mangues Kent, préparée avec des fruits locaux et du sucre de canne',\n            unit: 'pot',\n            badges: [\n                'Artisanal',\n                'Bio',\n                'Local'\n            ],\n            isNew: true,\n            isBio: true,\n            distance: 8.2,\n            deliveryTime: '60-75 min'\n        },\n        {\n            id: '32',\n            name: 'Savon Naturel au Karité 100g',\n            price: 1200,\n            category: 'Artisanat',\n            seller: 'Savonnerie Traditionnelle',\n            location: 'Thiès',\n            rating: 4.9,\n            image: 'https://images.unsplash.com/photo-1556228720-195a672e8a03?w=400',\n            inStock: true,\n            description: 'Savon artisanal au beurre de karité pur, fabriqué selon les méthodes traditionnelles',\n            unit: 'pièce',\n            badges: [\n                'Artisanal',\n                'Bio',\n                'Traditionnel'\n            ],\n            isNew: false,\n            isBio: true,\n            distance: 2.8,\n            deliveryTime: '20-30 min'\n        },\n        {\n            id: '33',\n            name: 'Panier Tressé en Raphia',\n            price: 3500,\n            category: 'Artisanat',\n            seller: 'Artisans de Kaolack',\n            location: 'Kaolack',\n            rating: 4.6,\n            image: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400',\n            inStock: true,\n            description: 'Panier artisanal tressé à la main en raphia naturel, idéal pour les courses ou la décoration',\n            unit: 'pièce',\n            badges: [\n                'Artisanal',\n                'Traditionnel',\n                'Local'\n            ],\n            isNew: true,\n            distance: 5.1,\n            deliveryTime: '40-50 min'\n        },\n        {\n            id: '34',\n            name: 'Jus de Gingembre Frais 500ml',\n            price: 1000,\n            category: 'Artisanat',\n            seller: 'Boissons Naturelles Dakar',\n            location: 'Dakar',\n            rating: 4.5,\n            image: 'https://images.unsplash.com/photo-1570197788417-0e82375c9371?w=400',\n            inStock: true,\n            description: 'Jus de gingembre frais artisanal, énergisant et rafraîchissant, préparé quotidiennement',\n            unit: 'bouteille',\n            badges: [\n                'Artisanal',\n                'Frais'\n            ],\n            isNew: false,\n            distance: 2.1,\n            deliveryTime: '15-25 min'\n        },\n        {\n            id: '35',\n            name: 'Oignons Violets de Galmi 1kg',\n            price: 1200,\n            category: 'Légumes',\n            seller: 'Maraîchers de Galmi',\n            location: 'Galmi, Niger',\n            rating: 4.7,\n            image: 'https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=400',\n            inStock: true,\n            description: 'Oignons violets de Galmi, réputés pour leur saveur douce et leur conservation exceptionnelle',\n            unit: 'kg',\n            badges: [\n                'Premium',\n                'Local'\n            ],\n            isNew: false,\n            distance: 12.5,\n            deliveryTime: '2-3 heures'\n        },\n        {\n            id: '36',\n            name: 'Piments Scotch Bonnet 250g',\n            price: 800,\n            category: 'Légumes',\n            seller: 'Jardin Épicé de Thiès',\n            location: 'Thiès',\n            rating: 4.8,\n            image: 'https://images.unsplash.com/photo-1583454110551-21f2fa2afe61?w=400',\n            inStock: true,\n            description: 'Piments Scotch Bonnet extra forts, parfaits pour relever vos plats traditionnels',\n            unit: 'barquette',\n            weight: '250g',\n            badges: [\n                'Frais',\n                'Local'\n            ],\n            isNew: true,\n            distance: 2.8,\n            deliveryTime: '20-30 min'\n        },\n        {\n            id: '37',\n            name: 'Choux Verts Bio 1 pièce',\n            price: 600,\n            category: 'Légumes',\n            seller: 'Bio Ferme des Niayes',\n            location: 'Niayes',\n            rating: 4.5,\n            image: 'https://images.unsplash.com/photo-1594282486552-05b4d80fbb9f?w=400',\n            inStock: true,\n            description: 'Choux verts biologiques cultivés dans les Niayes, riches en vitamines',\n            unit: 'pièce',\n            badges: [\n                'Bio',\n                'Frais',\n                'Local'\n            ],\n            isNew: false,\n            isBio: true,\n            distance: 3.7,\n            deliveryTime: '25-35 min'\n        },\n        {\n            id: '38',\n            name: 'Carottes Orange 1kg',\n            price: 900,\n            category: 'Légumes',\n            seller: 'Potager de Saint-Louis',\n            location: 'Saint-Louis',\n            rating: 4.6,\n            image: 'https://images.unsplash.com/photo-1445282768818-728615cc910a?w=400',\n            inStock: true,\n            description: 'Carottes orange fraîches, croquantes et sucrées, cultivées dans la vallée du fleuve',\n            unit: 'kg',\n            badges: [\n                'Frais',\n                'Local'\n            ],\n            isNew: false,\n            distance: 4.2,\n            deliveryTime: '30-40 min'\n        },\n        {\n            id: '39',\n            name: 'Aubergines Violettes 500g',\n            price: 1100,\n            category: 'Légumes',\n            seller: 'Jardin de Fatou',\n            location: 'Rufisque',\n            rating: 4.4,\n            image: 'https://images.unsplash.com/photo-1659261200833-ec8761558af7?w=400',\n            inStock: true,\n            description: 'Aubergines violettes fraîches, parfaites pour vos thieboudienne et ragoûts',\n            unit: 'barquette',\n            weight: '500g',\n            badges: [\n                'Frais',\n                'Local'\n            ],\n            isNew: false,\n            distance: 1.8,\n            deliveryTime: '15-25 min'\n        },\n        {\n            id: '40',\n            name: 'Gombo Frais 300g',\n            price: 700,\n            category: 'Légumes',\n            seller: 'Maraîchers de Casamance',\n            location: 'Casamance',\n            rating: 4.9,\n            image: 'https://images.unsplash.com/photo-1631207829628-5d8b3c8b1e3d?w=400',\n            inStock: true,\n            description: 'Gombo frais de Casamance, ingrédient essentiel de la cuisine sénégalaise',\n            unit: 'barquette',\n            weight: '300g',\n            badges: [\n                'Frais',\n                'Traditionnel',\n                'Local'\n            ],\n            isNew: false,\n            distance: 8.2,\n            deliveryTime: '60-75 min'\n        },\n        {\n            id: '41',\n            name: 'Épinards Locaux 250g',\n            price: 500,\n            category: 'Légumes',\n            seller: 'Jardin Vert de Pikine',\n            location: 'Pikine',\n            rating: 4.3,\n            image: 'https://images.unsplash.com/photo-1576045057995-568f588f82fb?w=400',\n            inStock: true,\n            description: 'Épinards locaux frais, riches en fer et vitamines, cultivés sans pesticides',\n            unit: 'botte',\n            weight: '250g',\n            badges: [\n                'Bio',\n                'Frais',\n                'Local'\n            ],\n            isNew: true,\n            isBio: true,\n            distance: 1.5,\n            deliveryTime: '10-20 min'\n        },\n        {\n            id: '42',\n            name: 'Courgettes Vertes 1kg',\n            price: 1300,\n            category: 'Légumes',\n            seller: 'Ferme Maraîchère de Mbour',\n            location: 'Mbour',\n            rating: 4.7,\n            image: 'https://images.unsplash.com/photo-1566385101042-1a0aa0c1268c?w=400',\n            inStock: true,\n            description: 'Courgettes vertes tendres et savoureuses, parfaites pour vos gratins et ratatouilles',\n            unit: 'kg',\n            badges: [\n                'Frais',\n                'Local'\n            ],\n            isNew: false,\n            distance: 3.8,\n            deliveryTime: '30-40 min'\n        },\n        {\n            id: '43',\n            name: 'Oranges Douces de Valencia 2kg',\n            price: 1800,\n            category: 'Fruits',\n            seller: 'Vergers de Casamance',\n            location: 'Casamance',\n            rating: 4.8,\n            image: 'https://images.unsplash.com/photo-1547514701-42782101795e?w=400',\n            inStock: true,\n            description: 'Oranges douces et juteuses de Valencia, riches en vitamine C, cultivées en Casamance',\n            unit: 'kg',\n            weight: '2kg',\n            badges: [\n                'Frais',\n                'Local',\n                'Premium'\n            ],\n            isNew: false,\n            distance: 8.2,\n            deliveryTime: '60-75 min'\n        },\n        {\n            id: '44',\n            name: 'Papayes Mûres 1 pièce',\n            price: 2200,\n            category: 'Fruits',\n            seller: 'Plantation Tropicale du Sud',\n            location: 'Ziguinchor',\n            rating: 4.9,\n            image: 'https://images.unsplash.com/photo-1617112848923-cc2234396a8d?w=400',\n            inStock: true,\n            description: 'Papayes mûres à point, chair orange et sucrée, excellente source d\\'enzymes digestives',\n            unit: 'pièce',\n            weight: '1.5kg',\n            badges: [\n                'Frais',\n                'Premium',\n                'Local'\n            ],\n            isNew: true,\n            distance: 6.8,\n            deliveryTime: '45-60 min'\n        },\n        {\n            id: '45',\n            name: 'Citrons Verts 500g',\n            price: 800,\n            category: 'Fruits',\n            seller: 'Agrumes de Thiès',\n            location: 'Thiès',\n            rating: 4.6,\n            image: 'https://images.unsplash.com/photo-1568702846914-96b305d2aaeb?w=400',\n            inStock: true,\n            description: 'Citrons verts frais et acidulés, parfaits pour vos boissons et marinades',\n            unit: 'barquette',\n            weight: '500g',\n            badges: [\n                'Frais',\n                'Local'\n            ],\n            isNew: false,\n            distance: 2.8,\n            deliveryTime: '20-30 min'\n        },\n        {\n            id: '46',\n            name: 'Pastèques Rouges 1 pièce',\n            price: 3500,\n            category: 'Fruits',\n            seller: 'Ferme de la Vallée',\n            location: 'Saint-Louis',\n            rating: 4.7,\n            image: 'https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=400',\n            inStock: true,\n            description: 'Pastèques rouges sucrées et rafraîchissantes, cultivées dans la vallée du fleuve Sénégal',\n            unit: 'pièce',\n            weight: '4-5kg',\n            badges: [\n                'Frais',\n                'Local',\n                'Premium'\n            ],\n            isNew: false,\n            distance: 4.2,\n            deliveryTime: '30-40 min'\n        },\n        {\n            id: '47',\n            name: 'Goyaves Roses 1kg',\n            price: 1500,\n            category: 'Fruits',\n            seller: 'Vergers Tropicaux',\n            location: 'Casamance',\n            rating: 4.5,\n            image: 'https://images.unsplash.com/photo-1536511132770-e5058c4e1d93?w=400',\n            inStock: true,\n            description: 'Goyaves roses parfumées, riches en vitamine C et antioxydants naturels',\n            unit: 'kg',\n            badges: [\n                'Frais',\n                'Local',\n                'Bio'\n            ],\n            isNew: true,\n            isBio: true,\n            distance: 8.2,\n            deliveryTime: '60-75 min'\n        },\n        {\n            id: '48',\n            name: 'Avocats Hass 4 pièces',\n            price: 2800,\n            category: 'Fruits',\n            seller: 'Plantation d\\'Avocats Bio',\n            location: 'Niayes',\n            rating: 4.8,\n            image: 'https://images.unsplash.com/photo-1523049673857-eb18f1d7b578?w=400',\n            inStock: true,\n            description: 'Avocats Hass biologiques, crémeux et nutritifs, parfaits pour vos salades et smoothies',\n            unit: 'lot de 4',\n            badges: [\n                'Bio',\n                'Premium',\n                'Local'\n            ],\n            isNew: false,\n            isBio: true,\n            distance: 3.7,\n            deliveryTime: '25-35 min'\n        },\n        {\n            id: '49',\n            name: 'Bœuf de Zébu Local 1kg',\n            price: 8500,\n            category: 'Viandes',\n            seller: 'Boucherie Moderne de Dakar',\n            location: 'Dakar',\n            rating: 4.9,\n            image: 'https://images.unsplash.com/photo-1588347818133-38c4106ca7b4?w=400',\n            inStock: true,\n            description: 'Viande de bœuf zébu local, élevé en pâturage naturel, tendre et savoureuse',\n            unit: 'kg',\n            badges: [\n                'Premium',\n                'Local',\n                'Frais'\n            ],\n            isNew: false,\n            distance: 1.2,\n            deliveryTime: '10-20 min'\n        },\n        {\n            id: '50',\n            name: 'Mouton de Tabaski 2kg',\n            price: 12000,\n            category: 'Viandes',\n            seller: 'Élevage Peul de Fouta',\n            location: 'Fouta Toro',\n            rating: 4.8,\n            image: 'https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?w=400',\n            inStock: true,\n            description: 'Viande de mouton de Tabaski, élevé selon les traditions peules, qualité exceptionnelle',\n            unit: 'kg',\n            badges: [\n                'Premium',\n                'Traditionnel',\n                'Local'\n            ],\n            isNew: true,\n            distance: 15.2,\n            deliveryTime: '3-4 heures'\n        },\n        {\n            id: '51',\n            name: 'Agneau Tendre 1kg',\n            price: 9500,\n            category: 'Viandes',\n            seller: 'Bergerie de Thiès',\n            location: 'Thiès',\n            rating: 4.7,\n            image: 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?w=400',\n            inStock: true,\n            description: 'Agneau tendre et savoureux, élevé au grain, parfait pour vos grillades et ragoûts',\n            unit: 'kg',\n            badges: [\n                'Premium',\n                'Frais',\n                'Local'\n            ],\n            isNew: false,\n            distance: 2.8,\n            deliveryTime: '20-30 min'\n        },\n        {\n            id: '52',\n            name: 'Viande Hachée de Bœuf 500g',\n            price: 4500,\n            category: 'Viandes',\n            seller: 'Boucherie Halal Premium',\n            location: 'Dakar',\n            rating: 4.6,\n            image: 'https://images.unsplash.com/photo-1603048297172-c92544798d5a?w=400',\n            inStock: true,\n            description: 'Viande hachée fraîche de bœuf local, idéale pour boulettes, burgers et farces',\n            unit: 'barquette',\n            weight: '500g',\n            badges: [\n                'Frais',\n                'Local'\n            ],\n            isNew: false,\n            distance: 1.5,\n            deliveryTime: '10-20 min'\n        },\n        {\n            id: '53',\n            name: 'Côtes de Bœuf Grillades 1.5kg',\n            price: 11000,\n            category: 'Viandes',\n            seller: 'Boucherie du Marché Kermel',\n            location: 'Dakar',\n            rating: 4.8,\n            image: 'https://images.unsplash.com/photo-1544025162-d76694265947?w=400',\n            inStock: true,\n            description: 'Côtes de bœuf premium, parfaites pour vos grillades et barbecues en famille',\n            unit: 'kg',\n            weight: '1.5kg',\n            badges: [\n                'Premium',\n                'Frais'\n            ],\n            isNew: false,\n            distance: 1.8,\n            deliveryTime: '15-25 min'\n        },\n        {\n            id: '54',\n            name: 'Filet de Bœuf Premium 800g',\n            price: 15000,\n            category: 'Viandes',\n            seller: 'Boucherie de Luxe Almadies',\n            location: 'Almadies',\n            rating: 4.9,\n            image: 'https://images.unsplash.com/photo-1551731409-43eb3e517a1a?w=400',\n            inStock: true,\n            description: 'Filet de bœuf premium, pièce noble et tendre, pour vos occasions spéciales',\n            unit: 'pièce',\n            weight: '800g',\n            badges: [\n                'Premium',\n                'Frais',\n                'Nouveau'\n            ],\n            isNew: true,\n            distance: 5.2,\n            deliveryTime: '35-45 min'\n        }\n    ];\n    // Fonctions utilitaires\n    const formatPrice = (price)=>{\n        return new Intl.NumberFormat('fr-FR', {\n            style: 'currency',\n            currency: 'XOF',\n            minimumFractionDigits: 0\n        }).format(price).replace('XOF', 'FCFA');\n    };\n    const toggleFavorite = (productId)=>{\n        setFavorites((prev)=>{\n            const newFavorites = new Set(prev);\n            if (newFavorites.has(productId)) {\n                newFavorites.delete(productId);\n            } else {\n                newFavorites.add(productId);\n            }\n            return newFavorites;\n        });\n    };\n    const updateCart = (productId, quantity)=>{\n        setCart((prev)=>({\n                ...prev,\n                [productId]: Math.max(0, quantity)\n            }));\n    };\n    const toggleTag = (tagName)=>{\n        setSelectedTags((prev)=>prev.includes(tagName) ? prev.filter((tag)=>tag !== tagName) : [\n                ...prev,\n                tagName\n            ]);\n    };\n    const clearAllFilters = ()=>{\n        setSelectedCategory('all');\n        setSearchQuery('');\n        setSelectedTags([]);\n        setShowOnlyPromo(false);\n        setShowOnlyBio(false);\n        setMinRating(0);\n        setPriceRange([\n            0,\n            50000\n        ]);\n    };\n    const addNotification = (message, type)=>{\n        const id = Date.now().toString();\n        setNotifications((prev)=>[\n                ...prev,\n                {\n                    id,\n                    message,\n                    type\n                }\n            ]);\n        setTimeout(()=>{\n            setNotifications((prev)=>prev.filter((n)=>n.id !== id));\n        }, 3000);\n    };\n    const scrollToTop = ()=>{\n        window.scrollTo({\n            top: 0,\n            behavior: 'smooth'\n        });\n    };\n    // Filtrage et tri\n    const filteredAndSortedProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductsPage.useMemo[filteredAndSortedProducts]\": ()=>{\n            let filtered = products.filter({\n                \"ProductsPage.useMemo[filteredAndSortedProducts].filtered\": (product)=>{\n                    const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) || product.seller.toLowerCase().includes(searchQuery.toLowerCase()) || product.category.toLowerCase().includes(searchQuery.toLowerCase()) || product.description.toLowerCase().includes(searchQuery.toLowerCase());\n                    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;\n                    const matchesPrice = product.price >= priceRange[0] && product.price <= priceRange[1];\n                    const matchesRating = product.rating >= minRating;\n                    const matchesPromo = !showOnlyPromo || product.isPromo;\n                    const matchesBio = !showOnlyBio || product.isBio;\n                    const matchesTags = selectedTags.length === 0 || selectedTags.every({\n                        \"ProductsPage.useMemo[filteredAndSortedProducts].filtered\": (tag)=>{\n                            var _product_badges;\n                            return (_product_badges = product.badges) === null || _product_badges === void 0 ? void 0 : _product_badges.includes(tag);\n                        }\n                    }[\"ProductsPage.useMemo[filteredAndSortedProducts].filtered\"]);\n                    return matchesSearch && matchesCategory && matchesPrice && matchesRating && matchesPromo && matchesBio && matchesTags;\n                }\n            }[\"ProductsPage.useMemo[filteredAndSortedProducts].filtered\"]);\n            filtered.sort({\n                \"ProductsPage.useMemo[filteredAndSortedProducts]\": (a, b)=>{\n                    switch(sortBy){\n                        case 'price':\n                            return (a.promoPrice || a.price) - (b.promoPrice || b.price);\n                        case 'rating':\n                            return (b.rating || 0) - (a.rating || 0);\n                        case 'distance':\n                            return (a.distance || 0) - (b.distance || 0);\n                        default:\n                            return a.name.localeCompare(b.name);\n                    }\n                }\n            }[\"ProductsPage.useMemo[filteredAndSortedProducts]\"]);\n            return filtered;\n        }\n    }[\"ProductsPage.useMemo[filteredAndSortedProducts]\"], [\n        products,\n        searchQuery,\n        selectedCategory,\n        priceRange,\n        minRating,\n        showOnlyPromo,\n        showOnlyBio,\n        sortBy,\n        selectedTags\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductsPage.useEffect\": ()=>{\n            const fetchProducts = {\n                \"ProductsPage.useEffect.fetchProducts\": async ()=>{\n                    setIsLoading(true);\n                    await new Promise({\n                        \"ProductsPage.useEffect.fetchProducts\": (resolve)=>setTimeout(resolve, 1000)\n                    }[\"ProductsPage.useEffect.fetchProducts\"]);\n                    setProducts(demoProducts);\n                    setIsLoading(false);\n                }\n            }[\"ProductsPage.useEffect.fetchProducts\"];\n            fetchProducts();\n        }\n    }[\"ProductsPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductsPage.useEffect\": ()=>{\n            setFilteredProducts(filteredAndSortedProducts);\n        }\n    }[\"ProductsPage.useEffect\"], [\n        filteredAndSortedProducts\n    ]);\n    // Gestion du scroll pour le bouton retour en haut\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductsPage.useEffect\": ()=>{\n            const handleScroll = {\n                \"ProductsPage.useEffect.handleScroll\": ()=>{\n                    setShowScrollTop(window.scrollY > 400);\n                }\n            }[\"ProductsPage.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"ProductsPage.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"ProductsPage.useEffect\"];\n        }\n    }[\"ProductsPage.useEffect\"], []);\n    // Skeleton Loader Component inspiré de TastyDaily\n    const SkeletonLoader = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-900\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative h-80 bg-gradient-to-r from-gray-300 to-gray-400 dark:from-gray-700 dark:to-gray-600 animate-pulse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-black/20\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 924,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-10 h-full flex items-center justify-center text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-4xl mx-auto px-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-16 bg-white/20 rounded-2xl mb-4 animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 927,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 bg-white/20 rounded-xl mb-6 animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 928,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-10 w-32 bg-white/20 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 930,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-10 w-32 bg-white/20 rounded-full animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 931,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 929,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 926,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 925,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                    lineNumber: 923,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"sticky top-0 z-40 bg-white/95 dark:bg-gray-800/95 backdrop-blur-md border-b border-gray-200 dark:border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 py-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col lg:flex-row gap-4 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 h-14 bg-gray-200 dark:bg-gray-700 rounded-2xl animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 941,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-14 w-32 bg-gray-200 dark:bg-gray-700 rounded-2xl animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 943,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-14 w-20 bg-gray-200 dark:bg-gray-700 rounded-2xl animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 944,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 942,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 940,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3 overflow-x-auto pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 949,\n                                        columnNumber: 13\n                                    }, this),\n                                    [\n                                        ...Array(8)\n                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-10 w-24 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse flex-shrink-0\"\n                                        }, i, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 951,\n                                            columnNumber: 15\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 948,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 939,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                    lineNumber: 938,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 py-8\",\n                    children: [\n                        [\n                            ...Array(3)\n                        ].map((_, sectionIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-2xl animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 964,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-6 w-40 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 966,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-4 w-60 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 967,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 965,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 963,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-6 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 970,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 962,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                        children: [\n                                            ...Array(4)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-48 bg-gray-200 dark:bg-gray-700 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 976,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-5 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 978,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-6 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 980,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 981,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 979,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 977,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, i, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 975,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 973,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, sectionIndex, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 961,\n                                columnNumber: 11\n                            }, this)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-48 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 992,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                                    children: [\n                                        ...Array(12)\n                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white dark:bg-gray-800 rounded-3xl shadow-lg overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-56 bg-gray-200 dark:bg-gray-700 animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 996,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-start mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 1000,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-4 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 1001,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 999,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-6 w-12 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 1003,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 998,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1005,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-4 w-3/4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1006,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-8 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 1008,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-10 w-24 bg-gray-200 dark:bg-gray-700 rounded-2xl animate-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 1009,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1007,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 997,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, i, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 995,\n                                            columnNumber: 15\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 993,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 991,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                    lineNumber: 958,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n            lineNumber: 921,\n            columnNumber: 5\n        }, this);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonLoader, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n            lineNumber: 1021,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative h-80 bg-gradient-to-r from-green-600 to-blue-600 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/30\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 1028,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-cover bg-center filter blur-sm\",\n                        style: {\n                            backgroundImage: 'url(https://images.unsplash.com/photo-1542838132-92c53300491e?w=1200)'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 1029,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 h-full flex items-center justify-center text-center text-white\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            className: \"max-w-4xl mx-auto px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl md:text-6xl font-bold mb-4\",\n                                    children: \"\\uD83D\\uDED2 LocaFresh Market\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 1042,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl md:text-2xl opacity-90 mb-6\",\n                                    children: \"D\\xe9couvrez les meilleurs produits locaux pr\\xe8s de chez vous\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 1045,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center gap-4 text-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full\",\n                                            children: [\n                                                \"\\uD83C\\uDF31 \",\n                                                filteredAndSortedProducts.length,\n                                                \" produits frais\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 1049,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full\",\n                                            children: \"\\uD83D\\uDE9A Livraison rapide\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 1052,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 1048,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 1036,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 1035,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                lineNumber: 1027,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                className: \"sticky top-0 z-40 bg-white/95 dark:bg-gray-800/95 backdrop-blur-md border-b border-gray-200 dark:border-gray-700 shadow-lg\",\n                initial: {\n                    opacity: 0,\n                    y: -20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    delay: 0.3\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col lg:flex-row gap-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaSearch, {\n                                            className: \"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 1071,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Rechercher fruits, l\\xe9gumes, viandes, poissons...\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            className: \"w-full pl-12 pr-4 py-4 bg-white dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-2xl focus:outline-none focus:ring-4 focus:ring-green-100 dark:focus:ring-green-900 focus:border-green-400 transition-all duration-300 text-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 1072,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 1070,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: sortBy,\n                                            onChange: (e)=>setSortBy(e.target.value),\n                                            className: \"px-4 py-4 bg-white dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-2xl focus:outline-none focus:ring-2 focus:ring-green-500 text-gray-900 dark:text-white\",\n                                            title: \"Trier par\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"name\",\n                                                    children: \"\\uD83D\\uDCDD Nom\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1089,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"price\",\n                                                    children: \"\\uD83D\\uDCB0 Prix\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1090,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"rating\",\n                                                    children: \"⭐ Note\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1091,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"distance\",\n                                                    children: \"\\uD83D\\uDCCD Distance\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1092,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 1083,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex bg-gray-100 dark:bg-gray-700 rounded-2xl p-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setViewMode('grid'),\n                                                    className: \"px-4 py-3 rounded-xl transition-all duration-300 \".concat(viewMode === 'grid' ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-md' : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'),\n                                                    title: \"Vue grille\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaTh, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1106,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1096,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setViewMode('list'),\n                                                    className: \"px-4 py-3 rounded-xl transition-all duration-300 \".concat(viewMode === 'list' ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-md' : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'),\n                                                    title: \"Vue liste\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaList, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1118,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1108,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 1095,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 1082,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 1069,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 overflow-x-auto pb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-gray-700 dark:text-gray-300 whitespace-nowrap\",\n                                    children: \"Cat\\xe9gories :\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 1126,\n                                    columnNumber: 13\n                                }, this),\n                                locaFreshCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                                        type: \"button\",\n                                        onClick: ()=>setSelectedCategory(category.id),\n                                        className: \"flex items-center gap-2 px-4 py-2 rounded-full whitespace-nowrap transition-all duration-300 \".concat(selectedCategory === category.id ? \"bg-gradient-to-r \".concat(category.color, \" text-white shadow-lg scale-105\") : 'bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'),\n                                        whileHover: {\n                                            scale: selectedCategory === category.id ? 1.05 : 1.02\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg\",\n                                                children: category.emoji\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1140,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: category.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1141,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, category.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 1128,\n                                        columnNumber: 15\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 1125,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3 overflow-x-auto pb-2 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-gray-700 dark:text-gray-300 whitespace-nowrap\",\n                                    children: \"Tags populaires :\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 1148,\n                                    columnNumber: 13\n                                }, this),\n                                popularTags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                                        type: \"button\",\n                                        onClick: ()=>toggleTag(tag.name),\n                                        className: \"flex items-center gap-2 px-3 py-1.5 rounded-full whitespace-nowrap text-sm font-medium transition-all duration-300 \".concat(selectedTags.includes(tag.name) ? \"\".concat(tag.color, \" shadow-md scale-105 ring-2 ring-offset-2 ring-blue-500 dark:ring-offset-gray-800\") : \"\".concat(tag.color, \" hover:shadow-md hover:scale-102 opacity-70 hover:opacity-100\")),\n                                        whileHover: {\n                                            scale: selectedTags.includes(tag.name) ? 1.05 : 1.02\n                                        },\n                                        whileTap: {\n                                            scale: 0.98\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: tag.emoji\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1162,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: tag.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1163,\n                                                columnNumber: 17\n                                            }, this),\n                                            selectedTags.includes(tag.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.span, {\n                                                initial: {\n                                                    scale: 0\n                                                },\n                                                animate: {\n                                                    scale: 1\n                                                },\n                                                className: \"text-xs\",\n                                                children: \"✓\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1165,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, tag.name, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 1150,\n                                        columnNumber: 15\n                                    }, this)),\n                                (selectedTags.length > 0 || selectedCategory !== 'all' || searchQuery || showOnlyPromo || showOnlyBio || minRating > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                                    type: \"button\",\n                                    onClick: clearAllFilters,\n                                    className: \"flex items-center gap-2 px-3 py-1.5 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-all duration-300\",\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaTimes, {\n                                            className: \"w-3 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 1185,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Effacer tout\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 1186,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 1178,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 1147,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mt-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setShowFilters(!showFilters),\n                                        className: \"flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-xl transition-all duration-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaFilter, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1199,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden sm:inline\",\n                                                children: \"Filtres\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1200,\n                                                columnNumber: 17\n                                            }, this),\n                                            (showOnlyPromo || showOnlyBio || minRating > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-red-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1202,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 1194,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: filteredAndSortedProducts.length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1207,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" produit\",\n                                            filteredAndSortedProducts.length > 1 ? 's' : '',\n                                            \" trouv\\xe9\",\n                                            filteredAndSortedProducts.length > 1 ? 's' : ''\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 1206,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 1193,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 1192,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                            children: showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    height: 0\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    height: 'auto'\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    height: 0\n                                },\n                                transition: {\n                                    duration: 0.3\n                                },\n                                className: \"mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                    children: [\n                                                        \"Prix maximum: \",\n                                                        priceRange[1].toLocaleString(),\n                                                        \" FCFA\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1224,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"range\",\n                                                    min: \"0\",\n                                                    max: \"50000\",\n                                                    value: priceRange[1],\n                                                    onChange: (e)=>setPriceRange([\n                                                            0,\n                                                            Number(e.target.value)\n                                                        ]),\n                                                    className: \"w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1227,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 1223,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                                    children: [\n                                                        \"Note minimum: \",\n                                                        minRating,\n                                                        \"⭐\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1237,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"range\",\n                                                    min: \"0\",\n                                                    max: \"5\",\n                                                    step: \"0.5\",\n                                                    value: minRating,\n                                                    onChange: (e)=>setMinRating(Number(e.target.value)),\n                                                    className: \"w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1240,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 1236,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: showOnlyPromo,\n                                                            onChange: (e)=>setShowOnlyPromo(e.target.checked),\n                                                            className: \"rounded border-gray-300 text-green-600 focus:ring-green-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1252,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 text-sm text-gray-700 dark:text-gray-300\",\n                                                            children: \"\\uD83C\\uDFF7️ Promotions uniquement\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1258,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1251,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: showOnlyBio,\n                                                            onChange: (e)=>setShowOnlyBio(e.target.checked),\n                                                            className: \"rounded border-gray-300 text-green-600 focus:ring-green-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1261,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 text-sm text-gray-700 dark:text-gray-300\",\n                                                            children: \"\\uD83C\\uDF31 Bio uniquement\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1267,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1260,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 1250,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 1222,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 1215,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 1213,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                    lineNumber: 1067,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                lineNumber: 1061,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.section, {\n                        className: \"mb-12\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.4\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-r from-red-500 to-orange-500 rounded-2xl flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaFire, {\n                                                    className: \"text-white text-xl\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1289,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1288,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: \"Offres du jour\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1292,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                        children: \"Profitez de nos promotions exceptionnelles\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1293,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1291,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 1287,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/fr/products?filter=promo\",\n                                        className: \"text-blue-600 hover:text-blue-700 font-medium\",\n                                        children: \"Voir tout →\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 1296,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 1286,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                children: filteredAndSortedProducts.filter((p)=>p.isPromo).slice(0, 4).map((product, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        className: \"group bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 border border-red-100 dark:border-red-900\",\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.9\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            delay: index * 0.1\n                                        },\n                                        whileHover: {\n                                            y: -4\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/fr/products/\".concat(product.id),\n                                            className: \"block\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative h-48 overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            src: product.image,\n                                                            alt: product.name,\n                                                            fill: true,\n                                                            className: \"object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1313,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-3 left-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg\",\n                                                                children: [\n                                                                    \"\\uD83C\\uDFF7️ -\",\n                                                                    Math.round((product.price - (product.promoPrice || product.price)) / product.price * 100),\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1320,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1319,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: (e)=>{\n                                                                e.preventDefault();\n                                                                e.stopPropagation();\n                                                                toggleFavorite(product.id);\n                                                            },\n                                                            className: \"absolute top-3 right-3 w-8 h-8 rounded-full shadow-lg transition-all duration-300 \".concat(favorites.has(product.id) ? 'bg-red-500 text-white' : 'bg-white/90 text-gray-600 hover:bg-red-500 hover:text-white'),\n                                                            title: favorites.has(product.id) ? 'Retirer des favoris' : 'Ajouter aux favoris',\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaHeart, {\n                                                                className: \"w-3 h-3 mx-auto\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1338,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1324,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1312,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-bold text-gray-900 dark:text-white mb-2 line-clamp-1\",\n                                                            children: product.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1343,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg font-bold text-red-600 dark:text-red-400\",\n                                                                            children: formatPrice(product.promoPrice || product.price)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 1346,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        product.promoPrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-500 line-through\",\n                                                                            children: formatPrice(product.price)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 1350,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 1345,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: (e)=>{\n                                                                        e.preventDefault();\n                                                                        e.stopPropagation();\n                                                                        updateCart(product.id, (cart[product.id] || 0) + 1);\n                                                                        addNotification(\"\".concat(product.name, \" ajout\\xe9 au panier\"), 'success');\n                                                                    },\n                                                                    className: \"w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-full flex items-center justify-center hover:from-green-600 hover:to-emerald-600 transition-all duration-300\",\n                                                                    title: \"Ajouter au panier\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaPlus, {\n                                                                        className: \"w-3 h-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 1366,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 1355,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1344,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1342,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 1311,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, \"promo-\".concat(product.id), false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 1303,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 1301,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 1280,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.section, {\n                        className: \"mb-12\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.5\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-xl\",\n                                                    children: \"\\uD83D\\uDD25\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1386,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1385,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: \"Produits populaires\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1389,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                        children: \"Les plus appr\\xe9ci\\xe9s par nos clients\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1390,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1388,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 1384,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/fr/products?sort=rating\",\n                                        className: \"text-blue-600 hover:text-blue-700 font-medium\",\n                                        children: \"Voir tout →\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 1393,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 1383,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 gap-6\",\n                                children: filteredAndSortedProducts.filter((p)=>p.rating >= 4.5).slice(0, 6).map((product, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        className: \"group bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500 border border-orange-100 dark:border-orange-900\",\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.9\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            delay: index * 0.1\n                                        },\n                                        whileHover: {\n                                            y: -8,\n                                            scale: 1.02\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative h-48 md:h-56 overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        src: product.image,\n                                                        alt: product.name,\n                                                        fill: true,\n                                                        className: \"object-cover group-hover:scale-110 transition-transform duration-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1412,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-3 left-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg\",\n                                                            children: [\n                                                                \"⭐ \",\n                                                                product.rating\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1419,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1418,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>toggleFavorite(product.id),\n                                                        className: \"absolute top-3 right-3 w-8 h-8 rounded-full shadow-lg transition-all duration-300 \".concat(favorites.has(product.id) ? 'bg-red-500 text-white' : 'bg-white/90 text-gray-600 hover:bg-red-500 hover:text-white'),\n                                                        title: favorites.has(product.id) ? 'Retirer des favoris' : 'Ajouter aux favoris',\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaHeart, {\n                                                            className: \"w-3 h-3 mx-auto\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1433,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1423,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1411,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-bold text-gray-900 dark:text-white mb-2 line-clamp-1\",\n                                                        children: product.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1438,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-600 dark:text-gray-400 mb-3 line-clamp-2\",\n                                                        children: product.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1439,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col\",\n                                                                children: product.isPromo && product.promoPrice ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg font-bold text-red-600 dark:text-red-400\",\n                                                                            children: formatPrice(product.promoPrice)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 1444,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-500 line-through\",\n                                                                            children: formatPrice(product.price)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 1447,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg font-bold text-gray-900 dark:text-white\",\n                                                                    children: formatPrice(product.price)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 1452,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1441,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>{\n                                                                    updateCart(product.id, (cart[product.id] || 0) + 1);\n                                                                    addNotification(\"\".concat(product.name, \" ajout\\xe9 au panier\"), 'success');\n                                                                },\n                                                                className: \"px-4 py-2 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-xl text-sm font-medium hover:from-orange-600 hover:to-red-600 transition-all duration-300 shadow-md hover:shadow-lg\",\n                                                                title: \"Ajouter au panier\",\n                                                                children: \"Ajouter\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1457,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1440,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1437,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, \"popular-\".concat(product.id), true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 1403,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 1398,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 1377,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.section, {\n                        className: \"mb-12\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.6\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-xl\",\n                                                    children: \"\\uD83E\\uDD6C\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1485,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1484,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: \"L\\xe9gumes Frais du Jour\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1488,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                        children: \"Fra\\xeecheur garantie, directement des producteurs locaux\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1489,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1487,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 1483,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/fr/products?category=L\\xe9gumes\",\n                                        className: \"text-blue-600 hover:text-blue-700 font-medium\",\n                                        children: \"Voir tout →\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 1492,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 1482,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                children: filteredAndSortedProducts.filter((p)=>p.category === 'Légumes').slice(0, 8).map((product, index)=>{\n                                    var _product_badges;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        className: \"group bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500 border border-green-100 dark:border-green-900\",\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.9\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            delay: index * 0.1\n                                        },\n                                        whileHover: {\n                                            y: -8,\n                                            scale: 1.02\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/fr/products/\".concat(product.id),\n                                            className: \"block\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative h-48 overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            src: product.image,\n                                                            alt: product.name,\n                                                            fill: true,\n                                                            className: \"object-cover group-hover:scale-110 transition-transform duration-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1512,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        product.isNew && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-3 left-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-gradient-to-r from-green-500 to-emerald-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg\",\n                                                                children: \"✨ Nouveau\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1520,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1519,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        product.isBio && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-3 left-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-gradient-to-r from-green-600 to-green-700 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg\",\n                                                                children: \"\\uD83C\\uDF31 Bio\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1527,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1526,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: (e)=>{\n                                                                e.preventDefault();\n                                                                e.stopPropagation();\n                                                                toggleFavorite(product.id);\n                                                            },\n                                                            className: \"absolute top-3 right-3 w-8 h-8 rounded-full shadow-lg transition-all duration-300 \".concat(favorites.has(product.id) ? 'bg-red-500 text-white' : 'bg-white/90 text-gray-600 hover:bg-red-500 hover:text-white'),\n                                                            title: favorites.has(product.id) ? 'Retirer des favoris' : 'Ajouter aux favoris',\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaHeart, {\n                                                                className: \"w-3 h-3 mx-auto\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1546,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1532,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1511,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-start mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-bold text-gray-900 dark:text-white mb-1 line-clamp-1\",\n                                                                            children: product.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 1553,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                            children: product.seller\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 1554,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 1552,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-1 bg-yellow-100 dark:bg-yellow-900 px-2 py-1 rounded-full\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaStar, {\n                                                                            className: \"w-3 h-3 text-yellow-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 1557,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs font-medium text-yellow-700 dark:text-yellow-300\",\n                                                                            children: product.rating\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 1558,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 1556,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1551,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-600 dark:text-gray-400 mb-3 line-clamp-2\",\n                                                            children: product.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1562,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-1 mb-3\",\n                                                            children: (_product_badges = product.badges) === null || _product_badges === void 0 ? void 0 : _product_badges.slice(0, 2).map((badge)=>{\n                                                                var _popularTags_find;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs px-2 py-1 rounded-full \".concat(((_popularTags_find = popularTags.find((tag)=>tag.name === badge)) === null || _popularTags_find === void 0 ? void 0 : _popularTags_find.color) || 'bg-gray-100 text-gray-800'),\n                                                                    children: badge\n                                                                }, badge, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 1566,\n                                                                    columnNumber: 25\n                                                                }, this);\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1564,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg font-bold text-green-600 dark:text-green-400\",\n                                                                            children: formatPrice(product.price)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 1579,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: [\n                                                                                \"par \",\n                                                                                product.unit\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 1582,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 1578,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: (e)=>{\n                                                                        e.preventDefault();\n                                                                        e.stopPropagation();\n                                                                        updateCart(product.id, (cart[product.id] || 0) + 1);\n                                                                        addNotification(\"\".concat(product.name, \" ajout\\xe9 au panier\"), 'success');\n                                                                    },\n                                                                    className: \"px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-xl text-sm font-medium hover:from-green-600 hover:to-emerald-600 transition-all duration-300 shadow-md hover:shadow-lg\",\n                                                                    title: \"Ajouter au panier\",\n                                                                    children: \"Ajouter\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 1584,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1577,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1550,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 1510,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, \"vegetables-\".concat(product.id), false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 1502,\n                                        columnNumber: 15\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 1497,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 1476,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.section, {\n                        className: \"mb-12\",\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.7\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-xl\",\n                                                    children: \"\\uD83C\\uDF4E\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1615,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1614,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                                        children: \"Fruits Tropicaux du Jour\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1618,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 dark:text-gray-400\",\n                                                        children: \"Saveurs exotiques et vitamines naturelles, directement des vergers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1619,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1617,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 1613,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: \"/fr/products?category=Fruits\",\n                                        className: \"text-blue-600 hover:text-blue-700 font-medium\",\n                                        children: \"Voir tout →\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 1622,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 1612,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                children: filteredAndSortedProducts.filter((p)=>p.category === 'Fruits').slice(0, 8).map((product, index)=>{\n                                    var _product_badges;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        className: \"group bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500 border border-orange-100 dark:border-orange-900\",\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.9\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            delay: index * 0.1\n                                        },\n                                        whileHover: {\n                                            y: -8,\n                                            scale: 1.02\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/fr/products/\".concat(product.id),\n                                            className: \"block\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative h-48 overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            src: product.image,\n                                                            alt: product.name,\n                                                            fill: true,\n                                                            className: \"object-cover group-hover:scale-110 transition-transform duration-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1642,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        product.isNew && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-3 left-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg\",\n                                                                children: \"✨ Nouveau\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1650,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1649,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        product.isBio && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-3 left-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-gradient-to-r from-green-600 to-green-700 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg\",\n                                                                children: \"\\uD83C\\uDF31 Bio\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1657,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1656,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: (e)=>{\n                                                                e.preventDefault();\n                                                                e.stopPropagation();\n                                                                toggleFavorite(product.id);\n                                                            },\n                                                            className: \"absolute top-3 right-3 w-8 h-8 rounded-full shadow-lg transition-all duration-300 \".concat(favorites.has(product.id) ? 'bg-red-500 text-white' : 'bg-white/90 text-gray-600 hover:bg-red-500 hover:text-white'),\n                                                            title: favorites.has(product.id) ? 'Retirer des favoris' : 'Ajouter aux favoris',\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaHeart, {\n                                                                className: \"w-3 h-3 mx-auto\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1676,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1662,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1641,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-start mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-bold text-gray-900 dark:text-white mb-1 line-clamp-1\",\n                                                                            children: product.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 1683,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                            children: product.seller\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 1684,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 1682,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-1 bg-yellow-100 dark:bg-yellow-900 px-2 py-1 rounded-full\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaStar, {\n                                                                            className: \"w-3 h-3 text-yellow-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 1687,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs font-medium text-yellow-700 dark:text-yellow-300\",\n                                                                            children: product.rating\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 1688,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 1686,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1681,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-600 dark:text-gray-400 mb-3 line-clamp-2\",\n                                                            children: product.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1692,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-1 mb-3\",\n                                                            children: (_product_badges = product.badges) === null || _product_badges === void 0 ? void 0 : _product_badges.slice(0, 2).map((badge)=>{\n                                                                var _popularTags_find;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs px-2 py-1 rounded-full \".concat(((_popularTags_find = popularTags.find((tag)=>tag.name === badge)) === null || _popularTags_find === void 0 ? void 0 : _popularTags_find.color) || 'bg-gray-100 text-gray-800'),\n                                                                    children: badge\n                                                                }, badge, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 1696,\n                                                                    columnNumber: 25\n                                                                }, this);\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1694,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg font-bold text-orange-600 dark:text-orange-400\",\n                                                                            children: formatPrice(product.price)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 1709,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: [\n                                                                                \"par \",\n                                                                                product.unit\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 1712,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 1708,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: (e)=>{\n                                                                        e.preventDefault();\n                                                                        e.stopPropagation();\n                                                                        updateCart(product.id, (cart[product.id] || 0) + 1);\n                                                                        addNotification(\"\".concat(product.name, \" ajout\\xe9 au panier\"), 'success');\n                                                                    },\n                                                                    className: \"px-4 py-2 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-xl text-sm font-medium hover:from-orange-600 hover:to-red-600 transition-all duration-300 shadow-md hover:shadow-lg\",\n                                                                    title: \"Ajouter au panier\",\n                                                                    children: \"Ajouter\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 1714,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1707,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                    lineNumber: 1680,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 1640,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, \"fruits-\".concat(product.id), false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 1632,\n                                        columnNumber: 15\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 1627,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 1606,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.section, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.8\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                            children: \"Tous nos produits\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 1743,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-400\",\n                                            children: \"D\\xe9couvrez notre s\\xe9lection compl\\xe8te\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                            lineNumber: 1744,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 1742,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 1741,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                                children: filteredAndSortedProducts.map((product, index)=>{\n                                    var _product_badges;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        className: \"group bg-white dark:bg-gray-800 rounded-3xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500\",\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.9\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            delay: index * 0.05\n                                        },\n                                        whileHover: {\n                                            y: -8\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative h-56 overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        src: product.image,\n                                                        alt: product.name,\n                                                        fill: true,\n                                                        className: \"object-cover group-hover:scale-110 transition-transform duration-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1759,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    product.isPromo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-3 left-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg\",\n                                                            children: [\n                                                                \"\\uD83C\\uDFF7️ -\",\n                                                                Math.round((product.price - (product.promoPrice || product.price)) / product.price * 100),\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1767,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1766,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    product.isNew && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-3 left-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-gradient-to-r from-green-500 to-emerald-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg\",\n                                                            children: \"✨ Nouveau\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1774,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1773,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>toggleFavorite(product.id),\n                                                        className: \"absolute top-3 right-3 w-10 h-10 rounded-full shadow-lg transition-all duration-300 \".concat(favorites.has(product.id) ? 'bg-red-500 text-white' : 'bg-white/90 text-gray-600 hover:bg-red-500 hover:text-white'),\n                                                        title: favorites.has(product.id) ? 'Retirer des favoris' : 'Ajouter aux favoris',\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaHeart, {\n                                                            className: \"w-4 h-4 mx-auto\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                            lineNumber: 1789,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1779,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1758,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-bold text-gray-900 dark:text-white mb-1 line-clamp-1\",\n                                                                        children: product.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 1796,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                        children: product.seller\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 1797,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1795,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1 bg-yellow-100 dark:bg-yellow-900 px-2 py-1 rounded-full\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaStar, {\n                                                                        className: \"w-3 h-3 text-yellow-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 1800,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs font-medium text-yellow-700 dark:text-yellow-300\",\n                                                                        children: product.rating\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 1801,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1799,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1794,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-2\",\n                                                        children: product.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1805,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1 mb-4\",\n                                                        children: (_product_badges = product.badges) === null || _product_badges === void 0 ? void 0 : _product_badges.slice(0, 2).map((badge)=>{\n                                                            var _popularTags_find;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs px-2 py-1 rounded-full \".concat(((_popularTags_find = popularTags.find((tag)=>tag.name === badge)) === null || _popularTags_find === void 0 ? void 0 : _popularTags_find.color) || 'bg-gray-100 text-gray-800'),\n                                                                children: badge\n                                                            }, badge, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1809,\n                                                                columnNumber: 23\n                                                            }, this);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1807,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col\",\n                                                                children: [\n                                                                    product.isPromo && product.promoPrice ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xl font-bold text-red-600 dark:text-red-400\",\n                                                                                children: formatPrice(product.promoPrice)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 1824,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm text-gray-500 line-through\",\n                                                                                children: formatPrice(product.price)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 1827,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xl font-bold text-gray-900 dark:text-white\",\n                                                                        children: formatPrice(product.price)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 1832,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: [\n                                                                            \"par \",\n                                                                            product.unit\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 1836,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1821,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    cart[product.id] > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2 bg-gray-100 dark:bg-gray-700 rounded-xl px-3 py-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>updateCart(product.id, cart[product.id] - 1),\n                                                                                className: \"w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaMinus, {\n                                                                                    className: \"w-2 h-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                                    lineNumber: 1847,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 1842,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-gray-900 dark:text-white min-w-[20px] text-center\",\n                                                                                children: cart[product.id]\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 1849,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                type: \"button\",\n                                                                                onClick: ()=>updateCart(product.id, cart[product.id] + 1),\n                                                                                className: \"w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center hover:bg-green-600 transition-colors\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaPlus, {\n                                                                                    className: \"w-2 h-2\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                                    lineNumber: 1857,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                                lineNumber: 1852,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 1841,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: ()=>{\n                                                                            updateCart(product.id, (cart[product.id] || 0) + 1);\n                                                                            addNotification(\"\".concat(product.name, \" ajout\\xe9 au panier\"), 'success');\n                                                                        },\n                                                                        className: \"px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-2xl font-medium hover:from-green-600 hover:to-emerald-600 transition-all duration-300 shadow-md hover:shadow-lg\",\n                                                                        title: \"Ajouter au panier\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaShoppingCart, {\n                                                                            className: \"w-4 h-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                            lineNumber: 1871,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                        lineNumber: 1862,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                                lineNumber: 1839,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                        lineNumber: 1820,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                                lineNumber: 1793,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, product.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                        lineNumber: 1750,\n                                        columnNumber: 15\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                lineNumber: 1748,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 1736,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                lineNumber: 1278,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: 300\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            x: 300\n                        },\n                        className: \"fixed top-4 right-4 z-50 px-6 py-4 rounded-2xl shadow-lg \".concat(notification.type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl\",\n                                    children: notification.type === 'success' ? '✅' : '❌'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 1897,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: notification.message\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 1900,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 1896,\n                            columnNumber: 13\n                        }, this)\n                    }, notification.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 1885,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                lineNumber: 1883,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.AnimatePresence, {\n                children: showScrollTop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.button, {\n                    type: \"button\",\n                    initial: {\n                        opacity: 0,\n                        scale: 0\n                    },\n                    animate: {\n                        opacity: 1,\n                        scale: 1\n                    },\n                    exit: {\n                        opacity: 0,\n                        scale: 0\n                    },\n                    onClick: scrollToTop,\n                    className: \"fixed bottom-8 right-8 z-40 w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center\",\n                    title: \"Retour en haut\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaArrowRight_FaFilter_FaFire_FaHeart_FaList_FaMinus_FaPlus_FaSearch_FaShoppingCart_FaStar_FaTh_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_7__.FaArrowRight, {\n                        className: \"w-4 h-4 transform -rotate-90\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 1918,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                    lineNumber: 1909,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                lineNumber: 1907,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n        lineNumber: 1025,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductsPage, \"V+Jl/P+ygj6BT4LsLoLG6xnxcC8=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_5__.useTranslations,\n        _contexts_OfflineModeContext__WEBPACK_IMPORTED_MODULE_4__.useOfflineMode\n    ];\n});\n_c = ProductsPage;\nvar _c;\n$RefreshReg$(_c, \"ProductsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/products/page.tsx\n"));

/***/ })

});