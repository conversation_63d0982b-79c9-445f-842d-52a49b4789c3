"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/products/page",{

/***/ "(app-pages-browser)/./app/[locale]/products/page.tsx":
/*!****************************************!*\
  !*** ./app/[locale]/products/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/esm/development/react-client/index.js\");\n/* harmony import */ var _contexts_OfflineModeContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/OfflineModeContext */ \"(app-pages-browser)/./contexts/OfflineModeContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ProductsPage() {\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_3__.useTranslations)();\n    const { isOnline, offlineData } = (0,_contexts_OfflineModeContext__WEBPACK_IMPORTED_MODULE_2__.useOfflineMode)();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredProducts, setFilteredProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('grid');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('name');\n    const [priceRange, setPriceRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        0,\n        50000\n    ]);\n    const [minRating, setMinRating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showOnlyPromo, setShowOnlyPromo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showOnlyBio, setShowOnlyBio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [favorites, setFavorites] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [cart, setCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [showScrollTop, setShowScrollTop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTags, setSelectedTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showSuggestions, setShowSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const productsPerPage = 12;\n    // Tags populaires inspirés de TastyDaily\n    const popularTags = [\n        {\n            name: 'Bio',\n            emoji: '🌱',\n            color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'\n        },\n        {\n            name: 'Promo',\n            emoji: '🏷️',\n            color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'\n        },\n        {\n            name: 'Local',\n            emoji: '📍',\n            color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'\n        },\n        {\n            name: 'Frais',\n            emoji: '❄️',\n            color: 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-200'\n        },\n        {\n            name: 'Premium',\n            emoji: '⭐',\n            color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'\n        },\n        {\n            name: 'Traditionnel',\n            emoji: '🏛️',\n            color: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'\n        },\n        {\n            name: 'Artisanal',\n            emoji: '🎨',\n            color: 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200'\n        },\n        {\n            name: 'Nouveau',\n            emoji: '✨',\n            color: 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200'\n        }\n    ];\n    // Catégories LocaFresh avec emojis - Inspiré de TastyDaily\n    const locaFreshCategories = [\n        {\n            id: 'all',\n            name: 'Tout',\n            emoji: '🛒',\n            color: 'from-gray-400 to-gray-500'\n        },\n        {\n            id: 'Fruits',\n            name: 'Fruits',\n            emoji: '🍎',\n            color: 'from-red-400 to-orange-500'\n        },\n        {\n            id: 'Légumes',\n            name: 'Légumes',\n            emoji: '🥬',\n            color: 'from-green-400 to-green-500'\n        },\n        {\n            id: 'Viandes',\n            name: 'Viandes',\n            emoji: '🥩',\n            color: 'from-red-500 to-red-600'\n        },\n        {\n            id: 'Volaille',\n            name: 'Volaille',\n            emoji: '🐔',\n            color: 'from-yellow-400 to-orange-500'\n        },\n        {\n            id: 'Poissons',\n            name: 'Poissons',\n            emoji: '🐟',\n            color: 'from-blue-400 to-blue-500'\n        },\n        {\n            id: 'Boulangerie',\n            name: 'Boulangerie',\n            emoji: '🍞',\n            color: 'from-amber-400 to-amber-500'\n        },\n        {\n            id: 'Boissons',\n            name: 'Boissons',\n            emoji: '🥤',\n            color: 'from-cyan-400 to-blue-500'\n        },\n        {\n            id: 'Artisanat',\n            name: 'Artisanat',\n            emoji: '🎨',\n            color: 'from-purple-400 to-pink-500'\n        }\n    ];\n    // Données de démonstration enrichies pour LocaFresh - 27 produits variés\n    const demoProducts = [\n        {\n            id: '1',\n            name: 'Mangues Bio Kent',\n            price: 2500,\n            category: 'Fruits',\n            seller: 'Ferme Bio Diallo',\n            location: 'Thiès',\n            rating: 4.8,\n            image: 'https://images.unsplash.com/photo-1553279768-865429fa0078?w=400',\n            inStock: true,\n            description: 'Mangues biologiques fraîches, cultivées sans pesticides',\n            unit: 'kg',\n            weight: '1kg',\n            isPromo: true,\n            promoPrice: 2000,\n            badges: [\n                'Bio',\n                'Promo',\n                'Local'\n            ],\n            isNew: false,\n            isBio: true,\n            distance: 2.5,\n            deliveryTime: '20-30 min'\n        },\n        {\n            id: '2',\n            name: 'Tomates Cerises Bio',\n            price: 1800,\n            category: 'Légumes',\n            seller: 'Jardin de Fatou',\n            location: 'Rufisque',\n            rating: 4.6,\n            image: 'https://images.unsplash.com/photo-1546470427-e5b89b618b84?w=400',\n            inStock: true,\n            description: 'Tomates cerises fraîches du jour, cultivées en agriculture biologique',\n            unit: 'barquette',\n            weight: '250g',\n            badges: [\n                'Bio',\n                'Frais',\n                'Local'\n            ],\n            isNew: true,\n            isBio: true,\n            distance: 1.8,\n            deliveryTime: '15-25 min'\n        },\n        {\n            id: '3',\n            name: 'Pain Traditionnel au Feu de Bois',\n            price: 500,\n            category: 'Boulangerie',\n            seller: 'Boulangerie Artisanale',\n            location: 'Dakar',\n            rating: 4.9,\n            image: 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400',\n            inStock: true,\n            description: 'Pain traditionnel sénégalais cuit au feu de bois selon la recette ancestrale',\n            unit: 'pièce',\n            badges: [\n                'Artisanal',\n                'Traditionnel'\n            ],\n            isNew: false,\n            distance: 1.2,\n            deliveryTime: '10-20 min'\n        },\n        {\n            id: '4',\n            name: 'Bissap Artisanal aux Épices',\n            price: 1200,\n            category: 'Boissons',\n            seller: 'Les Délices de Khadija',\n            location: 'Saint-Louis',\n            rating: 4.7,\n            image: 'https://images.unsplash.com/photo-1622597467836-f3285f2131b8?w=400',\n            inStock: true,\n            description: 'Bissap artisanal aux épices naturelles, sans conservateurs',\n            unit: 'bouteille',\n            weight: '500ml',\n            badges: [\n                'Artisanal',\n                'Traditionnel'\n            ],\n            isNew: false,\n            distance: 4.2,\n            deliveryTime: '30-40 min'\n        },\n        {\n            id: '5',\n            name: 'Thiof Frais du Matin',\n            price: 3500,\n            category: 'Poissons',\n            seller: 'Pêcheurs de Soumbédioune',\n            location: 'Dakar',\n            rating: 4.5,\n            image: 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=400',\n            inStock: true,\n            description: 'Thiof fraîchement pêché ce matin, qualité premium',\n            unit: 'kg',\n            badges: [\n                'Frais',\n                'Premium',\n                'Local'\n            ],\n            isNew: false,\n            distance: 3.2,\n            deliveryTime: '25-35 min'\n        },\n        {\n            id: '6',\n            name: 'Sac Artisanal en Raphia',\n            price: 8000,\n            category: 'Artisanat',\n            seller: 'Atelier Sénégal Authentique',\n            location: 'Kaolack',\n            rating: 4.8,\n            image: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400',\n            inStock: true,\n            description: 'Sac artisanal en raphia tressé à la main, design traditionnel',\n            unit: 'pièce',\n            badges: [\n                'Artisanal',\n                'Traditionnel'\n            ],\n            isNew: true,\n            distance: 5.1,\n            deliveryTime: '40-50 min'\n        },\n        {\n            id: '7',\n            name: 'Bananes Bio Plantain',\n            price: 1500,\n            category: 'Fruits',\n            seller: 'Coopérative Fruits Bio',\n            location: 'Ziguinchor',\n            rating: 4.4,\n            image: 'https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=400',\n            inStock: true,\n            description: 'Bananes plantain biologiques, parfaites pour la cuisine',\n            unit: 'régime',\n            weight: '2kg',\n            isPromo: true,\n            promoPrice: 1200,\n            badges: [\n                'Bio',\n                'Promo'\n            ],\n            isNew: false,\n            isBio: true,\n            distance: 6.8,\n            deliveryTime: '45-60 min'\n        },\n        {\n            id: '8',\n            name: 'Poulet Fermier Bio',\n            price: 12000,\n            category: 'Volaille',\n            seller: 'Ferme Avicole Bio',\n            location: 'Mbour',\n            rating: 4.7,\n            image: 'https://images.unsplash.com/photo-1604503468506-a8da13d82791?w=400',\n            inStock: true,\n            description: 'Poulet fermier élevé en liberté, nourri aux grains bio',\n            unit: 'kg',\n            badges: [\n                'Bio',\n                'Premium'\n            ],\n            isNew: false,\n            isBio: true,\n            distance: 3.8,\n            deliveryTime: '30-40 min'\n        },\n        {\n            id: '9',\n            name: 'Ananas Victoria',\n            price: 3000,\n            category: 'Fruits',\n            seller: 'Plantation Tropicale',\n            location: 'Casamance',\n            rating: 4.9,\n            image: 'https://images.unsplash.com/photo-1550258987-190a2d41a8ba?w=400',\n            inStock: true,\n            description: 'Ananas Victoria extra sucré, cultivé en Casamance',\n            unit: 'pièce',\n            weight: '1.5kg',\n            badges: [\n                'Premium',\n                'Local'\n            ],\n            isNew: false,\n            distance: 8.2,\n            deliveryTime: '60-75 min'\n        },\n        {\n            id: '10',\n            name: 'Crevettes Fraîches',\n            price: 8500,\n            category: 'Poissons',\n            seller: 'Pêcheurs de Joal',\n            location: 'Joal-Fadiouth',\n            rating: 4.6,\n            image: 'https://images.unsplash.com/photo-1565680018434-b513d5e5fd47?w=400',\n            inStock: true,\n            description: 'Crevettes fraîches pêchées dans les eaux sénégalaises',\n            unit: 'kg',\n            badges: [\n                'Frais',\n                'Premium'\n            ],\n            isNew: true,\n            distance: 4.5,\n            deliveryTime: '35-45 min'\n        },\n        {\n            id: '11',\n            name: 'Miel Pur Local 500g',\n            price: 4500,\n            category: 'Artisanat',\n            seller: 'Apiculteurs de Casamance',\n            location: 'Casamance',\n            rating: 4.9,\n            image: 'https://images.unsplash.com/photo-1587049352846-4a222e784d38?w=400',\n            inStock: true,\n            description: 'Miel pur et naturel récolté dans les ruches traditionnelles',\n            unit: 'pot',\n            weight: '500g',\n            badges: [\n                'Traditionnel',\n                'Local'\n            ],\n            isNew: false,\n            distance: 8.2,\n            deliveryTime: '60-75 min'\n        },\n        {\n            id: '12',\n            name: 'Jus de Gingembre',\n            price: 800,\n            category: 'Boissons',\n            seller: 'Boissons Naturelles Dakar',\n            location: 'Dakar',\n            rating: 4.3,\n            image: 'https://images.unsplash.com/photo-1570197788417-0e82375c9371?w=400',\n            inStock: true,\n            description: 'Jus de gingembre frais, énergisant et rafraîchissant',\n            unit: 'bouteille',\n            weight: '330ml',\n            isPromo: true,\n            promoPrice: 600,\n            badges: [\n                'Promo',\n                'Frais'\n            ],\n            isNew: false,\n            distance: 2.1,\n            deliveryTime: '15-25 min'\n        },\n        {\n            id: '13',\n            name: 'Croissants Artisanaux',\n            price: 1500,\n            category: 'Boulangerie',\n            seller: 'Pâtisserie Française',\n            location: 'Dakar',\n            rating: 4.7,\n            image: 'https://images.unsplash.com/photo-1555507036-ab794f4afe5a?w=400',\n            inStock: true,\n            description: 'Croissants pur beurre, préparés selon la tradition française',\n            unit: 'lot de 6',\n            badges: [\n                'Artisanal',\n                'Nouveau'\n            ],\n            isNew: true,\n            distance: 1.5,\n            deliveryTime: '10-20 min'\n        },\n        {\n            id: '14',\n            name: 'Oignons Rouges',\n            price: 900,\n            category: 'Légumes',\n            seller: 'Maraîchers de Niayes',\n            location: 'Niayes',\n            rating: 4.2,\n            image: 'https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=400',\n            inStock: true,\n            description: 'Oignons rouges frais des Niayes, parfaits pour vos plats',\n            unit: 'kg',\n            badges: [\n                'Local',\n                'Frais'\n            ],\n            isNew: false,\n            distance: 3.7,\n            deliveryTime: '25-35 min'\n        },\n        {\n            id: '15',\n            name: 'Œufs de Poules Élevées au Sol',\n            price: 2200,\n            category: 'Volaille',\n            seller: 'Ferme Avicole Naturelle',\n            location: 'Thiès',\n            rating: 4.8,\n            image: 'https://images.unsplash.com/photo-1582722872445-44dc5f7e3c8f?w=400',\n            inStock: true,\n            description: 'Œufs frais de poules élevées au sol, riches en oméga-3',\n            unit: 'douzaine',\n            badges: [\n                'Bio',\n                'Premium'\n            ],\n            isNew: false,\n            isBio: true,\n            distance: 2.8,\n            deliveryTime: '20-30 min'\n        }\n    ];\n    // Fonctions utilitaires\n    const formatPrice = (price)=>{\n        return new Intl.NumberFormat('fr-FR', {\n            style: 'currency',\n            currency: 'XOF',\n            minimumFractionDigits: 0\n        }).format(price).replace('XOF', 'FCFA');\n    };\n    const toggleFavorite = (productId)=>{\n        setFavorites((prev)=>{\n            const newFavorites = new Set(prev);\n            if (newFavorites.has(productId)) {\n                newFavorites.delete(productId);\n            } else {\n                newFavorites.add(productId);\n            }\n            return newFavorites;\n        });\n    };\n    const updateCart = (productId, quantity)=>{\n        setCart((prev)=>({\n                ...prev,\n                [productId]: Math.max(0, quantity)\n            }));\n    };\n    const toggleTag = (tagName)=>{\n        setSelectedTags((prev)=>prev.includes(tagName) ? prev.filter((tag)=>tag !== tagName) : [\n                ...prev,\n                tagName\n            ]);\n    };\n    const clearAllFilters = ()=>{\n        setSelectedCategory('all');\n        setSearchQuery('');\n        setSelectedTags([]);\n        setShowOnlyPromo(false);\n        setShowOnlyBio(false);\n        setMinRating(0);\n        setPriceRange([\n            0,\n            50000\n        ]);\n    };\n    // Filtrage et tri\n    const filteredAndSortedProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductsPage.useMemo[filteredAndSortedProducts]\": ()=>{\n            let filtered = products.filter({\n                \"ProductsPage.useMemo[filteredAndSortedProducts].filtered\": (product)=>{\n                    const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) || product.seller.toLowerCase().includes(searchQuery.toLowerCase()) || product.category.toLowerCase().includes(searchQuery.toLowerCase()) || product.description.toLowerCase().includes(searchQuery.toLowerCase());\n                    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;\n                    const matchesPrice = product.price >= priceRange[0] && product.price <= priceRange[1];\n                    const matchesRating = product.rating >= minRating;\n                    const matchesPromo = !showOnlyPromo || product.isPromo;\n                    const matchesBio = !showOnlyBio || product.isBio;\n                    const matchesTags = selectedTags.length === 0 || selectedTags.every({\n                        \"ProductsPage.useMemo[filteredAndSortedProducts].filtered\": (tag)=>{\n                            var _product_badges;\n                            return (_product_badges = product.badges) === null || _product_badges === void 0 ? void 0 : _product_badges.includes(tag);\n                        }\n                    }[\"ProductsPage.useMemo[filteredAndSortedProducts].filtered\"]);\n                    return matchesSearch && matchesCategory && matchesPrice && matchesRating && matchesPromo && matchesBio && matchesTags;\n                }\n            }[\"ProductsPage.useMemo[filteredAndSortedProducts].filtered\"]);\n            filtered.sort({\n                \"ProductsPage.useMemo[filteredAndSortedProducts]\": (a, b)=>{\n                    switch(sortBy){\n                        case 'price':\n                            return (a.promoPrice || a.price) - (b.promoPrice || b.price);\n                        case 'rating':\n                            return (b.rating || 0) - (a.rating || 0);\n                        case 'distance':\n                            return (a.distance || 0) - (b.distance || 0);\n                        default:\n                            return a.name.localeCompare(b.name);\n                    }\n                }\n            }[\"ProductsPage.useMemo[filteredAndSortedProducts]\"]);\n            return filtered;\n        }\n    }[\"ProductsPage.useMemo[filteredAndSortedProducts]\"], [\n        products,\n        searchQuery,\n        selectedCategory,\n        priceRange,\n        minRating,\n        showOnlyPromo,\n        showOnlyBio,\n        sortBy,\n        selectedTags\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductsPage.useEffect\": ()=>{\n            const fetchProducts = {\n                \"ProductsPage.useEffect.fetchProducts\": async ()=>{\n                    setIsLoading(true);\n                    await new Promise({\n                        \"ProductsPage.useEffect.fetchProducts\": (resolve)=>setTimeout(resolve, 1000)\n                    }[\"ProductsPage.useEffect.fetchProducts\"]);\n                    setProducts(demoProducts);\n                    setIsLoading(false);\n                }\n            }[\"ProductsPage.useEffect.fetchProducts\"];\n            fetchProducts();\n        }\n    }[\"ProductsPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductsPage.useEffect\": ()=>{\n            setFilteredProducts(filteredAndSortedProducts);\n        }\n    }[\"ProductsPage.useEffect\"], [\n        filteredAndSortedProducts\n    ]);\n    // Skeleton Loader\n    const SkeletonLoader = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-8 bg-gray-200 rounded animate-pulse mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 459,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-200 rounded animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 460,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                lineNumber: 458,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n            lineNumber: 457,\n            columnNumber: 5\n        }, this);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SkeletonLoader, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n            lineNumber: 466,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-4xl font-bold mb-4\",\n                    children: \"\\uD83D\\uDED2 LocaFresh Market\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                    lineNumber: 472,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xl mb-6\",\n                    children: \"D\\xe9couvrez les meilleurs produits locaux\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                    lineNumber: 473,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: [\n                            \"\\uD83C\\uDF31 \",\n                            filteredAndSortedProducts.length,\n                            \" produits frais\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                        lineNumber: 476,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                    lineNumber: 475,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-2 mb-4\",\n                    children: locaFreshCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setSelectedCategory(category.id),\n                            className: \"px-4 py-2 rounded \".concat(selectedCategory === category.id ? 'bg-blue-500 text-white' : 'bg-gray-200'),\n                            children: [\n                                category.emoji,\n                                \" \",\n                                category.name\n                            ]\n                        }, category.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 481,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                    lineNumber: 479,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                    children: filteredAndSortedProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white p-4 rounded shadow\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-bold\",\n                                    children: product.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: product.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg font-bold\",\n                                    children: formatPrice(product.price)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                                    lineNumber: 498,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, product.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                            lineNumber: 495,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n                    lineNumber: 493,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n            lineNumber: 471,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\LocaMarket\\\\app\\\\[locale]\\\\products\\\\page.tsx\",\n        lineNumber: 470,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductsPage, \"hurTDdy4Sgg7oAUhXXKRbGKFv38=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_3__.useTranslations,\n        _contexts_OfflineModeContext__WEBPACK_IMPORTED_MODULE_2__.useOfflineMode\n    ];\n});\n_c = ProductsPage;\nvar _c;\n$RefreshReg$(_c, \"ProductsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/products/page.tsx\n"));

/***/ })

});