'use client';

import React, { useState } from 'react';
import { useTranslations } from 'next-intl';
import { motion } from 'framer-motion';
import Image from 'next/image';
import {
  FaChartLine,
  FaShoppingCart,
  FaBox,
  FaStar,
  FaEye,
  FaEdit,
  FaPlus,
  FaGripVertical,
  FaCheckCircle,
  FaTimesCircle,
  FaClock,
  FaTruck,
  FaBell,
  FaCamera,
  FaTrash
} from 'react-icons/fa';

interface Order {
  id: string;
  customer_name: string;
  items: string[];
  total: number;
  status: 'nouvelle' | 'preparation' | 'prete' | 'livree' | 'annulee';
  created_at: string;
}

interface Product {
  id: string;
  name: string;
  price: number;
  stock: number;
  image_url: string;
  category: string;
  status: 'active' | 'inactive';
}

export default function SellerDashboard() {
  const t = useTranslations();

  // États pour la gestion
  const [activeTab, setActiveTab] = useState<'overview' | 'orders' | 'products' | 'analytics' | 'marketing'>('overview');
  const [isMarketDay, setIsMarketDay] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState<'today' | 'week' | 'month'>('today');
  const [draggedProduct, setDraggedProduct] = useState<string | null>(null);

  // Données de démonstration
  const stats = {
    todayOrders: 12,
    todayRevenue: 85000,
    pendingOrders: 3,
    totalProducts: 24,
    avgRating: 4.8,
    completionRate: 96.5
  };

  const recentOrders: Order[] = [
    {
      id: '001',
      customer_name: 'Aminata Diallo',
      items: ['Tomates Bio 1kg', 'Salade Verte'],
      total: 2300,
      status: 'nouvelle',
      created_at: '2024-01-15T10:30:00Z'
    },
    {
      id: '002',
      customer_name: 'Moussa Sow',
      items: ['Carottes Bio 1kg', 'Choux Verts'],
      total: 2100,
      status: 'preparation',
      created_at: '2024-01-15T09:15:00Z'
    },
    {
      id: '003',
      customer_name: 'Fatou Ndiaye',
      items: ['Tomates Bio 1kg'],
      total: 1500,
      status: 'prete',
      created_at: '2024-01-15T08:45:00Z'
    }
  ];

  const products: Product[] = [
    {
      id: '1',
      name: 'Tomates Bio 1kg',
      price: 1500,
      stock: 50,
      image_url: '/assets/images/products/vegetables/tomates-cerises-bio.jpg',
      category: 'Légumes',
      status: 'active'
    },
    {
      id: '2',
      name: 'Salade Verte Bio',
      price: 800,
      stock: 30,
      image_url: '/assets/images/products/vegetables/salade-verte-croquante.jpg',
      category: 'Légumes',
      status: 'active'
    }
  ];

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'nouvelle': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';
      case 'preparation': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
      case 'prete': return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      case 'livree': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
      case 'annulee': return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: Order['status']) => {
    switch (status) {
      case 'nouvelle': return <FaBell className="w-4 h-4" />;
      case 'preparation': return <FaClock className="w-4 h-4" />;
      case 'prete': return <FaCheckCircle className="w-4 h-4" />;
      case 'livree': return <FaTruck className="w-4 h-4" />;
      case 'annulee': return <FaTimesCircle className="w-4 h-4" />;
      default: return null;
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* En-tête du dashboard */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white font-heading">
            Dashboard Vendeur
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Ferme Bio de Thiès - Gérez votre activité
          </p>
        </div>

        {/* Mode Jour de Marché */}
        <div className="flex items-center space-x-4 mt-4 md:mt-0">
          <div className="flex items-center">
            <label className="flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={isMarketDay}
                onChange={(e) => setIsMarketDay(e.target.checked)}
                className="sr-only"
              />
              <div className={`relative w-12 h-6 rounded-full transition-colors ${isMarketDay ? 'bg-green-500' : 'bg-gray-300'}`}>
                <div className={`absolute top-1 left-1 w-4 h-4 bg-white rounded-full transition-transform ${isMarketDay ? 'translate-x-6' : ''}`}></div>
              </div>
              <span className="ml-3 text-sm font-medium text-gray-700 dark:text-gray-300">
                Mode Jour de Marché
              </span>
            </label>
          </div>
        </div>
      </div>

      {/* Navigation par onglets premium */}
      <div className="border-b border-gray-200 dark:border-gray-700 mb-8">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Vue d\'ensemble', icon: FaChartLine },
            { id: 'orders', label: 'Commandes', icon: FaShoppingCart, badge: 3 },
            { id: 'products', label: 'Produits', icon: FaBox },
            { id: 'analytics', label: 'Analyses', icon: FaEye },
            { id: 'marketing', label: 'Marketing', icon: FaGift }
          ].map((tab) => (
            <button
              key={tab.id}
              type="button"
              onClick={() => setActiveTab(tab.id as any)}
              className={`relative flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span>{tab.label}</span>
              {tab.badge && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {tab.badge}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Contenu des onglets */}
      {activeTab === 'overview' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* Sélecteur de période */}
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Tableau de bord
            </h2>
            <div className="flex space-x-2">
              {[
                { id: 'today', label: 'Aujourd\'hui' },
                { id: 'week', label: 'Cette semaine' },
                { id: 'month', label: 'Ce mois' }
              ].map((period) => (
                <button
                  key={period.id}
                  type="button"
                  onClick={() => setSelectedPeriod(period.id as any)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    selectedPeriod === period.id
                      ? 'bg-primary-600 text-white'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                  }`}
                >
                  {period.label}
                </button>
              ))}
            </div>
          </div>

          {/* Statistiques premium avec comparaison */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 border-l-4 border-blue-500">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Commandes {selectedPeriod === 'today' ? 'aujourd\'hui' : selectedPeriod === 'week' ? 'cette semaine' : 'ce mois'}</p>
                  <p className="text-3xl font-bold text-gray-900 dark:text-white">
                    {selectedPeriod === 'today' ? stats.todayOrders : selectedPeriod === 'week' ? 23 : 89}
                  </p>
                  <div className="flex items-center mt-2">
                    <span className="text-green-600 dark:text-green-400 text-sm font-medium">+12%</span>
                    <span className="text-gray-500 dark:text-gray-400 text-sm ml-2">vs période précédente</span>
                  </div>
                </div>
                <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                  <FaShoppingCart className="text-blue-600 dark:text-blue-400 w-6 h-6" />
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 border-l-4 border-green-500">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Revenus {selectedPeriod === 'today' ? 'aujourd\'hui' : selectedPeriod === 'week' ? 'cette semaine' : 'ce mois'}</p>
                  <p className="text-3xl font-bold text-gray-900 dark:text-white">
                    {selectedPeriod === 'today' ? (stats.todayRevenue / 1000).toFixed(0) + 'k' : selectedPeriod === 'week' ? '245k' : '850k'} FCFA
                  </p>
                  <div className="flex items-center mt-2">
                    <span className="text-green-600 dark:text-green-400 text-sm font-medium">+8%</span>
                    <span className="text-gray-500 dark:text-gray-400 text-sm ml-2">vs période précédente</span>
                  </div>
                </div>
                <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-lg">
                  <FaChartLine className="text-green-600 dark:text-green-400 w-6 h-6" />
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 border-l-4 border-yellow-500">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Commandes en attente</p>
                  <p className="text-3xl font-bold text-gray-900 dark:text-white">
                    {stats.pendingOrders}
                  </p>
                  <div className="flex items-center mt-2">
                    <span className="text-red-600 dark:text-red-400 text-sm font-medium">Urgent</span>
                    <span className="text-gray-500 dark:text-gray-400 text-sm ml-2">à traiter</span>
                  </div>
                </div>
                <div className="p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg">
                  <FaClock className="text-yellow-600 dark:text-yellow-400 w-6 h-6" />
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 border-l-4 border-purple-500">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Satisfaction client</p>
                  <p className="text-3xl font-bold text-gray-900 dark:text-white">
                    {stats.avgRating}/5
                  </p>
                  <div className="flex items-center mt-2">
                    <span className="text-green-600 dark:text-green-400 text-sm font-medium">Excellent</span>
                    <span className="text-gray-500 dark:text-gray-400 text-sm ml-2">98% positifs</span>
                  </div>
                </div>
                <div className="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                  <FaStar className="text-purple-600 dark:text-purple-400 w-6 h-6" />
                </div>
              </div>
            </div>
          </div>

          {/* Graphique de performance */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Évolution des ventes
              </h3>
              <div className="h-64 bg-gray-50 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <FaChartLine className="w-12 h-12 text-gray-400 dark:text-gray-500 mx-auto mb-2" />
                  <p className="text-gray-500 dark:text-gray-400">Graphique des ventes</p>
                  <p className="text-sm text-gray-400 dark:text-gray-500">Tendance: +15% ce mois</p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Top Produits
              </h3>
              <div className="space-y-4">
                {[
                  { name: 'Tomates Bio 1kg', sales: 45, revenue: 67500 },
                  { name: 'Salade Verte Bio', sales: 32, revenue: 25600 },
                  { name: 'Carottes Bio 1kg', sales: 28, revenue: 33600 },
                  { name: 'Choux Verts Bio', sales: 15, revenue: 13500 }
                ].map((product, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center">
                        <span className="text-primary-600 dark:text-primary-400 font-bold text-sm">
                          {index + 1}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white">{product.name}</p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">{product.sales} vendus</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-gray-900 dark:text-white">
                        {product.revenue.toLocaleString()} FCFA
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Commandes récentes */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                Commandes Récentes
              </h2>
              <button className="text-primary-600 hover:text-primary-700 dark:text-primary-400 text-sm font-medium">
                Voir toutes
              </button>
            </div>

            <div className="space-y-4">
              {recentOrders.map((order) => (
                <div key={order.id} className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      {getStatusIcon(order.status)}
                    </div>
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {order.customer_name}
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {order.items.join(', ')}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                      {order.status}
                    </span>
                    <span className="font-bold text-gray-900 dark:text-white">
                      {order.total.toLocaleString()} FCFA
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </motion.div>
      )}

      {/* Autres onglets à implémenter... */}
    </div>
  );
}
