'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { useTranslations } from 'next-intl';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { FaSearch, FaFilter, FaMapMarkerAlt, FaStar, FaShoppingCart, FaHeart, FaTh, FaList, FaSort, FaTimes, FaPlus, FaMinus, FaFire, FaTag, FaLeaf, FaArrowRight } from 'react-icons/fa';
import { useOfflineMode } from '@/contexts/OfflineModeContext';

interface Product {
  id: string;
  name: string;
  price: number;
  category: string;
  seller: string;
  location: string;
  rating: number;
  image: string;
  inStock: boolean;
  description: string;
  unit?: string;
  weight?: string;
  isPromo?: boolean;
  promoPrice?: number;
  badges?: string[];
  isNew?: boolean;
  isBio?: boolean;
  distance?: number;
  deliveryTime?: string;
}

export default function ProductsPage() {
  const t = useTranslations();
  const { isOnline, offlineData } = useOfflineMode();

  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isLoading, setIsLoading] = useState(true);
  const [showFilters, setShowFilters] = useState(false);
  const [sortBy, setSortBy] = useState<'name' | 'price' | 'rating' | 'distance'>('name');
  const [priceRange, setPriceRange] = useState([0, 50000]);
  const [minRating, setMinRating] = useState(0);
  const [showOnlyPromo, setShowOnlyPromo] = useState(false);
  const [showOnlyBio, setShowOnlyBio] = useState(false);
  const [favorites, setFavorites] = useState<Set<string>>(new Set());
  const [cart, setCart] = useState<Record<string, number>>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(true);
  const [notifications, setNotifications] = useState<Array<{id: string, message: string, type: 'success' | 'error'}>>([]);
  const productsPerPage = 12;

  // Tags populaires inspirés de TastyDaily
  const popularTags = [
    { name: 'Bio', emoji: '🌱', color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' },
    { name: 'Promo', emoji: '🏷️', color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' },
    { name: 'Local', emoji: '📍', color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' },
    { name: 'Frais', emoji: '❄️', color: 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-200' },
    { name: 'Premium', emoji: '⭐', color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' },
    { name: 'Traditionnel', emoji: '🏛️', color: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' },
    { name: 'Artisanal', emoji: '🎨', color: 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200' },
    { name: 'Nouveau', emoji: '✨', color: 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200' },
  ];

  // Catégories LocaFresh avec emojis - Inspiré de TastyDaily
  const locaFreshCategories = [
    { id: 'all', name: 'Tout', emoji: '🛒', color: 'from-gray-400 to-gray-500' },
    { id: 'Fruits', name: 'Fruits', emoji: '🍎', color: 'from-red-400 to-orange-500' },
    { id: 'Légumes', name: 'Légumes', emoji: '🥬', color: 'from-green-400 to-green-500' },
    { id: 'Viandes', name: 'Viandes', emoji: '🥩', color: 'from-red-500 to-red-600' },
    { id: 'Volaille', name: 'Volaille', emoji: '🐔', color: 'from-yellow-400 to-orange-500' },
    { id: 'Poissons', name: 'Poissons', emoji: '🐟', color: 'from-blue-400 to-blue-500' },
    { id: 'Boulangerie', name: 'Boulangerie', emoji: '🍞', color: 'from-amber-400 to-amber-500' },
    { id: 'Boissons', name: 'Boissons', emoji: '🥤', color: 'from-cyan-400 to-blue-500' },
    { id: 'Artisanat', name: 'Artisanat', emoji: '🎨', color: 'from-purple-400 to-pink-500' },
  ];

  // Données de démonstration enrichies pour LocaFresh
  const demoProducts: Product[] = [
    {
      id: '1',
      name: 'Mangues Bio Kent',
      price: 2500,
      category: 'Fruits',
      seller: 'Ferme Bio Diallo',
      location: 'Thiès',
      rating: 4.8,
      image: 'https://images.unsplash.com/photo-1553279768-865429fa0078?w=400',
      inStock: true,
      description: 'Mangues biologiques fraîches, cultivées sans pesticides',
      unit: 'kg',
      weight: '1kg',
      isPromo: true,
      promoPrice: 2000,
      badges: ['Bio', 'Promo', 'Local'],
      isNew: false,
      isBio: true,
      distance: 2.5,
      deliveryTime: '20-30 min'
    },
    {
      id: '2',
      name: 'Tomates Cerises Bio',
      price: 1800,
      category: 'Légumes',
      seller: 'Jardin de Fatou',
      location: 'Rufisque',
      rating: 4.6,
      image: 'https://images.unsplash.com/photo-1546470427-e5b89b618b84?w=400',
      inStock: true,
      description: 'Tomates cerises fraîches du jour, cultivées en agriculture biologique',
      unit: 'barquette',
      weight: '250g',
      badges: ['Bio', 'Frais', 'Local'],
      isNew: true,
      isBio: true,
      distance: 1.8,
      deliveryTime: '15-25 min'
    },
    {
      id: '3',
      name: 'Pain Traditionnel au Feu de Bois',
      price: 500,
      category: 'Boulangerie',
      seller: 'Boulangerie Artisanale',
      location: 'Dakar',
      rating: 4.9,
      image: 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400',
      inStock: true,
      description: 'Pain traditionnel sénégalais cuit au feu de bois selon la recette ancestrale',
      unit: 'pièce',
      badges: ['Artisanal', 'Traditionnel'],
      isNew: false,
      distance: 1.2,
      deliveryTime: '10-20 min'
    }
  ];

  // Fonctions utilitaires
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'XOF',
      minimumFractionDigits: 0,
    }).format(price).replace('XOF', 'FCFA');
  };

  const toggleFavorite = (productId: string) => {
    setFavorites(prev => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(productId)) {
        newFavorites.delete(productId);
      } else {
        newFavorites.add(productId);
      }
      return newFavorites;
    });
  };

  const updateCart = (productId: string, quantity: number) => {
    setCart(prev => ({
      ...prev,
      [productId]: Math.max(0, quantity)
    }));
  };

  const toggleTag = (tagName: string) => {
    setSelectedTags(prev => 
      prev.includes(tagName) 
        ? prev.filter(tag => tag !== tagName)
        : [...prev, tagName]
    );
  };

  const clearAllFilters = () => {
    setSelectedCategory('all');
    setSearchQuery('');
    setSelectedTags([]);
    setShowOnlyPromo(false);
    setShowOnlyBio(false);
    setMinRating(0);
    setPriceRange([0, 50000]);
  };

  // Filtrage et tri
  const filteredAndSortedProducts = useMemo(() => {
    let filtered = products.filter(product => {
      const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           product.seller.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           product.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           product.description.toLowerCase().includes(searchQuery.toLowerCase());
      
      const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;
      const matchesPrice = product.price >= priceRange[0] && product.price <= priceRange[1];
      const matchesRating = product.rating >= minRating;
      const matchesPromo = !showOnlyPromo || product.isPromo;
      const matchesBio = !showOnlyBio || product.isBio;
      const matchesTags = selectedTags.length === 0 || selectedTags.every(tag => product.badges?.includes(tag));

      return matchesSearch && matchesCategory && matchesPrice && matchesRating && matchesPromo && matchesBio && matchesTags;
    });

    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'price':
          return (a.promoPrice || a.price) - (b.promoPrice || b.price);
        case 'rating':
          return (b.rating || 0) - (a.rating || 0);
        case 'distance':
          return (a.distance || 0) - (b.distance || 0);
        default:
          return a.name.localeCompare(b.name);
      }
    });

    return filtered;
  }, [products, searchQuery, selectedCategory, priceRange, minRating, showOnlyPromo, showOnlyBio, sortBy, selectedTags]);

  useEffect(() => {
    const fetchProducts = async () => {
      setIsLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      setProducts(demoProducts);
      setIsLoading(false);
    };

    fetchProducts();
  }, []);

  useEffect(() => {
    setFilteredProducts(filteredAndSortedProducts);
  }, [filteredAndSortedProducts]);

  // Skeleton Loader
  const SkeletonLoader = () => (
    <div className="min-h-screen bg-gray-50">
      <div className="p-8">
        <div className="h-8 bg-gray-200 rounded animate-pulse mb-4"></div>
        <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
      </div>
    </div>
  );

  if (isLoading) {
    return <SkeletonLoader />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="p-8">
        <h1 className="text-4xl font-bold mb-4">🛒 LocaFresh Market</h1>
        <p className="text-xl mb-6">Découvrez les meilleurs produits locaux</p>
        
        <div className="mb-4">
          <span>🌱 {filteredAndSortedProducts.length} produits frais</span>
        </div>

        <div className="flex gap-2 mb-4">
          {locaFreshCategories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`px-4 py-2 rounded ${
                selectedCategory === category.id ? 'bg-blue-500 text-white' : 'bg-gray-200'
              }`}
            >
              {category.emoji} {category.name}
            </button>
          ))}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredAndSortedProducts.map((product) => (
            <div key={product.id} className="bg-white p-4 rounded shadow">
              <h3 className="font-bold">{product.name}</h3>
              <p className="text-gray-600">{product.description}</p>
              <p className="text-lg font-bold">{formatPrice(product.price)}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
