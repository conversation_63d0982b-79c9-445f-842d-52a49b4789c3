'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { useTranslations } from 'next-intl';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { FaSearch, FaFilter, FaMapMarkerAlt, FaStar, FaShoppingCart, FaHeart, FaTh, FaList, FaSort, FaTimes, FaPlus, FaMinus, FaFire, FaTag, FaLeaf, FaArrowRight } from 'react-icons/fa';
import { useOfflineMode } from '@/contexts/OfflineModeContext';

interface Product {
  id: string;
  name: string;
  price: number;
  category: string;
  seller: string;
  location: string;
  rating: number;
  image: string;
  inStock: boolean;
  description: string;
  unit?: string;
  weight?: string;
  isPromo?: boolean;
  promoPrice?: number;
  badges?: string[];
  isNew?: boolean;
  isBio?: boolean;
  distance?: number;
  deliveryTime?: string;
}

export default function ProductsPage() {
  const t = useTranslations();
  const { isOnline, offlineData } = useOfflineMode();

  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isLoading, setIsLoading] = useState(true);
  const [showFilters, setShowFilters] = useState(false);
  const [sortBy, setSortBy] = useState<'name' | 'price' | 'rating' | 'distance'>('name');
  const [priceRange, setPriceRange] = useState([0, 50000]);
  const [minRating, setMinRating] = useState(0);
  const [showOnlyPromo, setShowOnlyPromo] = useState(false);
  const [showOnlyBio, setShowOnlyBio] = useState(false);
  const [favorites, setFavorites] = useState<Set<string>>(new Set());
  const [cart, setCart] = useState<Record<string, number>>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(true);
  const [notifications, setNotifications] = useState<Array<{id: string, message: string, type: 'success' | 'error'}>>([]);
  const productsPerPage = 12;

  // Tags populaires inspirés de TastyDaily
  const popularTags = [
    { name: 'Bio', emoji: '🌱', color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' },
    { name: 'Promo', emoji: '🏷️', color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' },
    { name: 'Local', emoji: '📍', color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' },
    { name: 'Frais', emoji: '❄️', color: 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-200' },
    { name: 'Premium', emoji: '⭐', color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' },
    { name: 'Traditionnel', emoji: '🏛️', color: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' },
    { name: 'Artisanal', emoji: '🎨', color: 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200' },
    { name: 'Nouveau', emoji: '✨', color: 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200' },
  ];

  // Catégories LocaFresh avec emojis - Inspiré de TastyDaily
  const locaFreshCategories = [
    { id: 'all', name: 'Tout', emoji: '🛒', color: 'from-gray-400 to-gray-500' },
    { id: 'Fruits', name: 'Fruits', emoji: '🍎', color: 'from-red-400 to-orange-500' },
    { id: 'Légumes', name: 'Légumes', emoji: '🥬', color: 'from-green-400 to-green-500' },
    { id: 'Viandes', name: 'Viandes', emoji: '🥩', color: 'from-red-500 to-red-600' },
    { id: 'Volaille', name: 'Volaille', emoji: '🐔', color: 'from-yellow-400 to-orange-500' },
    { id: 'Poissons', name: 'Poissons', emoji: '🐟', color: 'from-blue-400 to-blue-500' },
    { id: 'Boulangerie', name: 'Boulangerie', emoji: '🍞', color: 'from-amber-400 to-amber-500' },
    { id: 'Boissons', name: 'Boissons', emoji: '🥤', color: 'from-cyan-400 to-blue-500' },
    { id: 'Artisanat', name: 'Artisanat', emoji: '🎨', color: 'from-purple-400 to-pink-500' },
  ];

  // Données de démonstration enrichies pour LocaFresh - 27 produits variés
  const demoProducts: Product[] = [
    {
      id: '1',
      name: 'Mangues Bio Kent',
      price: 2500,
      category: 'Fruits',
      seller: 'Ferme Bio Diallo',
      location: 'Thiès',
      rating: 4.8,
      image: 'https://images.unsplash.com/photo-1553279768-865429fa0078?w=400',
      inStock: true,
      description: 'Mangues biologiques fraîches, cultivées sans pesticides',
      unit: 'kg',
      weight: '1kg',
      isPromo: true,
      promoPrice: 2000,
      badges: ['Bio', 'Promo', 'Local'],
      isNew: false,
      isBio: true,
      distance: 2.5,
      deliveryTime: '20-30 min'
    },
    {
      id: '2',
      name: 'Tomates Cerises Bio',
      price: 1800,
      category: 'Légumes',
      seller: 'Jardin de Fatou',
      location: 'Rufisque',
      rating: 4.6,
      image: 'https://images.unsplash.com/photo-1546470427-e5b89b618b84?w=400',
      inStock: true,
      description: 'Tomates cerises fraîches du jour, cultivées en agriculture biologique',
      unit: 'barquette',
      weight: '250g',
      badges: ['Bio', 'Frais', 'Local'],
      isNew: true,
      isBio: true,
      distance: 1.8,
      deliveryTime: '15-25 min'
    },
    {
      id: '3',
      name: 'Pain Traditionnel au Feu de Bois',
      price: 500,
      category: 'Boulangerie',
      seller: 'Boulangerie Artisanale',
      location: 'Dakar',
      rating: 4.9,
      image: 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400',
      inStock: true,
      description: 'Pain traditionnel sénégalais cuit au feu de bois selon la recette ancestrale',
      unit: 'pièce',
      badges: ['Artisanal', 'Traditionnel'],
      isNew: false,
      distance: 1.2,
      deliveryTime: '10-20 min'
    },
    {
      id: '4',
      name: 'Bissap Artisanal aux Épices',
      price: 1200,
      category: 'Boissons',
      seller: 'Les Délices de Khadija',
      location: 'Saint-Louis',
      rating: 4.7,
      image: 'https://images.unsplash.com/photo-1622597467836-f3285f2131b8?w=400',
      inStock: true,
      description: 'Bissap artisanal aux épices naturelles, sans conservateurs',
      unit: 'bouteille',
      weight: '500ml',
      badges: ['Artisanal', 'Traditionnel'],
      isNew: false,
      distance: 4.2,
      deliveryTime: '30-40 min'
    },
    {
      id: '5',
      name: 'Thiof Frais du Matin',
      price: 3500,
      category: 'Poissons',
      seller: 'Pêcheurs de Soumbédioune',
      location: 'Dakar',
      rating: 4.5,
      image: 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=400',
      inStock: true,
      description: 'Thiof fraîchement pêché ce matin, qualité premium',
      unit: 'kg',
      badges: ['Frais', 'Premium', 'Local'],
      isNew: false,
      distance: 3.2,
      deliveryTime: '25-35 min'
    },
    {
      id: '6',
      name: 'Sac Artisanal en Raphia',
      price: 8000,
      category: 'Artisanat',
      seller: 'Atelier Sénégal Authentique',
      location: 'Kaolack',
      rating: 4.8,
      image: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400',
      inStock: true,
      description: 'Sac artisanal en raphia tressé à la main, design traditionnel',
      unit: 'pièce',
      badges: ['Artisanal', 'Traditionnel'],
      isNew: true,
      distance: 5.1,
      deliveryTime: '40-50 min'
    },
    {
      id: '7',
      name: 'Bananes Bio Plantain',
      price: 1500,
      category: 'Fruits',
      seller: 'Coopérative Fruits Bio',
      location: 'Ziguinchor',
      rating: 4.4,
      image: 'https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=400',
      inStock: true,
      description: 'Bananes plantain biologiques, parfaites pour la cuisine',
      unit: 'régime',
      weight: '2kg',
      isPromo: true,
      promoPrice: 1200,
      badges: ['Bio', 'Promo'],
      isNew: false,
      isBio: true,
      distance: 6.8,
      deliveryTime: '45-60 min'
    },
    {
      id: '8',
      name: 'Poulet Fermier Bio',
      price: 12000,
      category: 'Volaille',
      seller: 'Ferme Avicole Bio',
      location: 'Mbour',
      rating: 4.7,
      image: 'https://images.unsplash.com/photo-1604503468506-a8da13d82791?w=400',
      inStock: true,
      description: 'Poulet fermier élevé en liberté, nourri aux grains bio',
      unit: 'kg',
      badges: ['Bio', 'Premium'],
      isNew: false,
      isBio: true,
      distance: 3.8,
      deliveryTime: '30-40 min'
    },
    {
      id: '9',
      name: 'Ananas Victoria',
      price: 3000,
      category: 'Fruits',
      seller: 'Plantation Tropicale',
      location: 'Casamance',
      rating: 4.9,
      image: 'https://images.unsplash.com/photo-1550258987-190a2d41a8ba?w=400',
      inStock: true,
      description: 'Ananas Victoria extra sucré, cultivé en Casamance',
      unit: 'pièce',
      weight: '1.5kg',
      badges: ['Premium', 'Local'],
      isNew: false,
      distance: 8.2,
      deliveryTime: '60-75 min'
    },
    {
      id: '10',
      name: 'Crevettes Fraîches',
      price: 8500,
      category: 'Poissons',
      seller: 'Pêcheurs de Joal',
      location: 'Joal-Fadiouth',
      rating: 4.6,
      image: 'https://images.unsplash.com/photo-1565680018434-b513d5e5fd47?w=400',
      inStock: true,
      description: 'Crevettes fraîches pêchées dans les eaux sénégalaises',
      unit: 'kg',
      badges: ['Frais', 'Premium'],
      isNew: true,
      distance: 4.5,
      deliveryTime: '35-45 min'
    },
    {
      id: '11',
      name: 'Miel Pur Local 500g',
      price: 4500,
      category: 'Artisanat',
      seller: 'Apiculteurs de Casamance',
      location: 'Casamance',
      rating: 4.9,
      image: 'https://images.unsplash.com/photo-1587049352846-4a222e784d38?w=400',
      inStock: true,
      description: 'Miel pur et naturel récolté dans les ruches traditionnelles',
      unit: 'pot',
      weight: '500g',
      badges: ['Traditionnel', 'Local'],
      isNew: false,
      distance: 8.2,
      deliveryTime: '60-75 min'
    },
    {
      id: '12',
      name: 'Jus de Gingembre',
      price: 800,
      category: 'Boissons',
      seller: 'Boissons Naturelles Dakar',
      location: 'Dakar',
      rating: 4.3,
      image: 'https://images.unsplash.com/photo-1570197788417-0e82375c9371?w=400',
      inStock: true,
      description: 'Jus de gingembre frais, énergisant et rafraîchissant',
      unit: 'bouteille',
      weight: '330ml',
      isPromo: true,
      promoPrice: 600,
      badges: ['Promo', 'Frais'],
      isNew: false,
      distance: 2.1,
      deliveryTime: '15-25 min'
    },
    {
      id: '13',
      name: 'Croissants Artisanaux',
      price: 1500,
      category: 'Boulangerie',
      seller: 'Pâtisserie Française',
      location: 'Dakar',
      rating: 4.7,
      image: 'https://images.unsplash.com/photo-1555507036-ab794f4afe5a?w=400',
      inStock: true,
      description: 'Croissants pur beurre, préparés selon la tradition française',
      unit: 'lot de 6',
      badges: ['Artisanal', 'Nouveau'],
      isNew: true,
      distance: 1.5,
      deliveryTime: '10-20 min'
    },
    {
      id: '14',
      name: 'Oignons Rouges',
      price: 900,
      category: 'Légumes',
      seller: 'Maraîchers de Niayes',
      location: 'Niayes',
      rating: 4.2,
      image: 'https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=400',
      inStock: true,
      description: 'Oignons rouges frais des Niayes, parfaits pour vos plats',
      unit: 'kg',
      badges: ['Local', 'Frais'],
      isNew: false,
      distance: 3.7,
      deliveryTime: '25-35 min'
    },
    {
      id: '15',
      name: 'Œufs de Poules Élevées au Sol',
      price: 2200,
      category: 'Volaille',
      seller: 'Ferme Avicole Naturelle',
      location: 'Thiès',
      rating: 4.8,
      image: 'https://images.unsplash.com/photo-1582722872445-44dc5f7e3c8f?w=400',
      inStock: true,
      description: 'Œufs frais de poules élevées au sol, riches en oméga-3',
      unit: 'douzaine',
      badges: ['Bio', 'Premium'],
      isNew: false,
      isBio: true,
      distance: 2.8,
      deliveryTime: '20-30 min'
    },
    {
      id: '30',
      name: 'Jus de Bissap Artisanal 1L',
      price: 1500,
      category: 'Artisanat',
      seller: 'Boissons Naturelles Dakar',
      location: 'Dakar',
      rating: 4.7,
      image: 'https://images.unsplash.com/photo-1544145945-f90425340c7e?w=400',
      inStock: true,
      description: 'Jus de bissap artisanal, préparé selon la tradition sénégalaise avec des fleurs d\'hibiscus locales',
      unit: 'bouteille',
      badges: ['Artisanal', 'Traditionnel'],
      isNew: false,
      distance: 2.1,
      deliveryTime: '15-25 min'
    },
    {
      id: '31',
      name: 'Confiture de Mangue Artisanale 250g',
      price: 2800,
      category: 'Artisanat',
      seller: 'Confitures de Casamance',
      location: 'Casamance',
      rating: 4.8,
      image: 'https://images.unsplash.com/photo-1484723091739-30a097e8f929?w=400',
      inStock: true,
      description: 'Confiture artisanale de mangues Kent, préparée avec des fruits locaux et du sucre de canne',
      unit: 'pot',
      badges: ['Artisanal', 'Bio', 'Local'],
      isNew: true,
      isBio: true,
      distance: 8.2,
      deliveryTime: '60-75 min'
    },
    {
      id: '32',
      name: 'Savon Naturel au Karité 100g',
      price: 1200,
      category: 'Artisanat',
      seller: 'Savonnerie Traditionnelle',
      location: 'Thiès',
      rating: 4.9,
      image: 'https://images.unsplash.com/photo-1556228720-195a672e8a03?w=400',
      inStock: true,
      description: 'Savon artisanal au beurre de karité pur, fabriqué selon les méthodes traditionnelles',
      unit: 'pièce',
      badges: ['Artisanal', 'Bio', 'Traditionnel'],
      isNew: false,
      isBio: true,
      distance: 2.8,
      deliveryTime: '20-30 min'
    },
    {
      id: '33',
      name: 'Panier Tressé en Raphia',
      price: 3500,
      category: 'Artisanat',
      seller: 'Artisans de Kaolack',
      location: 'Kaolack',
      rating: 4.6,
      image: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400',
      inStock: true,
      description: 'Panier artisanal tressé à la main en raphia naturel, idéal pour les courses ou la décoration',
      unit: 'pièce',
      badges: ['Artisanal', 'Traditionnel', 'Local'],
      isNew: true,
      distance: 5.1,
      deliveryTime: '40-50 min'
    },
    {
      id: '34',
      name: 'Jus de Gingembre Frais 500ml',
      price: 1000,
      category: 'Artisanat',
      seller: 'Boissons Naturelles Dakar',
      location: 'Dakar',
      rating: 4.5,
      image: 'https://images.unsplash.com/photo-1570197788417-0e82375c9371?w=400',
      inStock: true,
      description: 'Jus de gingembre frais artisanal, énergisant et rafraîchissant, préparé quotidiennement',
      unit: 'bouteille',
      badges: ['Artisanal', 'Frais'],
      isNew: false,
      distance: 2.1,
      deliveryTime: '15-25 min'
    },
    {
      id: '35',
      name: 'Oignons Violets de Galmi 1kg',
      price: 1200,
      category: 'Légumes',
      seller: 'Maraîchers de Galmi',
      location: 'Galmi, Niger',
      rating: 4.7,
      image: 'https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=400',
      inStock: true,
      description: 'Oignons violets de Galmi, réputés pour leur saveur douce et leur conservation exceptionnelle',
      unit: 'kg',
      badges: ['Premium', 'Local'],
      isNew: false,
      distance: 12.5,
      deliveryTime: '2-3 heures'
    },
    {
      id: '36',
      name: 'Piments Scotch Bonnet 250g',
      price: 800,
      category: 'Légumes',
      seller: 'Jardin Épicé de Thiès',
      location: 'Thiès',
      rating: 4.8,
      image: 'https://images.unsplash.com/photo-1583454110551-21f2fa2afe61?w=400',
      inStock: true,
      description: 'Piments Scotch Bonnet extra forts, parfaits pour relever vos plats traditionnels',
      unit: 'barquette',
      weight: '250g',
      badges: ['Frais', 'Local'],
      isNew: true,
      distance: 2.8,
      deliveryTime: '20-30 min'
    },
    {
      id: '37',
      name: 'Choux Verts Bio 1 pièce',
      price: 600,
      category: 'Légumes',
      seller: 'Bio Ferme des Niayes',
      location: 'Niayes',
      rating: 4.5,
      image: 'https://images.unsplash.com/photo-1594282486552-05b4d80fbb9f?w=400',
      inStock: true,
      description: 'Choux verts biologiques cultivés dans les Niayes, riches en vitamines',
      unit: 'pièce',
      badges: ['Bio', 'Frais', 'Local'],
      isNew: false,
      isBio: true,
      distance: 3.7,
      deliveryTime: '25-35 min'
    },
    {
      id: '38',
      name: 'Carottes Orange 1kg',
      price: 900,
      category: 'Légumes',
      seller: 'Potager de Saint-Louis',
      location: 'Saint-Louis',
      rating: 4.6,
      image: 'https://images.unsplash.com/photo-1445282768818-728615cc910a?w=400',
      inStock: true,
      description: 'Carottes orange fraîches, croquantes et sucrées, cultivées dans la vallée du fleuve',
      unit: 'kg',
      badges: ['Frais', 'Local'],
      isNew: false,
      distance: 4.2,
      deliveryTime: '30-40 min'
    },
    {
      id: '39',
      name: 'Aubergines Violettes 500g',
      price: 1100,
      category: 'Légumes',
      seller: 'Jardin de Fatou',
      location: 'Rufisque',
      rating: 4.4,
      image: 'https://images.unsplash.com/photo-1659261200833-ec8761558af7?w=400',
      inStock: true,
      description: 'Aubergines violettes fraîches, parfaites pour vos thieboudienne et ragoûts',
      unit: 'barquette',
      weight: '500g',
      badges: ['Frais', 'Local'],
      isNew: false,
      distance: 1.8,
      deliveryTime: '15-25 min'
    },
    {
      id: '40',
      name: 'Gombo Frais 300g',
      price: 700,
      category: 'Légumes',
      seller: 'Maraîchers de Casamance',
      location: 'Casamance',
      rating: 4.9,
      image: 'https://images.unsplash.com/photo-1631207829628-5d8b3c8b1e3d?w=400',
      inStock: true,
      description: 'Gombo frais de Casamance, ingrédient essentiel de la cuisine sénégalaise',
      unit: 'barquette',
      weight: '300g',
      badges: ['Frais', 'Traditionnel', 'Local'],
      isNew: false,
      distance: 8.2,
      deliveryTime: '60-75 min'
    },
    {
      id: '41',
      name: 'Épinards Locaux 250g',
      price: 500,
      category: 'Légumes',
      seller: 'Jardin Vert de Pikine',
      location: 'Pikine',
      rating: 4.3,
      image: 'https://images.unsplash.com/photo-1576045057995-568f588f82fb?w=400',
      inStock: true,
      description: 'Épinards locaux frais, riches en fer et vitamines, cultivés sans pesticides',
      unit: 'botte',
      weight: '250g',
      badges: ['Bio', 'Frais', 'Local'],
      isNew: true,
      isBio: true,
      distance: 1.5,
      deliveryTime: '10-20 min'
    },
    {
      id: '42',
      name: 'Courgettes Vertes 1kg',
      price: 1300,
      category: 'Légumes',
      seller: 'Ferme Maraîchère de Mbour',
      location: 'Mbour',
      rating: 4.7,
      image: 'https://images.unsplash.com/photo-1566385101042-1a0aa0c1268c?w=400',
      inStock: true,
      description: 'Courgettes vertes tendres et savoureuses, parfaites pour vos gratins et ratatouilles',
      unit: 'kg',
      badges: ['Frais', 'Local'],
      isNew: false,
      distance: 3.8,
      deliveryTime: '30-40 min'
    },
    {
      id: '43',
      name: 'Oranges Douces de Valencia 2kg',
      price: 1800,
      category: 'Fruits',
      seller: 'Vergers de Casamance',
      location: 'Casamance',
      rating: 4.8,
      image: 'https://images.unsplash.com/photo-1547514701-42782101795e?w=400',
      inStock: true,
      description: 'Oranges douces et juteuses de Valencia, riches en vitamine C, cultivées en Casamance',
      unit: 'kg',
      weight: '2kg',
      badges: ['Frais', 'Local', 'Premium'],
      isNew: false,
      distance: 8.2,
      deliveryTime: '60-75 min'
    },
    {
      id: '44',
      name: 'Papayes Mûres 1 pièce',
      price: 2200,
      category: 'Fruits',
      seller: 'Plantation Tropicale du Sud',
      location: 'Ziguinchor',
      rating: 4.9,
      image: 'https://images.unsplash.com/photo-1617112848923-cc2234396a8d?w=400',
      inStock: true,
      description: 'Papayes mûres à point, chair orange et sucrée, excellente source d\'enzymes digestives',
      unit: 'pièce',
      weight: '1.5kg',
      badges: ['Frais', 'Premium', 'Local'],
      isNew: true,
      distance: 6.8,
      deliveryTime: '45-60 min'
    },
    {
      id: '45',
      name: 'Citrons Verts 500g',
      price: 800,
      category: 'Fruits',
      seller: 'Agrumes de Thiès',
      location: 'Thiès',
      rating: 4.6,
      image: 'https://images.unsplash.com/photo-1568702846914-96b305d2aaeb?w=400',
      inStock: true,
      description: 'Citrons verts frais et acidulés, parfaits pour vos boissons et marinades',
      unit: 'barquette',
      weight: '500g',
      badges: ['Frais', 'Local'],
      isNew: false,
      distance: 2.8,
      deliveryTime: '20-30 min'
    },
    {
      id: '46',
      name: 'Pastèques Rouges 1 pièce',
      price: 3500,
      category: 'Fruits',
      seller: 'Ferme de la Vallée',
      location: 'Saint-Louis',
      rating: 4.7,
      image: 'https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=400',
      inStock: true,
      description: 'Pastèques rouges sucrées et rafraîchissantes, cultivées dans la vallée du fleuve Sénégal',
      unit: 'pièce',
      weight: '4-5kg',
      badges: ['Frais', 'Local', 'Premium'],
      isNew: false,
      distance: 4.2,
      deliveryTime: '30-40 min'
    },
    {
      id: '47',
      name: 'Goyaves Roses 1kg',
      price: 1500,
      category: 'Fruits',
      seller: 'Vergers Tropicaux',
      location: 'Casamance',
      rating: 4.5,
      image: 'https://images.unsplash.com/photo-1536511132770-e5058c4e1d93?w=400',
      inStock: true,
      description: 'Goyaves roses parfumées, riches en vitamine C et antioxydants naturels',
      unit: 'kg',
      badges: ['Frais', 'Local', 'Bio'],
      isNew: true,
      isBio: true,
      distance: 8.2,
      deliveryTime: '60-75 min'
    },
    {
      id: '48',
      name: 'Avocats Hass 4 pièces',
      price: 2800,
      category: 'Fruits',
      seller: 'Plantation d\'Avocats Bio',
      location: 'Niayes',
      rating: 4.8,
      image: 'https://images.unsplash.com/photo-1523049673857-eb18f1d7b578?w=400',
      inStock: true,
      description: 'Avocats Hass biologiques, crémeux et nutritifs, parfaits pour vos salades et smoothies',
      unit: 'lot de 4',
      badges: ['Bio', 'Premium', 'Local'],
      isNew: false,
      isBio: true,
      distance: 3.7,
      deliveryTime: '25-35 min'
    },
    {
      id: '49',
      name: 'Bœuf de Zébu Local 1kg',
      price: 8500,
      category: 'Viandes',
      seller: 'Boucherie Moderne de Dakar',
      location: 'Dakar',
      rating: 4.9,
      image: 'https://images.unsplash.com/photo-1588347818133-38c4106ca7b4?w=400',
      inStock: true,
      description: 'Viande de bœuf zébu local, élevé en pâturage naturel, tendre et savoureuse',
      unit: 'kg',
      badges: ['Premium', 'Local', 'Frais'],
      isNew: false,
      distance: 1.2,
      deliveryTime: '10-20 min'
    },
    {
      id: '50',
      name: 'Mouton de Tabaski 2kg',
      price: 12000,
      category: 'Viandes',
      seller: 'Élevage Peul de Fouta',
      location: 'Fouta Toro',
      rating: 4.8,
      image: 'https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?w=400',
      inStock: true,
      description: 'Viande de mouton de Tabaski, élevé selon les traditions peules, qualité exceptionnelle',
      unit: 'kg',
      badges: ['Premium', 'Traditionnel', 'Local'],
      isNew: true,
      distance: 15.2,
      deliveryTime: '3-4 heures'
    },
    {
      id: '51',
      name: 'Agneau Tendre 1kg',
      price: 9500,
      category: 'Viandes',
      seller: 'Bergerie de Thiès',
      location: 'Thiès',
      rating: 4.7,
      image: 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?w=400',
      inStock: true,
      description: 'Agneau tendre et savoureux, élevé au grain, parfait pour vos grillades et ragoûts',
      unit: 'kg',
      badges: ['Premium', 'Frais', 'Local'],
      isNew: false,
      distance: 2.8,
      deliveryTime: '20-30 min'
    },
    {
      id: '52',
      name: 'Viande Hachée de Bœuf 500g',
      price: 4500,
      category: 'Viandes',
      seller: 'Boucherie Halal Premium',
      location: 'Dakar',
      rating: 4.6,
      image: 'https://images.unsplash.com/photo-1603048297172-c92544798d5a?w=400',
      inStock: true,
      description: 'Viande hachée fraîche de bœuf local, idéale pour boulettes, burgers et farces',
      unit: 'barquette',
      weight: '500g',
      badges: ['Frais', 'Local'],
      isNew: false,
      distance: 1.5,
      deliveryTime: '10-20 min'
    },
    {
      id: '53',
      name: 'Côtes de Bœuf Grillades 1.5kg',
      price: 11000,
      category: 'Viandes',
      seller: 'Boucherie du Marché Kermel',
      location: 'Dakar',
      rating: 4.8,
      image: 'https://images.unsplash.com/photo-1544025162-d76694265947?w=400',
      inStock: true,
      description: 'Côtes de bœuf premium, parfaites pour vos grillades et barbecues en famille',
      unit: 'kg',
      weight: '1.5kg',
      badges: ['Premium', 'Frais'],
      isNew: false,
      distance: 1.8,
      deliveryTime: '15-25 min'
    },
    {
      id: '54',
      name: 'Filet de Bœuf Premium 800g',
      price: 15000,
      category: 'Viandes',
      seller: 'Boucherie de Luxe Almadies',
      location: 'Almadies',
      rating: 4.9,
      image: 'https://images.unsplash.com/photo-1551731409-43eb3e517a1a?w=400',
      inStock: true,
      description: 'Filet de bœuf premium, pièce noble et tendre, pour vos occasions spéciales',
      unit: 'pièce',
      weight: '800g',
      badges: ['Premium', 'Frais', 'Nouveau'],
      isNew: true,
      distance: 5.2,
      deliveryTime: '35-45 min'
    }
  ];

  // Fonctions utilitaires
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'XOF',
      minimumFractionDigits: 0,
    }).format(price).replace('XOF', 'FCFA');
  };

  const toggleFavorite = (productId: string) => {
    setFavorites(prev => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(productId)) {
        newFavorites.delete(productId);
      } else {
        newFavorites.add(productId);
      }
      return newFavorites;
    });
  };

  const updateCart = (productId: string, quantity: number) => {
    setCart(prev => ({
      ...prev,
      [productId]: Math.max(0, quantity)
    }));
  };

  const toggleTag = (tagName: string) => {
    setSelectedTags(prev =>
      prev.includes(tagName)
        ? prev.filter(tag => tag !== tagName)
        : [...prev, tagName]
    );
  };

  const clearAllFilters = () => {
    setSelectedCategory('all');
    setSearchQuery('');
    setSelectedTags([]);
    setShowOnlyPromo(false);
    setShowOnlyBio(false);
    setMinRating(0);
    setPriceRange([0, 50000]);
  };

  const addNotification = (message: string, type: 'success' | 'error') => {
    const id = Date.now().toString();
    setNotifications(prev => [...prev, { id, message, type }]);
    setTimeout(() => {
      setNotifications(prev => prev.filter(n => n.id !== id));
    }, 3000);
  };

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Filtrage et tri
  const filteredAndSortedProducts = useMemo(() => {
    let filtered = products.filter(product => {
      const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           product.seller.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           product.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           product.description.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;
      const matchesPrice = product.price >= priceRange[0] && product.price <= priceRange[1];
      const matchesRating = product.rating >= minRating;
      const matchesPromo = !showOnlyPromo || product.isPromo;
      const matchesBio = !showOnlyBio || product.isBio;
      const matchesTags = selectedTags.length === 0 || selectedTags.every(tag => product.badges?.includes(tag));

      return matchesSearch && matchesCategory && matchesPrice && matchesRating && matchesPromo && matchesBio && matchesTags;
    });

    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'price':
          return (a.promoPrice || a.price) - (b.promoPrice || b.price);
        case 'rating':
          return (b.rating || 0) - (a.rating || 0);
        case 'distance':
          return (a.distance || 0) - (b.distance || 0);
        default:
          return a.name.localeCompare(b.name);
      }
    });

    return filtered;
  }, [products, searchQuery, selectedCategory, priceRange, minRating, showOnlyPromo, showOnlyBio, sortBy, selectedTags]);

  useEffect(() => {
    const fetchProducts = async () => {
      setIsLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      setProducts(demoProducts);
      setIsLoading(false);
    };

    fetchProducts();
  }, []);

  useEffect(() => {
    setFilteredProducts(filteredAndSortedProducts);
  }, [filteredAndSortedProducts]);

  // Gestion du scroll pour le bouton retour en haut
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 400);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Skeleton Loader Component inspiré de TastyDaily
  const SkeletonLoader = () => (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-900">
      {/* Header Skeleton */}
      <div className="relative h-80 bg-gradient-to-r from-gray-300 to-gray-400 dark:from-gray-700 dark:to-gray-600 animate-pulse">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10 h-full flex items-center justify-center text-center">
          <div className="max-w-4xl mx-auto px-4">
            <div className="h-16 bg-white/20 rounded-2xl mb-4 animate-pulse"></div>
            <div className="h-8 bg-white/20 rounded-xl mb-6 animate-pulse"></div>
            <div className="flex items-center justify-center gap-4">
              <div className="h-10 w-32 bg-white/20 rounded-full animate-pulse"></div>
              <div className="h-10 w-32 bg-white/20 rounded-full animate-pulse"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters Skeleton */}
      <div className="sticky top-0 z-40 bg-white/95 dark:bg-gray-800/95 backdrop-blur-md border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex flex-col lg:flex-row gap-4 mb-4">
            <div className="flex-1 h-14 bg-gray-200 dark:bg-gray-700 rounded-2xl animate-pulse"></div>
            <div className="flex gap-3">
              <div className="h-14 w-32 bg-gray-200 dark:bg-gray-700 rounded-2xl animate-pulse"></div>
              <div className="h-14 w-20 bg-gray-200 dark:bg-gray-700 rounded-2xl animate-pulse"></div>
            </div>
          </div>

          <div className="flex items-center gap-3 overflow-x-auto pb-2">
            <div className="h-6 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            {[...Array(8)].map((_, i) => (
              <div key={i} className="h-10 w-24 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse flex-shrink-0"></div>
            ))}
          </div>
        </div>
      </div>

      {/* Content Skeleton */}
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Sections dynamiques skeleton */}
        {[...Array(3)].map((_, sectionIndex) => (
          <div key={sectionIndex} className="mb-12">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-2xl animate-pulse"></div>
                <div>
                  <div className="h-6 w-40 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2"></div>
                  <div className="h-4 w-60 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                </div>
              </div>
              <div className="h-6 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden">
                  <div className="h-48 bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
                  <div className="p-4">
                    <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2"></div>
                    <div className="flex items-center justify-between">
                      <div className="h-6 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                      <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}

        {/* Main products grid skeleton */}
        <div className="mb-8">
          <div className="h-8 w-48 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {[...Array(12)].map((_, i) => (
              <div key={i} className="bg-white dark:bg-gray-800 rounded-3xl shadow-lg overflow-hidden">
                <div className="h-56 bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
                <div className="p-6">
                  <div className="flex justify-between items-start mb-3">
                    <div className="flex-1">
                      <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-1"></div>
                      <div className="h-4 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                    </div>
                    <div className="h-6 w-12 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>
                  </div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-4"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-4 w-3/4"></div>
                  <div className="flex items-center justify-between">
                    <div className="h-8 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                    <div className="h-10 w-24 bg-gray-200 dark:bg-gray-700 rounded-2xl animate-pulse"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  if (isLoading) {
    return <SkeletonLoader />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-900">
      {/* Header avec image de fond floue - Style TastyDaily */}
      <div className="relative h-80 bg-gradient-to-r from-green-600 to-blue-600 overflow-hidden">
        <div className="absolute inset-0 bg-black/30"></div>
        <div
          className="absolute inset-0 bg-cover bg-center filter blur-sm"
          style={{
            backgroundImage: 'url(https://images.unsplash.com/photo-1542838132-92c53300491e?w=1200)'
          }}
        ></div>
        <div className="relative z-10 h-full flex items-center justify-center text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto px-4"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-4">
              🛒 LocaFresh Market
            </h1>
            <p className="text-xl md:text-2xl opacity-90 mb-6">
              Découvrez les meilleurs produits locaux près de chez vous
            </p>
            <div className="flex items-center justify-center gap-4 text-lg">
              <span className="bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full">
                🌱 {filteredAndSortedProducts.length} produits frais
              </span>
              <span className="bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full">
                🚚 Livraison rapide
              </span>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Filtres rapides par catégories avec emojis */}
      <motion.div
        className="sticky top-0 z-40 bg-white/95 dark:bg-gray-800/95 backdrop-blur-md border-b border-gray-200 dark:border-gray-700 shadow-lg"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <div className="max-w-7xl mx-auto px-4 py-4">
          {/* Barre de recherche moderne */}
          <div className="flex flex-col lg:flex-row gap-4 mb-4">
            <div className="flex-1 relative">
              <FaSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Rechercher fruits, légumes, viandes, poissons..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-12 pr-4 py-4 bg-white dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-2xl focus:outline-none focus:ring-4 focus:ring-green-100 dark:focus:ring-green-900 focus:border-green-400 transition-all duration-300 text-lg"
              />
            </div>

            {/* Tri et vue */}
            <div className="flex gap-3">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="px-4 py-4 bg-white dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-2xl focus:outline-none focus:ring-2 focus:ring-green-500 text-gray-900 dark:text-white"
                title="Trier par"
              >
                <option value="name">📝 Nom</option>
                <option value="price">💰 Prix</option>
                <option value="rating">⭐ Note</option>
                <option value="distance">📍 Distance</option>
              </select>

              <div className="flex bg-gray-100 dark:bg-gray-700 rounded-2xl p-1">
                <button
                  type="button"
                  onClick={() => setViewMode('grid')}
                  className={`px-4 py-3 rounded-xl transition-all duration-300 ${
                    viewMode === 'grid'
                      ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-md'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                  }`}
                  title="Vue grille"
                >
                  <FaTh />
                </button>
                <button
                  type="button"
                  onClick={() => setViewMode('list')}
                  className={`px-4 py-3 rounded-xl transition-all duration-300 ${
                    viewMode === 'list'
                      ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-md'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                  }`}
                  title="Vue liste"
                >
                  <FaList />
                </button>
              </div>
            </div>
          </div>

          {/* Catégories avec emojis */}
          <div className="flex items-center gap-3 overflow-x-auto pb-2">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300 whitespace-nowrap">Catégories :</span>
            {locaFreshCategories.map((category) => (
              <motion.button
                key={category.id}
                type="button"
                onClick={() => setSelectedCategory(category.id)}
                className={`flex items-center gap-2 px-4 py-2 rounded-full whitespace-nowrap transition-all duration-300 ${
                  selectedCategory === category.id
                    ? `bg-gradient-to-r ${category.color} text-white shadow-lg scale-105`
                    : 'bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                }`}
                whileHover={{ scale: selectedCategory === category.id ? 1.05 : 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <span className="text-lg">{category.emoji}</span>
                <span className="text-sm font-medium">{category.name}</span>
              </motion.button>
            ))}
          </div>

          {/* Tags populaires inspirés de TastyDaily */}
          <div className="flex items-center gap-3 overflow-x-auto pb-2 mt-4">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300 whitespace-nowrap">Tags populaires :</span>
            {popularTags.map((tag) => (
              <motion.button
                key={tag.name}
                type="button"
                onClick={() => toggleTag(tag.name)}
                className={`flex items-center gap-2 px-3 py-1.5 rounded-full whitespace-nowrap text-sm font-medium transition-all duration-300 ${
                  selectedTags.includes(tag.name)
                    ? `${tag.color} shadow-md scale-105 ring-2 ring-offset-2 ring-blue-500 dark:ring-offset-gray-800`
                    : `${tag.color} hover:shadow-md hover:scale-102 opacity-70 hover:opacity-100`
                }`}
                whileHover={{ scale: selectedTags.includes(tag.name) ? 1.05 : 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <span>{tag.emoji}</span>
                <span>{tag.name}</span>
                {selectedTags.includes(tag.name) && (
                  <motion.span
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="text-xs"
                  >
                    ✓
                  </motion.span>
                )}
              </motion.button>
            ))}

            {/* Bouton pour effacer tous les filtres */}
            {(selectedTags.length > 0 || selectedCategory !== 'all' || searchQuery || showOnlyPromo || showOnlyBio || minRating > 0) && (
              <motion.button
                type="button"
                onClick={clearAllFilters}
                className="flex items-center gap-2 px-3 py-1.5 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-all duration-300"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <FaTimes className="w-3 h-3" />
                <span>Effacer tout</span>
              </motion.button>
            )}
          </div>

          {/* Filtres avancés */}
          <div className="flex items-center justify-between mt-4">
            <div className="flex items-center gap-4">
              <button
                type="button"
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-xl transition-all duration-300"
              >
                <FaFilter />
                <span className="hidden sm:inline">Filtres</span>
                {(showOnlyPromo || showOnlyBio || minRating > 0) && (
                  <span className="w-2 h-2 bg-red-500 rounded-full"></span>
                )}
              </button>

              <div className="text-sm text-gray-600 dark:text-gray-400">
                <span className="font-medium">{filteredAndSortedProducts.length}</span> produit{filteredAndSortedProducts.length > 1 ? 's' : ''} trouvé{filteredAndSortedProducts.length > 1 ? 's' : ''}
              </div>
            </div>
          </div>

          {/* Panneau de filtres avancés */}
          <AnimatePresence>
            {showFilters && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
                className="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700"
              >
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Prix maximum: {priceRange[1].toLocaleString()} FCFA
                    </label>
                    <input
                      type="range"
                      min="0"
                      max="50000"
                      value={priceRange[1]}
                      onChange={(e) => setPriceRange([0, Number(e.target.value)])}
                      className="w-full"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Note minimum: {minRating}⭐
                    </label>
                    <input
                      type="range"
                      min="0"
                      max="5"
                      step="0.5"
                      value={minRating}
                      onChange={(e) => setMinRating(Number(e.target.value))}
                      className="w-full"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={showOnlyPromo}
                        onChange={(e) => setShowOnlyPromo(e.target.checked)}
                        className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                      />
                      <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">🏷️ Promotions uniquement</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={showOnlyBio}
                        onChange={(e) => setShowOnlyBio(e.target.checked)}
                        className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                      />
                      <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">🌱 Bio uniquement</span>
                    </label>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </motion.div>

      {/* Sections dynamiques inspirées de TastyDaily */}
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Offres du jour */}
        <motion.section
          className="mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-orange-500 rounded-2xl flex items-center justify-center">
                <FaFire className="text-white text-xl" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Offres du jour</h2>
                <p className="text-gray-600 dark:text-gray-400">Profitez de nos promotions exceptionnelles</p>
              </div>
            </div>
            <Link href="/fr/products?filter=promo" className="text-blue-600 hover:text-blue-700 font-medium">
              Voir tout →
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {filteredAndSortedProducts.filter(p => p.isPromo).slice(0, 4).map((product, index) => (
              <motion.div
                key={`promo-${product.id}`}
                className="group bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 border border-red-100 dark:border-red-900"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -4 }}
              >
                <Link href={`/fr/products/${product.id}`} className="block">
                  <div className="relative h-48 overflow-hidden">
                    <Image
                      src={product.image}
                      alt={product.name}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute top-3 left-3">
                      <span className="bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
                        🏷️ -{Math.round(((product.price - (product.promoPrice || product.price)) / product.price) * 100)}%
                      </span>
                    </div>
                    <button
                      type="button"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        toggleFavorite(product.id);
                      }}
                      className={`absolute top-3 right-3 w-8 h-8 rounded-full shadow-lg transition-all duration-300 ${
                        favorites.has(product.id)
                          ? 'bg-red-500 text-white'
                          : 'bg-white/90 text-gray-600 hover:bg-red-500 hover:text-white'
                      }`}
                      title={favorites.has(product.id) ? 'Retirer des favoris' : 'Ajouter aux favoris'}
                    >
                      <FaHeart className="w-3 h-3 mx-auto" />
                    </button>
                  </div>

                  <div className="p-4">
                    <h3 className="font-bold text-gray-900 dark:text-white mb-2 line-clamp-1">{product.name}</h3>
                    <div className="flex items-center justify-between">
                      <div className="flex flex-col">
                        <span className="text-lg font-bold text-red-600 dark:text-red-400">
                          {formatPrice(product.promoPrice || product.price)}
                        </span>
                        {product.promoPrice && (
                          <span className="text-sm text-gray-500 line-through">
                            {formatPrice(product.price)}
                          </span>
                        )}
                      </div>
                      <button
                        type="button"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          updateCart(product.id, (cart[product.id] || 0) + 1);
                          addNotification(`${product.name} ajouté au panier`, 'success');
                        }}
                        className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-full flex items-center justify-center hover:from-green-600 hover:to-emerald-600 transition-all duration-300"
                        title="Ajouter au panier"
                      >
                        <FaPlus className="w-3 h-3" />
                      </button>
                    </div>
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* Produits populaires */}
        <motion.section
          className="mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl flex items-center justify-center">
                <span className="text-white text-xl">🔥</span>
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Produits populaires</h2>
                <p className="text-gray-600 dark:text-gray-400">Les plus appréciés par nos clients</p>
              </div>
            </div>
            <Link href="/fr/products?sort=rating" className="text-blue-600 hover:text-blue-700 font-medium">
              Voir tout →
            </Link>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
            {filteredAndSortedProducts
              .filter(p => p.rating >= 4.5)
              .slice(0, 6)
              .map((product, index) => (
              <motion.div
                key={`popular-${product.id}`}
                className="group bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500 border border-orange-100 dark:border-orange-900"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -8, scale: 1.02 }}
              >
                <div className="relative h-48 md:h-56 overflow-hidden">
                  <Image
                    src={product.image}
                    alt={product.name}
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute top-3 left-3">
                    <span className="bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
                      ⭐ {product.rating}
                    </span>
                  </div>
                  <button
                    type="button"
                    onClick={() => toggleFavorite(product.id)}
                    className={`absolute top-3 right-3 w-8 h-8 rounded-full shadow-lg transition-all duration-300 ${
                      favorites.has(product.id)
                        ? 'bg-red-500 text-white'
                        : 'bg-white/90 text-gray-600 hover:bg-red-500 hover:text-white'
                    }`}
                    title={favorites.has(product.id) ? 'Retirer des favoris' : 'Ajouter aux favoris'}
                  >
                    <FaHeart className="w-3 h-3 mx-auto" />
                  </button>
                </div>

                <div className="p-4">
                  <h3 className="font-bold text-gray-900 dark:text-white mb-2 line-clamp-1">{product.name}</h3>
                  <p className="text-xs text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">{product.description}</p>
                  <div className="flex items-center justify-between">
                    <div className="flex flex-col">
                      {product.isPromo && product.promoPrice ? (
                        <>
                          <span className="text-lg font-bold text-red-600 dark:text-red-400">
                            {formatPrice(product.promoPrice)}
                          </span>
                          <span className="text-sm text-gray-500 line-through">
                            {formatPrice(product.price)}
                          </span>
                        </>
                      ) : (
                        <span className="text-lg font-bold text-gray-900 dark:text-white">
                          {formatPrice(product.price)}
                        </span>
                      )}
                    </div>
                    <button
                      type="button"
                      onClick={() => {
                        updateCart(product.id, (cart[product.id] || 0) + 1);
                        addNotification(`${product.name} ajouté au panier`, 'success');
                      }}
                      className="px-4 py-2 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-xl text-sm font-medium hover:from-orange-600 hover:to-red-600 transition-all duration-300 shadow-md hover:shadow-lg"
                      title="Ajouter au panier"
                    >
                      Ajouter
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* Section Légumes Frais du Jour - Style TastyDaily */}
        <motion.section
          className="mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center">
                <span className="text-white text-xl">🥬</span>
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Légumes Frais du Jour</h2>
                <p className="text-gray-600 dark:text-gray-400">Fraîcheur garantie, directement des producteurs locaux</p>
              </div>
            </div>
            <Link href="/fr/products?category=Légumes" className="text-blue-600 hover:text-blue-700 font-medium">
              Voir tout →
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {filteredAndSortedProducts
              .filter(p => p.category === 'Légumes')
              .slice(0, 8)
              .map((product, index) => (
              <motion.div
                key={`vegetables-${product.id}`}
                className="group bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500 border border-green-100 dark:border-green-900"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -8, scale: 1.02 }}
              >
                <Link href={`/fr/products/${product.id}`} className="block">
                  <div className="relative h-48 overflow-hidden">
                    <Image
                      src={product.image}
                      alt={product.name}
                      fill
                      className="object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                    {product.isNew && (
                      <div className="absolute top-3 left-3">
                        <span className="bg-gradient-to-r from-green-500 to-emerald-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
                          ✨ Nouveau
                        </span>
                      </div>
                    )}
                    {product.isBio && (
                      <div className="absolute top-3 left-3">
                        <span className="bg-gradient-to-r from-green-600 to-green-700 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
                          🌱 Bio
                        </span>
                      </div>
                    )}
                    <button
                      type="button"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        toggleFavorite(product.id);
                      }}
                      className={`absolute top-3 right-3 w-8 h-8 rounded-full shadow-lg transition-all duration-300 ${
                        favorites.has(product.id)
                          ? 'bg-red-500 text-white'
                          : 'bg-white/90 text-gray-600 hover:bg-red-500 hover:text-white'
                      }`}
                      title={favorites.has(product.id) ? 'Retirer des favoris' : 'Ajouter aux favoris'}
                    >
                      <FaHeart className="w-3 h-3 mx-auto" />
                    </button>
                  </div>

                  <div className="p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex-1">
                        <h3 className="font-bold text-gray-900 dark:text-white mb-1 line-clamp-1">{product.name}</h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">{product.seller}</p>
                      </div>
                      <div className="flex items-center gap-1 bg-yellow-100 dark:bg-yellow-900 px-2 py-1 rounded-full">
                        <FaStar className="w-3 h-3 text-yellow-500" />
                        <span className="text-xs font-medium text-yellow-700 dark:text-yellow-300">{product.rating}</span>
                      </div>
                    </div>

                    <p className="text-xs text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">{product.description}</p>

                    <div className="flex flex-wrap gap-1 mb-3">
                      {product.badges?.slice(0, 2).map((badge) => (
                        <span
                          key={badge}
                          className={`text-xs px-2 py-1 rounded-full ${
                            popularTags.find(tag => tag.name === badge)?.color || 'bg-gray-100 text-gray-800'
                          }`}
                        >
                          {badge}
                        </span>
                      ))}
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex flex-col">
                        <span className="text-lg font-bold text-green-600 dark:text-green-400">
                          {formatPrice(product.price)}
                        </span>
                        <span className="text-xs text-gray-500">par {product.unit}</span>
                      </div>
                      <button
                        type="button"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          updateCart(product.id, (cart[product.id] || 0) + 1);
                          addNotification(`${product.name} ajouté au panier`, 'success');
                        }}
                        className="px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-xl text-sm font-medium hover:from-green-600 hover:to-emerald-600 transition-all duration-300 shadow-md hover:shadow-lg"
                        title="Ajouter au panier"
                      >
                        Ajouter
                      </button>
                    </div>
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* Section Fruits Tropicaux du Jour - Style TastyDaily */}
        <motion.section
          className="mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
        >
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl flex items-center justify-center">
                <span className="text-white text-xl">🍎</span>
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Fruits Tropicaux du Jour</h2>
                <p className="text-gray-600 dark:text-gray-400">Saveurs exotiques et vitamines naturelles, directement des vergers</p>
              </div>
            </div>
            <Link href="/fr/products?category=Fruits" className="text-blue-600 hover:text-blue-700 font-medium">
              Voir tout →
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {filteredAndSortedProducts
              .filter(p => p.category === 'Fruits')
              .slice(0, 8)
              .map((product, index) => (
              <motion.div
                key={`fruits-${product.id}`}
                className="group bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500 border border-orange-100 dark:border-orange-900"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -8, scale: 1.02 }}
              >
                <Link href={`/fr/products/${product.id}`} className="block">
                  <div className="relative h-48 overflow-hidden">
                    <Image
                      src={product.image}
                      alt={product.name}
                      fill
                      className="object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                    {product.isNew && (
                      <div className="absolute top-3 left-3">
                        <span className="bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
                          ✨ Nouveau
                        </span>
                      </div>
                    )}
                    {product.isBio && (
                      <div className="absolute top-3 left-3">
                        <span className="bg-gradient-to-r from-green-600 to-green-700 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
                          🌱 Bio
                        </span>
                      </div>
                    )}
                    <button
                      type="button"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        toggleFavorite(product.id);
                      }}
                      className={`absolute top-3 right-3 w-8 h-8 rounded-full shadow-lg transition-all duration-300 ${
                        favorites.has(product.id)
                          ? 'bg-red-500 text-white'
                          : 'bg-white/90 text-gray-600 hover:bg-red-500 hover:text-white'
                      }`}
                      title={favorites.has(product.id) ? 'Retirer des favoris' : 'Ajouter aux favoris'}
                    >
                      <FaHeart className="w-3 h-3 mx-auto" />
                    </button>
                  </div>

                  <div className="p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex-1">
                        <h3 className="font-bold text-gray-900 dark:text-white mb-1 line-clamp-1">{product.name}</h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">{product.seller}</p>
                      </div>
                      <div className="flex items-center gap-1 bg-yellow-100 dark:bg-yellow-900 px-2 py-1 rounded-full">
                        <FaStar className="w-3 h-3 text-yellow-500" />
                        <span className="text-xs font-medium text-yellow-700 dark:text-yellow-300">{product.rating}</span>
                      </div>
                    </div>

                    <p className="text-xs text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">{product.description}</p>

                    <div className="flex flex-wrap gap-1 mb-3">
                      {product.badges?.slice(0, 2).map((badge) => (
                        <span
                          key={badge}
                          className={`text-xs px-2 py-1 rounded-full ${
                            popularTags.find(tag => tag.name === badge)?.color || 'bg-gray-100 text-gray-800'
                          }`}
                        >
                          {badge}
                        </span>
                      ))}
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex flex-col">
                        <span className="text-lg font-bold text-orange-600 dark:text-orange-400">
                          {formatPrice(product.price)}
                        </span>
                        <span className="text-xs text-gray-500">par {product.unit}</span>
                      </div>
                      <button
                        type="button"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          updateCart(product.id, (cart[product.id] || 0) + 1);
                          addNotification(`${product.name} ajouté au panier`, 'success');
                        }}
                        className="px-4 py-2 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-xl text-sm font-medium hover:from-orange-600 hover:to-red-600 transition-all duration-300 shadow-md hover:shadow-lg"
                        title="Ajouter au panier"
                      >
                        Ajouter
                      </button>
                    </div>
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* Section Viandes Premium du Jour - Style TastyDaily */}
        <motion.section
          className="mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
        >
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-red-600 to-red-700 rounded-2xl flex items-center justify-center">
                <span className="text-white text-xl">🥩</span>
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Viandes Premium du Jour</h2>
                <p className="text-gray-600 dark:text-gray-400">Qualité exceptionnelle, fraîcheur garantie, directement des éleveurs</p>
              </div>
            </div>
            <Link href="/fr/products?category=Viandes" className="text-blue-600 hover:text-blue-700 font-medium">
              Voir tout →
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredAndSortedProducts
              .filter(p => p.category === 'Viandes')
              .slice(0, 6)
              .map((product, index) => (
              <motion.div
                key={`meat-${product.id}`}
                className="group bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500 border border-red-100 dark:border-red-900"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -8, scale: 1.02 }}
              >
                <Link href={`/fr/products/${product.id}`} className="block">
                  <div className="relative h-56 overflow-hidden">
                    <Image
                      src={product.image}
                      alt={product.name}
                      fill
                      className="object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                    {product.isNew && (
                      <div className="absolute top-3 left-3">
                        <span className="bg-gradient-to-r from-red-600 to-red-700 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
                          ✨ Nouveau
                        </span>
                      </div>
                    )}
                    <div className="absolute top-3 left-3">
                      <span className="bg-gradient-to-r from-red-600 to-red-700 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
                        🥩 Premium
                      </span>
                    </div>
                    <button
                      type="button"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        toggleFavorite(product.id);
                      }}
                      className={`absolute top-3 right-3 w-8 h-8 rounded-full shadow-lg transition-all duration-300 ${
                        favorites.has(product.id)
                          ? 'bg-red-500 text-white'
                          : 'bg-white/90 text-gray-600 hover:bg-red-500 hover:text-white'
                      }`}
                      title={favorites.has(product.id) ? 'Retirer des favoris' : 'Ajouter aux favoris'}
                    >
                      <FaHeart className="w-3 h-3 mx-auto" />
                    </button>
                  </div>

                  <div className="p-5">
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex-1">
                        <h3 className="font-bold text-gray-900 dark:text-white mb-1 line-clamp-1">{product.name}</h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">{product.seller}</p>
                      </div>
                      <div className="flex items-center gap-1 bg-yellow-100 dark:bg-yellow-900 px-2 py-1 rounded-full">
                        <FaStar className="w-3 h-3 text-yellow-500" />
                        <span className="text-xs font-medium text-yellow-700 dark:text-yellow-300">{product.rating}</span>
                      </div>
                    </div>

                    <p className="text-xs text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">{product.description}</p>

                    <div className="flex flex-wrap gap-1 mb-4">
                      {product.badges?.slice(0, 3).map((badge) => (
                        <span
                          key={badge}
                          className={`text-xs px-2 py-1 rounded-full ${
                            popularTags.find(tag => tag.name === badge)?.color || 'bg-gray-100 text-gray-800'
                          }`}
                        >
                          {badge}
                        </span>
                      ))}
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex flex-col">
                        <span className="text-xl font-bold text-red-600 dark:text-red-400">
                          {formatPrice(product.price)}
                        </span>
                        <span className="text-xs text-gray-500">par {product.unit}</span>
                      </div>
                      <button
                        type="button"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          updateCart(product.id, (cart[product.id] || 0) + 1);
                          addNotification(`${product.name} ajouté au panier`, 'success');
                        }}
                        className="px-5 py-2.5 bg-gradient-to-r from-red-600 to-red-700 text-white rounded-xl text-sm font-medium hover:from-red-700 hover:to-red-800 transition-all duration-300 shadow-md hover:shadow-lg"
                        title="Ajouter au panier"
                      >
                        Ajouter
                      </button>
                    </div>
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* Grille principale des produits */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.9 }}
        >
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Tous nos produits</h2>
              <p className="text-gray-600 dark:text-gray-400">Découvrez notre sélection complète</p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredAndSortedProducts.map((product, index) => (
              <motion.div
                key={product.id}
                className="group bg-white dark:bg-gray-800 rounded-3xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.05 }}
                whileHover={{ y: -8 }}
              >
                <div className="relative h-56 overflow-hidden">
                  <Image
                    src={product.image}
                    alt={product.name}
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  {product.isPromo && (
                    <div className="absolute top-3 left-3">
                      <span className="bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
                        🏷️ -{Math.round(((product.price - (product.promoPrice || product.price)) / product.price) * 100)}%
                      </span>
                    </div>
                  )}
                  {product.isNew && (
                    <div className="absolute top-3 left-3">
                      <span className="bg-gradient-to-r from-green-500 to-emerald-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
                        ✨ Nouveau
                      </span>
                    </div>
                  )}
                  <button
                    type="button"
                    onClick={() => toggleFavorite(product.id)}
                    className={`absolute top-3 right-3 w-10 h-10 rounded-full shadow-lg transition-all duration-300 ${
                      favorites.has(product.id)
                        ? 'bg-red-500 text-white'
                        : 'bg-white/90 text-gray-600 hover:bg-red-500 hover:text-white'
                    }`}
                    title={favorites.has(product.id) ? 'Retirer des favoris' : 'Ajouter aux favoris'}
                  >
                    <FaHeart className="w-4 h-4 mx-auto" />
                  </button>
                </div>

                <div className="p-6">
                  <div className="flex justify-between items-start mb-3">
                    <div className="flex-1">
                      <h3 className="font-bold text-gray-900 dark:text-white mb-1 line-clamp-1">{product.name}</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">{product.seller}</p>
                    </div>
                    <div className="flex items-center gap-1 bg-yellow-100 dark:bg-yellow-900 px-2 py-1 rounded-full">
                      <FaStar className="w-3 h-3 text-yellow-500" />
                      <span className="text-xs font-medium text-yellow-700 dark:text-yellow-300">{product.rating}</span>
                    </div>
                  </div>

                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">{product.description}</p>

                  <div className="flex flex-wrap gap-1 mb-4">
                    {product.badges?.slice(0, 2).map((badge) => (
                      <span
                        key={badge}
                        className={`text-xs px-2 py-1 rounded-full ${
                          popularTags.find(tag => tag.name === badge)?.color || 'bg-gray-100 text-gray-800'
                        }`}
                      >
                        {badge}
                      </span>
                    ))}
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex flex-col">
                      {product.isPromo && product.promoPrice ? (
                        <>
                          <span className="text-xl font-bold text-red-600 dark:text-red-400">
                            {formatPrice(product.promoPrice)}
                          </span>
                          <span className="text-sm text-gray-500 line-through">
                            {formatPrice(product.price)}
                          </span>
                        </>
                      ) : (
                        <span className="text-xl font-bold text-gray-900 dark:text-white">
                          {formatPrice(product.price)}
                        </span>
                      )}
                      <span className="text-xs text-gray-500">par {product.unit}</span>
                    </div>

                    <div className="flex items-center gap-2">
                      {cart[product.id] > 0 && (
                        <div className="flex items-center gap-2 bg-gray-100 dark:bg-gray-700 rounded-xl px-3 py-2">
                          <button
                            type="button"
                            onClick={() => updateCart(product.id, cart[product.id] - 1)}
                            className="w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors"
                          >
                            <FaMinus className="w-2 h-2" />
                          </button>
                          <span className="text-sm font-medium text-gray-900 dark:text-white min-w-[20px] text-center">
                            {cart[product.id]}
                          </span>
                          <button
                            type="button"
                            onClick={() => updateCart(product.id, cart[product.id] + 1)}
                            className="w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center hover:bg-green-600 transition-colors"
                          >
                            <FaPlus className="w-2 h-2" />
                          </button>
                        </div>
                      )}

                      <button
                        type="button"
                        onClick={() => {
                          updateCart(product.id, (cart[product.id] || 0) + 1);
                          addNotification(`${product.name} ajouté au panier`, 'success');
                        }}
                        className="px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-2xl font-medium hover:from-green-600 hover:to-emerald-600 transition-all duration-300 shadow-md hover:shadow-lg"
                        title="Ajouter au panier"
                      >
                        <FaShoppingCart className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.section>
      </div>

      {/* Notifications Toast */}
      <AnimatePresence>
        {notifications.map((notification) => (
          <motion.div
            key={notification.id}
            initial={{ opacity: 0, x: 300 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 300 }}
            className={`fixed top-4 right-4 z-50 px-6 py-4 rounded-2xl shadow-lg ${
              notification.type === 'success'
                ? 'bg-green-500 text-white'
                : 'bg-red-500 text-white'
            }`}
          >
            <div className="flex items-center gap-3">
              <span className="text-xl">
                {notification.type === 'success' ? '✅' : '❌'}
              </span>
              <span className="font-medium">{notification.message}</span>
            </div>
          </motion.div>
        ))}
      </AnimatePresence>

      {/* Bouton retour en haut */}
      <AnimatePresence>
        {showScrollTop && (
          <motion.button
            type="button"
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0 }}
            onClick={scrollToTop}
            className="fixed bottom-8 right-8 z-40 w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center"
            title="Retour en haut"
          >
            <FaArrowRight className="w-4 h-4 transform -rotate-90" />
          </motion.button>
        )}
      </AnimatePresence>
    </div>
  );
}
