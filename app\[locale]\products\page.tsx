"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { useTranslations } from 'next-intl';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { FaSearch, FaFilter, FaMapMarkerAlt, FaStar, FaShoppingCart, FaHeart, FaTh, FaList, FaSort, FaTimes, FaPlus, FaMinus, FaFire, FaTag, FaLeaf, FaArrowRight } from 'react-icons/fa';
import { useOfflineMode } from '@/contexts/OfflineModeContext';

interface Product {
  id: string;
  name: string;
  price: number;
  category: string;
  seller: string;
  location: string;
  rating: number;
  image: string;
  inStock: boolean;
  description: string;
  unit?: string;
  weight?: string;
  isPromo?: boolean;
  promoPrice?: number;
  badges?: string[];
  isNew?: boolean;
  isBio?: boolean;
  distance?: number;
  deliveryTime?: string;
}

export default function ProductsPage() {
  const t = useTranslations();
  const { isOnline, offlineData } = useOfflineMode();

  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isLoading, setIsLoading] = useState(true);
  const [showFilters, setShowFilters] = useState(false);
  const [sortBy, setSortBy] = useState<'name' | 'price' | 'rating' | 'distance'>('name');
  const [priceRange, setPriceRange] = useState([0, 50000]);
  const [minRating, setMinRating] = useState(0);
  const [showOnlyPromo, setShowOnlyPromo] = useState(false);
  const [showOnlyBio, setShowOnlyBio] = useState(false);
  const [favorites, setFavorites] = useState<Set<string>>(new Set());
  const [cart, setCart] = useState<Record<string, number>>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(true);
  const [notifications, setNotifications] = useState<Array<{id: string, message: string, type: 'success' | 'error'}>>([]);
  const productsPerPage = 12;

  // Tags populaires inspirés de TastyDaily
  const popularTags = [
    { name: 'Bio', emoji: '🌱', color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' },
    { name: 'Promo', emoji: '🏷️', color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' },
    { name: 'Local', emoji: '📍', color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' },
    { name: 'Frais', emoji: '❄️', color: 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-200' },
    { name: 'Premium', emoji: '⭐', color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' },
    { name: 'Traditionnel', emoji: '🏛️', color: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' },
    { name: 'Artisanal', emoji: '🎨', color: 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200' },
    { name: 'Nouveau', emoji: '✨', color: 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200' },
  ];

  // Catégories LocaFresh avec emojis - Inspiré de TastyDaily
  const locaFreshCategories = [
    { id: 'all', name: 'Tout', emoji: '🛒', color: 'from-gray-400 to-gray-500' },
    { id: 'Fruits', name: 'Fruits', emoji: '🍎', color: 'from-red-400 to-orange-500' },
    { id: 'Légumes', name: 'Légumes', emoji: '🥬', color: 'from-green-400 to-green-500' },
    { id: 'Viandes', name: 'Viandes', emoji: '🥩', color: 'from-red-500 to-red-600' },
    { id: 'Volaille', name: 'Volaille', emoji: '🐔', color: 'from-yellow-400 to-orange-500' },
    { id: 'Poissons', name: 'Poissons', emoji: '🐟', color: 'from-blue-400 to-blue-500' },
    { id: 'Boulangerie', name: 'Boulangerie', emoji: '🍞', color: 'from-amber-400 to-amber-500' },
    { id: 'Boissons', name: 'Boissons', emoji: '🥤', color: 'from-cyan-400 to-blue-500' },
    { id: 'Artisanat', name: 'Artisanat', emoji: '🎨', color: 'from-purple-400 to-pink-500' },
  ];

  // Données de démonstration enrichies pour LocaFresh
  const demoProducts: Product[] = [
    {
      id: '1',
      name: 'Mangues Bio Kent',
      price: 2500,
      category: 'Fruits',
      seller: 'Ferme Bio Diallo',
      location: 'Thiès',
      rating: 4.8,
      image: 'https://images.unsplash.com/photo-1553279768-865429fa0078?w=400',
      inStock: true,
      description: 'Mangues biologiques fraîches, cultivées sans pesticides',
      unit: 'kg',
      weight: '1kg',
      isPromo: true,
      promoPrice: 2000,
      badges: ['Bio', 'Promo', 'Local'],
      isNew: false,
      isBio: true,
      distance: 2.5,
      deliveryTime: '20-30 min'
    },
    {
      id: '2',
      name: 'Tomates Cerises Bio',
      price: 1800,
      category: 'Légumes',
      seller: 'Jardin de Fatou',
      location: 'Rufisque',
      rating: 4.6,
      image: 'https://images.unsplash.com/photo-1546470427-e5b89b618b84?w=400',
      inStock: true,
      description: 'Tomates cerises fraîches du jour, cultivées en agriculture biologique',
      unit: 'barquette',
      weight: '250g',
      badges: ['Bio', 'Frais', 'Local'],
      isNew: true,
      isBio: true,
      distance: 1.8,
      deliveryTime: '15-25 min'
    },
    {
      id: '3',
      name: 'Pain Traditionnel au Feu de Bois',
      price: 500,
      category: 'Boulangerie',
      seller: 'Boulangerie Artisanale',
      location: 'Dakar',
      rating: 4.9,
      image: 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400',
      inStock: true,
      description: 'Pain traditionnel sénégalais cuit au feu de bois selon la recette ancestrale',
      unit: 'pièce',
      badges: ['Artisanal', 'Traditionnel'],
      isNew: false,
      distance: 1.2,
      deliveryTime: '10-20 min'
    },
    {
      id: '4',
      name: 'Bissap Artisanal aux Épices',
      price: 1200,
      category: 'Boissons',
      seller: 'Les Délices de Khadija',
      location: 'Saint-Louis',
      rating: 4.7,
      image: 'https://images.unsplash.com/photo-1622597467836-f3285f2131b8?w=400',
      inStock: false,
      description: 'Bissap artisanal aux épices naturelles, sans conservateurs',
      unit: 'bouteille',
      weight: '500ml',
      badges: ['Artisanal', 'Naturel'],
      isNew: false,
      distance: 4.2,
      deliveryTime: '30-40 min'
    },
    {
      id: '5',
      name: 'Thiof Frais du Matin',
      price: 3500,
      category: 'Poissons',
      seller: 'Pêcheurs de Soumbédioune',
      location: 'Dakar',
      rating: 4.5,
      image: 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=400',
      inStock: true,
      description: 'Thiof fraîchement pêché ce matin, qualité premium',
      unit: 'kg',
      badges: ['Frais', 'Premium', 'Local'],
      isNew: false,
      distance: 3.2,
      deliveryTime: '25-35 min'
    },
    {
      id: '6',
      name: 'Sac Artisanal en Raphia',
      price: 8000,
      category: 'Artisanat',
      seller: 'Atelier Sénégal Authentique',
      location: 'Kaolack',
      rating: 4.8,
      image: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400',
      inStock: true,
      description: 'Sac artisanal en raphia tressé à la main, design traditionnel',
      unit: 'pièce',
      badges: ['Artisanal', 'Fait Main', 'Authentique'],
      isNew: true,
      distance: 5.1,
      deliveryTime: '40-50 min'
    },
    {
      id: '7',
      name: 'Bananes Bio Plantain',
      price: 1500,
      category: 'Fruits',
      seller: 'Coopérative Fruits Bio',
      location: 'Ziguinchor',
      rating: 4.4,
      image: 'https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=400',
      inStock: true,
      description: 'Bananes plantain biologiques, parfaites pour la cuisine',
      unit: 'régime',
      weight: '2kg',
      isPromo: true,
      promoPrice: 1200,
      badges: ['Bio', 'Promo'],
      isNew: false,
      isBio: true,
      distance: 6.8,
      deliveryTime: '45-60 min'
    },
    {
      id: '8',
      name: 'Poulet Fermier Bio',
      price: 12000,
      category: 'Volaille',
      seller: 'Ferme Avicole Bio',
      location: 'Mbour',
      rating: 4.7,
      image: 'https://images.unsplash.com/photo-1604503468506-a8da13d82791?w=400',
      inStock: true,
      description: 'Poulet fermier élevé en liberté, nourri aux grains bio',
      unit: 'kg',
      badges: ['Bio', 'Fermier', 'Premium'],
      isNew: false,
      isBio: true,
      distance: 3.8,
      deliveryTime: '30-40 min'
    },
    // Ajout de nouveaux produits pour enrichir l'expérience
    {
      id: '9',
      name: 'Ananas Victoria',
      price: 3000,
      category: 'Fruits',
      seller: 'Plantation Tropicale',
      location: 'Casamance',
      rating: 4.9,
      image: 'https://images.unsplash.com/photo-1550258987-190a2d41a8ba?w=400',
      inStock: true,
      description: 'Ananas Victoria extra sucré, cultivé en Casamance',
      unit: 'pièce',
      weight: '1.5kg',
      badges: ['Populaire', 'Sucré', 'Local'],
      isNew: false,
      distance: 8.2,
      deliveryTime: '60-75 min'
    },
    {
      id: '10',
      name: 'Crevettes Fraîches',
      price: 8500,
      category: 'Poissons',
      seller: 'Pêcheurs de Joal',
      location: 'Joal-Fadiouth',
      rating: 4.6,
      image: 'https://images.unsplash.com/photo-1565680018434-b513d5e5fd47?w=400',
      inStock: true,
      description: 'Crevettes fraîches pêchées dans les eaux sénégalaises',
      unit: 'kg',
      badges: ['Frais', 'Premium', 'Populaire'],
      isNew: true,
      distance: 4.5,
      deliveryTime: '35-45 min'
    },
    {
      id: '11',
      name: 'Miel Pur Local 500g',
      price: 4500,
      category: 'Artisanat',
      seller: 'Apiculteurs de Casamance',
      location: 'Casamance',
      rating: 4.9,
      image: 'https://images.unsplash.com/photo-1587049352846-4a222e784d38?w=400',
      inStock: true,
      description: 'Miel pur et naturel récolté dans les ruches traditionnelles de Casamance',
      unit: 'pot',
      weight: '500g',
      badges: ['100% Naturel', 'Local', 'Artisanal'],
      isNew: false,
      distance: 8.2,
      deliveryTime: '60-75 min'
    },
    {
      id: '12',
      name: 'Jus de Gingembre',
      price: 800,
      category: 'Boissons',
      seller: 'Boissons Naturelles Dakar',
      location: 'Dakar',
      rating: 4.3,
      image: 'https://images.unsplash.com/photo-1570197788417-0e82375c9371?w=400',
      inStock: true,
      description: 'Jus de gingembre frais, énergisant et rafraîchissant',
      unit: 'bouteille',
      weight: '330ml',
      isPromo: true,
      promoPrice: 600,
      badges: ['Promo', 'Naturel', 'Énergisant'],
      isNew: false,
      distance: 2.1,
      deliveryTime: '15-25 min'
    },
    {
      id: '13',
      name: 'Croissants Artisanaux',
      price: 1500,
      category: 'Boulangerie',
      seller: 'Pâtisserie Française',
      location: 'Dakar',
      rating: 4.7,
      image: 'https://images.unsplash.com/photo-1555507036-ab794f4afe5a?w=400',
      inStock: true,
      description: 'Croissants pur beurre, préparés selon la tradition française',
      unit: 'lot de 6',
      badges: ['Artisanal', 'Pur Beurre', 'Nouveau'],
      isNew: true,
      distance: 1.5,
      deliveryTime: '10-20 min'
    },
    {
      id: '14',
      name: 'Oignons Rouges',
      price: 900,
      category: 'Légumes',
      seller: 'Maraîchers de Niayes',
      location: 'Niayes',
      rating: 4.2,
      image: 'https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=400',
      inStock: true,
      description: 'Oignons rouges frais des Niayes, parfaits pour vos plats',
      unit: 'kg',
      badges: ['Local', 'Frais'],
      isNew: false,
      distance: 3.7,
      deliveryTime: '25-35 min'
    },
    {
      id: '15',
      name: 'Œufs de Poules Élevées au Sol',
      price: 2200,
      category: 'Volaille',
      seller: 'Ferme Avicole Naturelle',
      location: 'Thiès',
      rating: 4.8,
      image: 'https://images.unsplash.com/photo-1582722872445-44dc5f7e3c8f?w=400',
      inStock: true,
      description: 'Œufs frais de poules élevées au sol, riches en oméga-3',
      unit: 'douzaine',
      badges: ['Bio', 'Naturel', 'Oméga-3'],
      isNew: false,
      isBio: true,
      distance: 2.8,
      deliveryTime: '20-30 min'
    },
    // Nouveaux produits inspirés de TastyDaily pour enrichir l'expérience
    {
      id: '16',
      name: 'Fraises Bio 250g',
      price: 3500,
      category: 'Fruits',
      seller: 'Ferme Bio des Niayes',
      location: 'Niayes',
      rating: 4.9,
      image: 'https://images.unsplash.com/photo-1464965911861-746a04b4bca6?w=400',
      inStock: true,
      description: 'Fraises biologiques fraîches, cultivées sans pesticides dans les Niayes',
      unit: 'barquette',
      weight: '250g',
      isPromo: true,
      promoPrice: 2800,
      badges: ['Bio', 'Promo', 'Frais', 'Local'],
      isNew: false,
      isBio: true,
      distance: 2.1,
      deliveryTime: '15-25 min'
    },
    {
      id: '17',
      name: 'Avocat Hass 2 unités',
      price: 2500,
      category: 'Fruits',
      seller: 'Plantation Tropicale',
      location: 'Casamance',
      rating: 4.8,
      image: 'https://images.unsplash.com/photo-1523049673857-eb18f1d7b578?w=400',
      inStock: true,
      description: 'Avocats Hass mûrs à point, riches en vitamines et minéraux',
      unit: 'lot',
      weight: '2 unités',
      badges: ['Premium', 'Nutritif', 'Local'],
      isNew: true,
      distance: 8.5,
      deliveryTime: '60-75 min'
    },
    {
      id: '18',
      name: 'Carottes Bio 1kg',
      price: 1200,
      category: 'Légumes',
      seller: 'Maraîchers Bio Thiès',
      location: 'Thiès',
      rating: 4.5,
      image: 'https://images.unsplash.com/photo-1445282768818-728615cc910a?w=400',
      inStock: true,
      description: 'Carottes biologiques croquantes, parfaites pour vos plats et jus',
      unit: 'kg',
      badges: ['Bio', 'Croquant', 'Local'],
      isNew: false,
      isBio: true,
      distance: 2.8,
      deliveryTime: '20-30 min'
    },
    {
      id: '19',
      name: 'Radis Rouge 1 botte',
      price: 800,
      category: 'Légumes',
      seller: 'Jardin de Fatou',
      location: 'Rufisque',
      rating: 4.3,
      image: 'https://images.unsplash.com/photo-1598170845058-32b9d6a5da37?w=400',
      inStock: true,
      description: 'Radis rouges frais et croquants, parfaits pour les salades',
      unit: 'botte',
      badges: ['Frais', 'Croquant', 'Local'],
      isNew: false,
      distance: 1.8,
      deliveryTime: '15-25 min'
    },
    {
      id: '20',
      name: 'Champignons de Paris 500g',
      price: 2200,
      category: 'Légumes',
      seller: 'Champignonnière Moderne',
      location: 'Dakar',
      rating: 4.6,
      image: 'https://images.unsplash.com/photo-1506976785307-8732e854ad03?w=400',
      inStock: true,
      description: 'Champignons de Paris frais, cultivés localement en serre',
      unit: 'barquette',
      weight: '500g',
      badges: ['Frais', 'Cultivé Local', 'Premium'],
      isNew: true,
      distance: 1.5,
      deliveryTime: '10-20 min'
    },
    {
      id: '21',
      name: 'Bœuf Haché Premium 500g',
      price: 4500,
      category: 'Viandes',
      seller: 'Boucherie Halal Premium',
      location: 'Dakar',
      rating: 4.7,
      image: 'https://images.unsplash.com/photo-1603048297172-c92544798d5a?w=400',
      inStock: true,
      description: 'Bœuf haché premium, viande fraîche de qualité supérieure',
      unit: 'barquette',
      weight: '500g',
      badges: ['Halal', 'Premium', 'Frais'],
      isNew: false,
      distance: 1.2,
      deliveryTime: '10-20 min'
    },
    {
      id: '22',
      name: 'Agneau Côtelettes 1kg',
      price: 8500,
      category: 'Viandes',
      seller: 'Boucherie Traditionnelle',
      location: 'Thiès',
      rating: 4.8,
      image: 'https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?w=400',
      inStock: true,
      description: 'Côtelettes d\'agneau tendres, élevage local traditionnel',
      unit: 'kg',
      badges: ['Halal', 'Traditionnel', 'Tendre'],
      isNew: false,
      distance: 2.5,
      deliveryTime: '20-30 min'
    },
    {
      id: '23',
      name: 'Saumon Frais 1kg',
      price: 12000,
      category: 'Poissons',
      seller: 'Poissonnerie Atlantique',
      location: 'Dakar',
      rating: 4.9,
      image: 'https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?w=400',
      inStock: true,
      description: 'Saumon frais de l\'Atlantique, qualité restaurant',
      unit: 'kg',
      badges: ['Premium', 'Frais', 'Restaurant'],
      isNew: true,
      distance: 3.2,
      deliveryTime: '25-35 min'
    },
    {
      id: '24',
      name: 'Crevettes Géantes 500g',
      price: 9500,
      category: 'Poissons',
      seller: 'Pêcheurs de Joal',
      location: 'Joal-Fadiouth',
      rating: 4.7,
      image: 'https://images.unsplash.com/photo-1565680018434-b513d5e5fd47?w=400',
      inStock: true,
      description: 'Crevettes géantes fraîches, pêchées dans nos eaux',
      unit: 'barquette',
      weight: '500g',
      isPromo: true,
      promoPrice: 8000,
      badges: ['Promo', 'Géantes', 'Frais', 'Local'],
      isNew: false,
      distance: 4.5,
      deliveryTime: '35-45 min'
    },
    {
      id: '25',
      name: 'Baguette Traditionnelle',
      price: 300,
      category: 'Boulangerie',
      seller: 'Boulangerie du Coin',
      location: 'Dakar',
      rating: 4.4,
      image: 'https://images.unsplash.com/photo-1549931319-a545dcf3bc73?w=400',
      inStock: true,
      description: 'Baguette traditionnelle croustillante, cuite au four à bois',
      unit: 'pièce',
      badges: ['Traditionnel', 'Croustillant', 'Four à Bois'],
      isNew: false,
      distance: 0.8,
      deliveryTime: '5-15 min'
    },
    {
      id: '26',
      name: 'Viennoiseries Assorties',
      price: 2500,
      category: 'Boulangerie',
      seller: 'Pâtisserie Française',
      location: 'Dakar',
      rating: 4.8,
      image: 'https://images.unsplash.com/photo-1555507036-ab794f4afe5a?w=400',
      inStock: true,
      description: 'Assortiment de viennoiseries fraîches : croissants, pains au chocolat',
      unit: 'lot de 6',
      badges: ['Artisanal', 'Assortiment', 'Frais'],
      isNew: true,
      distance: 1.5,
      deliveryTime: '10-20 min'
    },
    {
      id: '27',
      name: 'Jus d\'Orange Pressé 1L',
      price: 1800,
      category: 'Boissons',
      seller: 'Jus Naturels Dakar',
      location: 'Dakar',
      rating: 4.6,
      image: 'https://images.unsplash.com/photo-1621506289937-a8e4df240d0b?w=400',
      inStock: true,
      description: 'Jus d\'orange fraîchement pressé, 100% naturel sans conservateurs',
      unit: 'bouteille',
      weight: '1L',
      badges: ['100% Naturel', 'Pressé Frais', 'Sans Conservateurs'],
      isNew: false,
      distance: 2.1,
      deliveryTime: '15-25 min'
    },
    {
      id: '28',
      name: 'Eau Minérale Locale 1.5L',
      price: 600,
      category: 'Boissons',
      seller: 'Source Naturelle',
      location: 'Thiès',
      rating: 4.2,
      image: 'https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=400',
      inStock: true,
      description: 'Eau minérale naturelle de source locale, pure et rafraîchissante',
      unit: 'bouteille',
      weight: '1.5L',
      badges: ['Source Locale', 'Naturel', 'Pure'],
      isNew: false,
      distance: 2.8,
      deliveryTime: '20-30 min'
    },
    {
      id: '29',
      name: 'Confiture de Mangue Artisanale 250g',
      price: 2800,
      category: 'Artisanat',
      seller: 'Confitures de Maman Fatou',
      location: 'Thiès',
      rating: 4.8,
      image: 'https://images.unsplash.com/photo-1484723091739-30a097e8f929?w=400',
      inStock: true,
      description: 'Confiture artisanale de mangue locale, sans conservateurs, recette traditionnelle',
      unit: 'pot',
      weight: '250g',
      badges: ['Sans Conservateurs', 'Recette Traditionnelle', 'Artisanal'],
      isNew: true,
      distance: 2.8,
      deliveryTime: '20-30 min'
    },
    {
      id: '30',
      name: 'Jus de Bissap Artisanal 1L',
      price: 1500,
      category: 'Artisanat',
      seller: 'Boissons Naturelles Dakar',
      location: 'Dakar',
      rating: 4.7,
      image: 'https://images.unsplash.com/photo-1544145945-f90425340c7e?w=400',
      inStock: true,
      description: 'Jus de bissap artisanal, préparé selon la tradition sénégalaise',
      unit: 'bouteille',
      weight: '1L',
      badges: ['Tradition Sénégalaise', '100% Naturel', 'Artisanal'],
      isNew: false,
      distance: 1.5,
      deliveryTime: '15-25 min'
    },
    {
      id: '31',
      name: 'Savon Traditionnel au Karité 100g',
      price: 1200,
      category: 'Artisanat',
      seller: 'Savonnerie Traditionnelle',
      location: 'Kaolack',
      rating: 4.9,
      image: 'https://images.unsplash.com/photo-1559181567-c3190ca9959b?w=400',
      inStock: true,
      description: 'Savon traditionnel au beurre de karité, fabriqué artisanalement',
      unit: 'pièce',
      weight: '100g',
      badges: ['Karité', 'Traditionnel', 'Artisanal'],
      isNew: false,
      distance: 5.1,
      deliveryTime: '40-50 min'
    },
    {
      id: '32',
      name: 'Panier Tressé Traditionnel',
      price: 3500,
      category: 'Artisanat',
      seller: 'Coopérative Artisanale Fatick',
      location: 'Fatick',
      rating: 4.6,
      image: 'https://images.unsplash.com/photo-1586075010923-2dd4570fb338?w=400',
      inStock: true,
      description: 'Panier traditionnel tressé à la main, idéal pour vos courses au marché',
      unit: 'pièce',
      badges: ['Fait Main', 'Traditionnel', 'Écologique'],
      isNew: false,
      distance: 7.1,
      deliveryTime: '50-65 min'
    },
    {
      id: '33',
      name: 'Natte Traditionnelle en Paille',
      price: 4200,
      category: 'Artisanat',
      seller: 'Artisans de Saint-Louis',
      location: 'Saint-Louis',
      rating: 4.5,
      image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400',
      inStock: true,
      description: 'Natte traditionnelle tressée en paille locale, parfaite pour la détente',
      unit: 'pièce',
      badges: ['Paille Locale', 'Tressage Traditionnel', 'Artisanal'],
      isNew: true,
      distance: 4.8,
      deliveryTime: '40-50 min'
    },
    {
      id: '34',
      name: 'Confiture de Goyave 250g',
      price: 2500,
      category: 'Artisanat',
      seller: 'Délices de Casamance',
      location: 'Casamance',
      rating: 4.7,
      image: 'https://images.unsplash.com/photo-1484723091739-30a097e8f929?w=400',
      inStock: true,
      description: 'Confiture de goyave artisanale, fruits cueillis à maturité',
      unit: 'pot',
      weight: '250g',
      badges: ['Fruits Mûrs', 'Artisanal', 'Casamance'],
      isNew: false,
      distance: 8.5,
      deliveryTime: '60-75 min'
    },
    // Nouveaux légumes locaux inspirés de TastyDaily
    {
      id: '35',
      name: 'Tomates Cerises Bio 500g',
      price: 1800,
      category: 'Légumes',
      seller: 'Ferme Bio des Niayes',
      location: 'Niayes',
      rating: 4.7,
      image: 'https://images.unsplash.com/photo-1592924357228-91a4daadcfea?w=400',
      inStock: true,
      description: 'Tomates cerises biologiques, sucrées et parfumées, cultivées sans pesticides',
      unit: 'barquette',
      weight: '500g',
      badges: ['Bio', 'Sucré', 'Sans Pesticides', 'Local'],
      isNew: true,
      isBio: true,
      distance: 2.1,
      deliveryTime: '15-25 min'
    },
    {
      id: '36',
      name: 'Aubergines Violettes 1kg',
      price: 1500,
      category: 'Légumes',
      seller: 'Maraîchers de Thiès',
      location: 'Thiès',
      rating: 4.4,
      image: 'https://images.unsplash.com/photo-1659261200833-ec8761558af7?w=400',
      inStock: true,
      description: 'Aubergines violettes fraîches, parfaites pour vos plats traditionnels',
      unit: 'kg',
      badges: ['Frais', 'Traditionnel', 'Local'],
      isNew: false,
      distance: 2.8,
      deliveryTime: '20-30 min'
    },
    {
      id: '37',
      name: 'Courgettes Vertes 1kg',
      price: 1300,
      category: 'Légumes',
      seller: 'Jardin de Khadija',
      location: 'Rufisque',
      rating: 4.5,
      image: 'https://images.unsplash.com/photo-1566385101042-1a0aa0c1268c?w=400',
      inStock: true,
      description: 'Courgettes vertes tendres, idéales pour gratins et ratatouilles',
      unit: 'kg',
      badges: ['Tendre', 'Polyvalent', 'Local'],
      isNew: false,
      distance: 1.8,
      deliveryTime: '15-25 min'
    },
    {
      id: '38',
      name: 'Poivrons Multicolores 500g',
      price: 2000,
      category: 'Légumes',
      seller: 'Serre Moderne Dakar',
      location: 'Dakar',
      rating: 4.6,
      image: 'https://images.unsplash.com/photo-1563565375-f3fdfdbefa83?w=400',
      inStock: true,
      description: 'Mélange de poivrons rouges, jaunes et verts, cultivés en serre',
      unit: 'barquette',
      weight: '500g',
      badges: ['Multicolore', 'Serre', 'Premium'],
      isNew: true,
      distance: 1.5,
      deliveryTime: '10-20 min'
    },
    {
      id: '39',
      name: 'Épinards Frais 300g',
      price: 1000,
      category: 'Légumes',
      seller: 'Maraîchers Bio Thiès',
      location: 'Thiès',
      rating: 4.3,
      image: 'https://images.unsplash.com/photo-1576045057995-568f588f82fb?w=400',
      inStock: true,
      description: 'Épinards frais et tendres, riches en fer et vitamines',
      unit: 'botte',
      weight: '300g',
      badges: ['Bio', 'Riche en Fer', 'Vitamines'],
      isNew: false,
      isBio: true,
      distance: 2.8,
      deliveryTime: '20-30 min'
    },
    {
      id: '40',
      name: 'Concombres Bio 1kg',
      price: 1100,
      category: 'Légumes',
      seller: 'Ferme Écologique',
      location: 'Niayes',
      rating: 4.4,
      image: 'https://images.unsplash.com/photo-1449300079323-02e209d9d3a6?w=400',
      inStock: true,
      description: 'Concombres biologiques croquants et rafraîchissants',
      unit: 'kg',
      badges: ['Bio', 'Croquant', 'Rafraîchissant'],
      isNew: false,
      isBio: true,
      distance: 2.1,
      deliveryTime: '15-25 min'
    },
    {
      id: '41',
      name: 'Brocolis Frais 500g',
      price: 1800,
      category: 'Légumes',
      seller: 'Serre Moderne Dakar',
      location: 'Dakar',
      rating: 4.7,
      image: 'https://images.unsplash.com/photo-1459411621453-7b03977f4bfc?w=400',
      inStock: true,
      description: 'Brocolis frais et verts, riches en vitamines et antioxydants',
      unit: 'tête',
      weight: '500g',
      badges: ['Frais', 'Vitamines', 'Antioxydants'],
      isNew: true,
      distance: 1.5,
      deliveryTime: '10-20 min'
    },
    {
      id: '42',
      name: 'Choux Verts 1 pièce',
      price: 800,
      category: 'Légumes',
      seller: 'Maraîchers de Niayes',
      location: 'Niayes',
      rating: 4.2,
      image: 'https://images.unsplash.com/photo-1594282486552-05b4d80fbb9f?w=400',
      inStock: true,
      description: 'Chou vert frais et croquant, parfait pour les soupes et salades',
      unit: 'pièce',
      badges: ['Frais', 'Croquant', 'Traditionnel'],
      isNew: false,
      distance: 2.1,
      deliveryTime: '15-25 min'
    },
    {
      id: '43',
      name: 'Piments Forts 100g',
      price: 500,
      category: 'Légumes',
      seller: 'Épices du Sénégal',
      location: 'Kaolack',
      rating: 4.8,
      image: 'https://images.unsplash.com/photo-1583454110551-21f2fa2afe61?w=400',
      inStock: true,
      description: 'Piments forts locaux, parfaits pour relever vos plats traditionnels',
      unit: 'sachet',
      weight: '100g',
      badges: ['Fort', 'Épicé', 'Traditionnel', 'Local'],
      isNew: false,
      distance: 5.1,
      deliveryTime: '40-50 min'
    },
    {
      id: '44',
      name: 'Laitue Iceberg 1 pièce',
      price: 600,
      category: 'Légumes',
      seller: 'Jardin de Fatou',
      location: 'Rufisque',
      rating: 4.1,
      image: 'https://images.unsplash.com/photo-1622206151226-18ca2c9ab4a1?w=400',
      inStock: true,
      description: 'Laitue iceberg fraîche et croquante, idéale pour les salades',
      unit: 'pièce',
      badges: ['Frais', 'Croquant', 'Salade'],
      isNew: false,
      distance: 1.8,
      deliveryTime: '15-25 min'
    },
    {
      id: '45',
      name: 'Betteraves Rouges 1kg',
      price: 1400,
      category: 'Légumes',
      seller: 'Maraîchers Bio Thiès',
      location: 'Thiès',
      rating: 4.5,
      image: 'https://images.unsplash.com/photo-1570197788417-0e82375c9371?w=400',
      inStock: true,
      description: 'Betteraves rouges biologiques, riches en antioxydants et minéraux',
      unit: 'kg',
      badges: ['Bio', 'Antioxydants', 'Minéraux'],
      isNew: false,
      isBio: true,
      distance: 2.8,
      deliveryTime: '20-30 min'
    },
    {
      id: '46',
      name: 'Haricots Verts 500g',
      price: 1600,
      category: 'Légumes',
      seller: 'Ferme Bio des Niayes',
      location: 'Niayes',
      rating: 4.6,
      image: 'https://images.unsplash.com/photo-1506976785307-8732e854ad03?w=400',
      inStock: true,
      description: 'Haricots verts fins et tendres, cultivés biologiquement',
      unit: 'barquette',
      weight: '500g',
      badges: ['Bio', 'Fins', 'Tendres'],
      isNew: true,
      isBio: true,
      distance: 2.1,
      deliveryTime: '15-25 min'
    }
  ];



  // Fonctions utilitaires
  const toggleFavorite = (productId: string) => {
    setFavorites(prev => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(productId)) {
        newFavorites.delete(productId);
      } else {
        newFavorites.add(productId);
      }
      return newFavorites;
    });
  };

  // Gestion des notifications toast
  const addNotification = (message: string, type: 'success' | 'error' = 'success') => {
    const id = Date.now().toString();
    setNotifications(prev => [...prev, { id, message, type }]);

    // Auto-suppression après 3 secondes
    setTimeout(() => {
      setNotifications(prev => prev.filter(notif => notif.id !== id));
    }, 3000);
  };

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(notif => notif.id !== id));
  };

  // Animation de produit qui glisse vers le panier
  const animateProductToCart = (event: React.MouseEvent) => {
    const button = event.currentTarget as HTMLElement;
    const cartButton = document.querySelector('[title="Voir le panier"]') as HTMLElement;

    if (button && cartButton) {
      // Créer un élément temporaire pour l'animation
      const flyingProduct = button.cloneNode(true) as HTMLElement;
      flyingProduct.style.position = 'fixed';
      flyingProduct.style.zIndex = '9999';
      flyingProduct.style.pointerEvents = 'none';
      flyingProduct.style.transition = 'all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)';

      const buttonRect = button.getBoundingClientRect();
      const cartRect = cartButton.getBoundingClientRect();

      flyingProduct.style.left = `${buttonRect.left}px`;
      flyingProduct.style.top = `${buttonRect.top}px`;
      flyingProduct.style.width = `${buttonRect.width}px`;
      flyingProduct.style.height = `${buttonRect.height}px`;

      document.body.appendChild(flyingProduct);

      // Animation vers le panier
      setTimeout(() => {
        flyingProduct.style.left = `${cartRect.left}px`;
        flyingProduct.style.top = `${cartRect.top}px`;
        flyingProduct.style.transform = 'scale(0.1)';
        flyingProduct.style.opacity = '0';
      }, 50);

      // Nettoyer après l'animation
      setTimeout(() => {
        if (flyingProduct.parentNode) {
          flyingProduct.parentNode.removeChild(flyingProduct);
        }
      }, 850);
    }
  };

  const updateCart = (productId: string, quantity: number, event?: React.MouseEvent) => {
    const product = products.find(p => p.id === productId);
    const previousQuantity = cart[productId] || 0;

    setCart(prev => ({
      ...prev,
      [productId]: Math.max(0, quantity)
    }));

    // Animation de produit qui glisse vers le panier
    if (quantity > previousQuantity && event) {
      animateProductToCart(event);
    }

    // Notification avec animation de produit ajouté
    if (quantity > previousQuantity && product) {
      addNotification(`${product.name} ajouté au panier ! 🛒`, 'success');
    } else if (quantity === 0 && previousQuantity > 0 && product) {
      addNotification(`${product.name} retiré du panier`, 'success');
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('fr-SN', {
      style: 'currency',
      currency: 'XOF',
      minimumFractionDigits: 0,
    }).format(price);
  };

  // Gestion des tags
  const toggleTag = (tagName: string) => {
    setSelectedTags(prev =>
      prev.includes(tagName)
        ? prev.filter(tag => tag !== tagName)
        : [...prev, tagName]
    );
  };

  const clearAllFilters = () => {
    setSearchQuery('');
    setSelectedCategory('all');
    setSelectedTags([]);
    setShowOnlyPromo(false);
    setShowOnlyBio(false);
    setMinRating(0);
    setPriceRange([0, 50000]);
  };

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Gestion du scroll pour le bouton "Retour en haut"
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 400);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    const fetchProducts = async () => {
      setIsLoading(true);

      if (!isOnline) {
        // Mode hors ligne : utiliser les données de démonstration
        setProducts(demoProducts);
        setFilteredProducts(demoProducts);
      } else {
        // Mode en ligne : simuler un appel API
        try {
          await new Promise(resolve => setTimeout(resolve, 1000)); // Simulation
          setProducts(demoProducts);
          setFilteredProducts(demoProducts);
        } catch (error) {
          console.error('Erreur lors du chargement des produits:', error);
          setProducts(demoProducts);
          setFilteredProducts(demoProducts);
        }
      }

      setIsLoading(false);
    };

    fetchProducts();
  }, [isOnline]);

  // Filtrage et tri avancés avec useMemo pour les performances - Inspiré de TastyDaily
  const filteredAndSortedProducts = useMemo(() => {
    let filtered = products.filter(product => {
      const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           product.seller.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           product.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           product.description.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;
      const matchesPrice = product.price >= priceRange[0] && product.price <= priceRange[1];
      const matchesRating = product.rating >= minRating;
      const matchesPromo = !showOnlyPromo || product.isPromo;
      const matchesBio = !showOnlyBio || product.isBio;

      // Filtrage par tags sélectionnés
      const matchesTags = selectedTags.length === 0 ||
        selectedTags.every(tag => product.badges?.includes(tag));

      return matchesSearch && matchesCategory && matchesPrice && matchesRating && matchesPromo && matchesBio && matchesTags;
    });

    // Tri
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'price':
          return (a.promoPrice || a.price) - (b.promoPrice || b.price);
        case 'rating':
          return (b.rating || 0) - (a.rating || 0);
        case 'distance':
          return (a.distance || 0) - (b.distance || 0);
        default:
          return a.name.localeCompare(b.name);
      }
    });

    return filtered;
  }, [products, searchQuery, selectedCategory, priceRange, minRating, showOnlyPromo, showOnlyBio, sortBy]);

  // Pagination des produits
  const paginatedProducts = useMemo(() => {
    const startIndex = (currentPage - 1) * productsPerPage;
    const endIndex = startIndex + productsPerPage;
    return filteredAndSortedProducts.slice(startIndex, endIndex);
  }, [filteredAndSortedProducts, currentPage, productsPerPage]);

  const totalPages = Math.ceil(filteredAndSortedProducts.length / productsPerPage);

  useEffect(() => {
    setFilteredProducts(filteredAndSortedProducts);
    setCurrentPage(1); // Reset à la page 1 quand les filtres changent
  }, [filteredAndSortedProducts]);

  const sectionVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 }
    }
  };

  const productVariants = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: { duration: 0.3 }
    }
  };

  // Skeleton Loader Component inspiré de TastyDaily
  const SkeletonLoader = () => (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-900">
      {/* Header Skeleton */}
      <div className="relative h-80 bg-gradient-to-r from-gray-300 to-gray-400 dark:from-gray-700 dark:to-gray-600 animate-pulse">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10 h-full flex items-center justify-center text-center">
          <div className="max-w-4xl mx-auto px-4">
            <div className="h-16 bg-white/20 rounded-2xl mb-4 animate-pulse"></div>
            <div className="h-8 bg-white/20 rounded-xl mb-6 animate-pulse"></div>
            <div className="flex items-center justify-center gap-4">
              <div className="h-10 w-32 bg-white/20 rounded-full animate-pulse"></div>
              <div className="h-10 w-32 bg-white/20 rounded-full animate-pulse"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters Skeleton */}
      <div className="sticky top-0 z-40 bg-white/95 dark:bg-gray-800/95 backdrop-blur-md border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex flex-col lg:flex-row gap-4 mb-4">
            <div className="flex-1 h-14 bg-gray-200 dark:bg-gray-700 rounded-2xl animate-pulse"></div>
            <div className="flex gap-3">
              <div className="h-14 w-32 bg-gray-200 dark:bg-gray-700 rounded-2xl animate-pulse"></div>
              <div className="h-14 w-20 bg-gray-200 dark:bg-gray-700 rounded-2xl animate-pulse"></div>
            </div>
          </div>

          <div className="flex items-center gap-3 overflow-x-auto pb-2">
            <div className="h-6 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            {[...Array(8)].map((_, i) => (
              <div key={i} className="h-10 w-24 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse flex-shrink-0"></div>
            ))}
          </div>
        </div>
      </div>

      {/* Content Skeleton */}
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Sections dynamiques skeleton */}
        {[...Array(3)].map((_, sectionIndex) => (
          <div key={sectionIndex} className="mb-12">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-2xl animate-pulse"></div>
                <div>
                  <div className="h-6 w-40 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2"></div>
                  <div className="h-4 w-60 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                </div>
              </div>
              <div className="h-6 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden">
                  <div className="h-48 bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
                  <div className="p-4">
                    <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2"></div>
                    <div className="flex items-center justify-between">
                      <div className="h-6 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                      <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}

        {/* Main products grid skeleton */}
        <div className="mb-8">
          <div className="h-8 w-48 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {[...Array(12)].map((_, i) => (
              <div key={i} className="bg-white dark:bg-gray-800 rounded-3xl shadow-lg overflow-hidden">
                <div className="h-56 bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
                <div className="p-6">
                  <div className="flex justify-between items-start mb-3">
                    <div className="flex-1">
                      <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-1"></div>
                      <div className="h-4 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                    </div>
                    <div className="h-6 w-12 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>
                  </div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-4"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-4 w-3/4"></div>
                  <div className="flex items-center justify-between">
                    <div className="h-8 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                    <div className="h-10 w-24 bg-gray-200 dark:bg-gray-700 rounded-2xl animate-pulse"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  // Condition de chargement
  if (isLoading) {
    return <SkeletonLoader />;
  }

  // Rendu principal de la page
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-900">
      {/* Header avec image de fond floue - Style TastyDaily */}
      <div className="relative h-80 bg-gradient-to-r from-green-600 to-blue-600 overflow-hidden">
        <div className="absolute inset-0 bg-black/30"></div>
        <div
          className="absolute inset-0 bg-cover bg-center filter blur-sm"
          style={{
            backgroundImage: 'url(https://images.unsplash.com/photo-1542838132-92c53300491e?w=1200)'
          }}
        ></div>
        <div className="relative z-10 h-full flex items-center justify-center text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto px-4"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-4">
              🛒 LocaFresh Market
            </h1>
            <p className="text-xl md:text-2xl opacity-90 mb-6">
              Découvrez les meilleurs produits locaux près de chez vous
            </p>
            <div className="flex items-center justify-center gap-4 text-lg">
              <span className="bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full">
                🌱 {filteredProducts.length} produits frais
              </span>
              <span className="bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full">
                🚚 Livraison rapide
              </span>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Filtres rapides par catégories avec emojis */}
      <motion.div
        className="sticky top-0 z-40 bg-white/95 dark:bg-gray-800/95 backdrop-blur-md border-b border-gray-200 dark:border-gray-700 shadow-lg"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <div className="max-w-7xl mx-auto px-4 py-4">
          {/* Barre de recherche moderne */}
          <div className="flex flex-col lg:flex-row gap-4 mb-4">
            <div className="flex-1 relative">
              <FaSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Rechercher fruits, légumes, viandes, poissons..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-12 pr-4 py-4 bg-white dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-2xl focus:outline-none focus:ring-4 focus:ring-green-100 dark:focus:ring-green-900 focus:border-green-400 transition-all duration-300 text-lg"
              />
            </div>

            {/* Tri et vue */}
            <div className="flex gap-3">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="px-4 py-4 bg-white dark:bg-gray-700 border-2 border-gray-200 dark:border-gray-600 rounded-2xl focus:outline-none focus:ring-2 focus:ring-green-500 text-gray-900 dark:text-white"
                title="Trier par"
              >
                <option value="name">📝 Nom</option>
                <option value="price">💰 Prix</option>
                <option value="rating">⭐ Note</option>
                <option value="distance">📍 Distance</option>
              </select>

              <div className="flex bg-gray-100 dark:bg-gray-700 rounded-2xl p-1">
                <button
                  type="button"
                  onClick={() => setViewMode('grid')}
                  className={`px-4 py-3 rounded-xl transition-all duration-300 ${
                    viewMode === 'grid'
                      ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-md'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                  }`}
                  title="Vue grille"
                >
                  <FaTh />
                </button>
                <button
                  type="button"
                  onClick={() => setViewMode('list')}
                  className={`px-4 py-3 rounded-xl transition-all duration-300 ${
                    viewMode === 'list'
                      ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-md'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                  }`}
                  title="Vue liste"
                >
                  <FaList />
                </button>
              </div>
            </div>
          </div>

          {/* Catégories avec emojis */}
          <div className="flex items-center gap-3 overflow-x-auto pb-2">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300 whitespace-nowrap">Catégories :</span>
            {locaFreshCategories.map((category) => (
              <motion.button
                key={category.id}
                type="button"
                onClick={() => setSelectedCategory(category.id)}
                className={`flex items-center gap-2 px-4 py-2 rounded-full whitespace-nowrap transition-all duration-300 ${
                  selectedCategory === category.id
                    ? `bg-gradient-to-r ${category.color} text-white shadow-lg scale-105`
                    : 'bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                }`}
                whileHover={{ scale: selectedCategory === category.id ? 1.05 : 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <span className="text-lg">{category.emoji}</span>
                <span className="text-sm font-medium">{category.name}</span>
              </motion.button>
            ))}
          </div>

          {/* Tags populaires inspirés de TastyDaily */}
          <div className="flex items-center gap-3 overflow-x-auto pb-2 mt-4">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300 whitespace-nowrap">Tags populaires :</span>
            {popularTags.map((tag) => (
              <motion.button
                key={tag.name}
                type="button"
                onClick={() => toggleTag(tag.name)}
                className={`flex items-center gap-2 px-3 py-1.5 rounded-full whitespace-nowrap text-sm font-medium transition-all duration-300 ${
                  selectedTags.includes(tag.name)
                    ? `${tag.color} shadow-md scale-105 ring-2 ring-offset-2 ring-blue-500 dark:ring-offset-gray-800`
                    : `${tag.color} hover:shadow-md hover:scale-102 opacity-70 hover:opacity-100`
                }`}
                whileHover={{ scale: selectedTags.includes(tag.name) ? 1.05 : 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <span>{tag.emoji}</span>
                <span>{tag.name}</span>
                {selectedTags.includes(tag.name) && (
                  <motion.span
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="text-xs"
                  >
                    ✓
                  </motion.span>
                )}
              </motion.button>
            ))}

            {/* Bouton pour effacer tous les filtres */}
            {(selectedTags.length > 0 || selectedCategory !== 'all' || searchQuery || showOnlyPromo || showOnlyBio || minRating > 0) && (
              <motion.button
                type="button"
                onClick={clearAllFilters}
                className="flex items-center gap-2 px-3 py-1.5 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-all duration-300"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <FaTimes className="w-3 h-3" />
                <span>Effacer tout</span>
              </motion.button>
            )}
          </div>

          {/* Filtres avancés */}
          <div className="flex items-center justify-between mt-4">
            <div className="flex items-center gap-4">
              <button
                type="button"
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-xl transition-all duration-300"
              >
                <FaFilter />
                <span className="hidden sm:inline">Filtres</span>
                {(showOnlyPromo || showOnlyBio || minRating > 0) && (
                  <span className="w-2 h-2 bg-red-500 rounded-full"></span>
                )}
              </button>

              <div className="text-sm text-gray-600 dark:text-gray-400">
                <span className="font-medium">{filteredProducts.length}</span> produit{filteredProducts.length > 1 ? 's' : ''} trouvé{filteredProducts.length > 1 ? 's' : ''}
              </div>
            </div>
          </div>

          {/* Panneau de filtres avancés */}
          <AnimatePresence>
            {showFilters && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
                className="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700"
              >
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Prix maximum: {priceRange[1].toLocaleString()} FCFA
                    </label>
                    <input
                      type="range"
                      min="0"
                      max="50000"
                      value={priceRange[1]}
                      onChange={(e) => setPriceRange([0, Number(e.target.value)])}
                      className="w-full"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Note minimum: {minRating}⭐
                    </label>
                    <input
                      type="range"
                      min="0"
                      max="5"
                      step="0.5"
                      value={minRating}
                      onChange={(e) => setMinRating(Number(e.target.value))}
                      className="w-full"
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={showOnlyPromo}
                        onChange={(e) => setShowOnlyPromo(e.target.checked)}
                        className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                      />
                      <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">🏷️ Promotions uniquement</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={showOnlyBio}
                        onChange={(e) => setShowOnlyBio(e.target.checked)}
                        className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                      />
                      <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">🌱 Bio uniquement</span>
                    </label>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </motion.div>

      {/* Suggestions personnalisées inspirées de TastyDaily */}
      {showSuggestions && (
        <motion.div
          className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 py-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <div className="max-w-7xl mx-auto px-4">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center">
                  <span className="text-white text-xl">🎯</span>
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Recommandé pour vous</h2>
                  <p className="text-gray-600 dark:text-gray-400">Basé sur vos préférences et achats récents</p>
                </div>
              </div>
              <button
                type="button"
                onClick={() => setShowSuggestions(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                title="Masquer les suggestions"
              >
                <FaTimes className="w-5 h-5" />
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Suggestions basées sur les favoris et le panier */}
              {filteredProducts
                .filter(p => p.rating >= 4.5 || p.isPromo || favorites.has(p.id))
                .slice(0, 4)
                .map((product, index) => (
                <motion.div
                  key={`suggestion-${product.id}`}
                  className="group bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 border border-blue-100 dark:border-blue-900"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ y: -4 }}
                >
                  <div className="relative h-48 overflow-hidden">
                    <Image
                      src={product.image}
                      alt={product.name}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute top-3 left-3">
                      <span className="bg-gradient-to-r from-blue-500 to-purple-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
                        🎯 Suggéré
                      </span>
                    </div>
                    <button
                      type="button"
                      onClick={() => toggleFavorite(product.id)}
                      className={`absolute top-3 right-3 w-8 h-8 rounded-full shadow-lg transition-all duration-300 ${
                        favorites.has(product.id)
                          ? 'bg-red-500 text-white'
                          : 'bg-white/90 text-gray-600 hover:bg-red-500 hover:text-white'
                      }`}
                      title={favorites.has(product.id) ? 'Retirer des favoris' : 'Ajouter aux favoris'}
                    >
                      <FaHeart className="w-3 h-3 mx-auto" />
                    </button>
                  </div>

                  <div className="p-4">
                    <h3 className="font-bold text-gray-900 dark:text-white mb-2 line-clamp-1">{product.name}</h3>
                    <p className="text-xs text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">{product.description}</p>
                    <div className="flex items-center justify-between">
                      <div className="flex flex-col">
                        {product.isPromo && product.promoPrice ? (
                          <>
                            <span className="text-lg font-bold text-red-600 dark:text-red-400">
                              {formatPrice(product.promoPrice)}
                            </span>
                            <span className="text-sm text-gray-500 line-through">
                              {formatPrice(product.price)}
                            </span>
                          </>
                        ) : (
                          <span className="text-lg font-bold text-gray-900 dark:text-white">
                            {formatPrice(product.price)}
                          </span>
                        )}
                      </div>
                      <button
                        type="button"
                        onClick={() => updateCart(product.id, (cart[product.id] || 0) + 1)}
                        className="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-xl text-sm font-medium hover:from-blue-600 hover:to-purple-600 transition-all duration-300 shadow-md hover:shadow-lg"
                        title="Ajouter au panier"
                      >
                        Ajouter
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>
      )}

      {/* Sections dynamiques inspirées de TastyDaily */}
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Offres du jour */}
        <motion.section
          className="mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-orange-500 rounded-2xl flex items-center justify-center">
                <FaFire className="text-white text-xl" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Offres du jour</h2>
                <p className="text-gray-600 dark:text-gray-400">Profitez de nos promotions exceptionnelles</p>
              </div>
            </div>
            <Link href="/fr/products?filter=promo" className="text-blue-600 hover:text-blue-700 font-medium">
              Voir tout →
            </Link>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {filteredProducts.filter(p => p.isPromo).slice(0, 4).map((product, index) => (
              <motion.div
                key={`promo-${product.id}`}
                className="group bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 border border-red-100 dark:border-red-900"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -4 }}
              >
                <div className="relative h-48 overflow-hidden">
                  <Image
                    src={product.image}
                    alt={product.name}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-3 left-3">
                    <span className="bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
                      🏷️ -{Math.round(((product.price - (product.promoPrice || product.price)) / product.price) * 100)}%
                    </span>
                  </div>
                  <button
                    type="button"
                    onClick={() => toggleFavorite(product.id)}
                    className={`absolute top-3 right-3 w-8 h-8 rounded-full shadow-lg transition-all duration-300 ${
                      favorites.has(product.id)
                        ? 'bg-red-500 text-white'
                        : 'bg-white/90 text-gray-600 hover:bg-red-500 hover:text-white'
                    }`}
                    title={favorites.has(product.id) ? 'Retirer des favoris' : 'Ajouter aux favoris'}
                  >
                    <FaHeart className="w-3 h-3 mx-auto" />
                  </button>
                </div>

                <div className="p-4">
                  <h3 className="font-bold text-gray-900 dark:text-white mb-2 line-clamp-1">{product.name}</h3>
                  <div className="flex items-center justify-between">
                    <div className="flex flex-col">
                      <span className="text-lg font-bold text-red-600 dark:text-red-400">
                        {formatPrice(product.promoPrice || product.price)}
                      </span>
                      {product.promoPrice && (
                        <span className="text-sm text-gray-500 line-through">
                          {formatPrice(product.price)}
                        </span>
                      )}
                    </div>
                    <button
                      type="button"
                      onClick={() => updateCart(product.id, (cart[product.id] || 0) + 1)}
                      className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-full flex items-center justify-center hover:from-green-600 hover:to-emerald-600 transition-all duration-300"
                    >
                      <FaPlus className="w-3 h-3" />
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* Produits populaires - Amélioré */}
        <motion.section
          className="mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl flex items-center justify-center">
                <span className="text-white text-xl">🔥</span>
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Produits populaires</h2>
                <p className="text-gray-600 dark:text-gray-400">Les plus appréciés par nos clients</p>
              </div>
            </div>
            <Link href="/fr/products?sort=rating" className="text-blue-600 hover:text-blue-700 font-medium">
              Voir tout →
            </Link>
          </div>

          {/* Grille responsive 2-3 colonnes */}
          <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
            {filteredProducts
              .filter(p => p.rating >= 4.5)
              .slice(0, 6)
              .map((product, index) => (
              <motion.div
                key={`popular-${product.id}`}
                className="group bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500 border border-orange-100 dark:border-orange-900"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -8, scale: 1.02 }}
              >
                {/* Image cliquable */}
                <Link href={`/fr/products/${product.id}`} className="block">
                  <div className="relative h-48 md:h-56 overflow-hidden cursor-pointer">
                    <Image
                      src={product.image}
                      alt={product.name}
                      fill
                      className="object-cover group-hover:scale-110 transition-transform duration-500"
                    />

                    {/* Badge Populaire dynamique */}
                    <div className="absolute top-3 left-3">
                      <motion.div
                        className="flex items-center gap-1 bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-lg"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: 0.2 }}
                      >
                        <span>🔥</span>
                        <span>Populaire</span>
                      </motion.div>
                    </div>

                    {/* Nombre de ventes/likes simulé */}
                    <div className="absolute top-3 right-3">
                      <motion.div
                        className="flex items-center gap-1 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm text-gray-800 dark:text-white text-xs font-medium px-2 py-1 rounded-full shadow-md"
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.3 }}
                      >
                        <FaHeart className="w-3 h-3 text-red-500" />
                        <span>{Math.floor(product.rating * 50)}+</span>
                      </motion.div>
                    </div>

                    {/* Note avec étoiles */}
                    <div className="absolute bottom-3 left-3">
                      <div className="flex items-center gap-1 bg-yellow-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg">
                        <FaStar className="w-3 h-3" />
                        <span>{product.rating}</span>
                      </div>
                    </div>

                    {/* Overlay au survol */}
                    <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                      <span className="text-white font-medium text-sm bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full">
                        Voir le produit
                      </span>
                    </div>
                  </div>
                </Link>

                <div className="p-4">
                  <Link href={`/fr/products/${product.id}`}>
                    <h3 className="font-bold text-gray-900 dark:text-white text-lg mb-2 line-clamp-2 hover:text-blue-600 dark:hover:text-blue-400 transition-colors cursor-pointer">
                      {product.name}
                    </h3>
                  </Link>

                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                    {product.description}
                  </p>

                  <div className="flex items-center justify-between">
                    <div className="flex flex-col">
                      {product.isPromo && product.promoPrice ? (
                        <>
                          <span className="text-xl font-bold text-red-600 dark:text-red-400">
                            {formatPrice(product.promoPrice)}
                          </span>
                          <span className="text-sm text-gray-500 line-through">
                            {formatPrice(product.price)}
                          </span>
                        </>
                      ) : (
                        <span className="text-xl font-bold text-gray-900 dark:text-white">
                          {formatPrice(product.price)}
                        </span>
                      )}
                    </div>

                    {/* Bouton Ajouter au panier amélioré */}
                    {cart[product.id] > 0 ? (
                      <div className="flex items-center gap-2 bg-green-50 dark:bg-green-900/20 rounded-xl p-1">
                        <motion.button
                          type="button"
                          onClick={() => updateCart(product.id, cart[product.id] - 1)}
                          className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center hover:bg-green-600 transition-colors"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          title="Diminuer la quantité"
                        >
                          <FaMinus className="w-3 h-3" />
                        </motion.button>
                        <span className="text-green-700 dark:text-green-300 font-bold min-w-[20px] text-center">
                          {cart[product.id]}
                        </span>
                        <motion.button
                          type="button"
                          onClick={() => updateCart(product.id, cart[product.id] + 1)}
                          className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center hover:bg-green-600 transition-colors"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          title="Augmenter la quantité"
                        >
                          <FaPlus className="w-3 h-3" />
                        </motion.button>
                      </div>
                    ) : (
                      <motion.button
                        type="button"
                        onClick={() => updateCart(product.id, 1)}
                        className="flex items-center gap-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white px-4 py-2 rounded-xl font-bold hover:from-green-600 hover:to-emerald-600 transition-all duration-300 shadow-lg hover:shadow-xl"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        title="Ajouter au panier"
                      >
                        <FaShoppingCart className="w-4 h-4" />
                        <span className="hidden sm:inline">Ajouter</span>
                      </motion.button>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* Nouveaux produits - Amélioré */}
        <motion.section
          className="mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-2xl flex items-center justify-center">
                <span className="text-white text-xl">✨</span>
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Nouveaux produits</h2>
                <p className="text-gray-600 dark:text-gray-400">Découvrez nos dernières arrivées fraîches</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              {/* Filtre par catégorie pour nouveaux produits */}
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-2 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500"
                title="Filtrer par catégorie"
              >
                <option value="all">Toutes catégories</option>
                {locaFreshCategories.slice(1).map(category => (
                  <option key={category.id} value={category.id}>
                    {category.emoji} {category.name}
                  </option>
                ))}
              </select>
              <Link href="/fr/products?filter=new" className="text-blue-600 hover:text-blue-700 font-medium">
                Voir tout →
              </Link>
            </div>
          </div>

          {/* Grille responsive pour nouveaux produits */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {filteredProducts
              .filter(p => p.isNew)
              .sort((a, b) => {
                // Simulation du tri par date d'ajout (plus récent en premier)
                // En réalité, vous utiliseriez une vraie date de création
                return parseInt(b.id) - parseInt(a.id);
              })
              .slice(0, 8)
              .map((product, index) => (
              <motion.div
                key={`new-${product.id}`}
                className="group bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500 border border-emerald-100 dark:border-emerald-900"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -8, scale: 1.02 }}
              >
                {/* Image cliquable */}
                <Link href={`/fr/products/${product.id}`} className="block">
                  <div className="relative h-48 overflow-hidden cursor-pointer">
                    <Image
                      src={product.image}
                      alt={product.name}
                      fill
                      className="object-cover group-hover:scale-110 transition-transform duration-500"
                    />

                    {/* Badge Nouveau dynamique */}
                    <div className="absolute top-3 left-3">
                      <motion.div
                        className="flex items-center gap-1 bg-gradient-to-r from-emerald-500 to-teal-500 text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-lg"
                        initial={{ scale: 0, rotate: -10 }}
                        animate={{ scale: 1, rotate: 0 }}
                        transition={{ delay: 0.2, type: "spring" }}
                      >
                        <span>✨</span>
                        <span>Nouveau</span>
                      </motion.div>
                    </div>

                    {/* Indicateur de fraîcheur */}
                    <div className="absolute top-3 right-3">
                      <motion.div
                        className="flex items-center gap-1 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm text-gray-800 dark:text-white text-xs font-medium px-2 py-1 rounded-full shadow-md"
                        initial={{ opacity: 0, scale: 0 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: 0.4 }}
                      >
                        <span className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
                        <span>Frais</span>
                      </motion.div>
                    </div>

                    {/* Catégorie badge */}
                    <div className="absolute bottom-3 left-3">
                      <div className="bg-black/50 backdrop-blur-sm text-white text-xs font-medium px-2 py-1 rounded-full">
                        {locaFreshCategories.find(cat => cat.id === product.category)?.emoji} {product.category}
                      </div>
                    </div>

                    {/* Overlay au survol */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center pb-4">
                      <span className="text-white font-medium text-sm bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full">
                        Découvrir ce nouveau produit
                      </span>
                    </div>
                  </div>
                </Link>

                <div className="p-4">
                  <Link href={`/fr/products/${product.id}`}>
                    <h3 className="font-bold text-gray-900 dark:text-white text-lg mb-2 line-clamp-2 hover:text-emerald-600 dark:hover:text-emerald-400 transition-colors cursor-pointer">
                      {product.name}
                    </h3>
                  </Link>

                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                    {product.description}
                  </p>

                  {/* Informations vendeur */}
                  <div className="flex items-center gap-2 mb-3">
                    <FaMapMarkerAlt className="w-3 h-3 text-emerald-500" />
                    <span className="text-xs text-gray-600 dark:text-gray-400">
                      {product.seller}
                    </span>
                    {product.distance && (
                      <>
                        <span className="text-gray-400">•</span>
                        <span className="text-xs text-emerald-600 dark:text-emerald-400 font-medium">
                          {product.distance}km
                        </span>
                      </>
                    )}
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex flex-col">
                      {product.isPromo && product.promoPrice ? (
                        <>
                          <span className="text-xl font-bold text-red-600 dark:text-red-400">
                            {formatPrice(product.promoPrice)}
                          </span>
                          <span className="text-sm text-gray-500 line-through">
                            {formatPrice(product.price)}
                          </span>
                        </>
                      ) : (
                        <span className="text-xl font-bold text-gray-900 dark:text-white">
                          {formatPrice(product.price)}
                        </span>
                      )}
                    </div>

                    {/* Bouton Ajouter au panier amélioré */}
                    {cart[product.id] > 0 ? (
                      <div className="flex items-center gap-2 bg-emerald-50 dark:bg-emerald-900/20 rounded-xl p-1">
                        <motion.button
                          type="button"
                          onClick={() => updateCart(product.id, cart[product.id] - 1)}
                          className="w-8 h-8 bg-emerald-500 text-white rounded-full flex items-center justify-center hover:bg-emerald-600 transition-colors"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          title="Diminuer la quantité"
                        >
                          <FaMinus className="w-3 h-3" />
                        </motion.button>
                        <span className="text-emerald-700 dark:text-emerald-300 font-bold min-w-[20px] text-center">
                          {cart[product.id]}
                        </span>
                        <motion.button
                          type="button"
                          onClick={() => updateCart(product.id, cart[product.id] + 1)}
                          className="w-8 h-8 bg-emerald-500 text-white rounded-full flex items-center justify-center hover:bg-emerald-600 transition-colors"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          title="Augmenter la quantité"
                        >
                          <FaPlus className="w-3 h-3" />
                        </motion.button>
                      </div>
                    ) : (
                      <motion.button
                        type="button"
                        onClick={() => updateCart(product.id, 1)}
                        className="flex items-center gap-2 bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-4 py-2 rounded-xl font-bold hover:from-emerald-600 hover:to-teal-600 transition-all duration-300 shadow-lg hover:shadow-xl"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        title="Ajouter au panier"
                      >
                        <FaShoppingCart className="w-4 h-4" />
                        <span className="hidden sm:inline">Essayer</span>
                      </motion.button>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* Section Produits à découvrir - Nouvelle section */}
        <motion.section
          className="mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
        >
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center">
                <span className="text-white text-xl">🌟</span>
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Produits à découvrir</h2>
                <p className="text-gray-600 dark:text-gray-400">Explorez notre sélection variée de produits locaux</p>
              </div>
            </div>
            <Link href="/fr/products" className="text-blue-600 hover:text-blue-700 font-medium">
              Voir tout →
            </Link>
          </div>

          {/* Grille responsive 2-4 colonnes */}
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {filteredProducts
              .filter(p => !p.isNew && !p.isPromo) // Produits qui ne sont ni nouveaux ni en promo
              .sort(() => Math.random() - 0.5) // Mélange aléatoire
              .slice(0, 8)
              .map((product, index) => (
              <motion.div
                key={`discover-${product.id}`}
                className="group bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500 border border-purple-100 dark:border-purple-900"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -8, scale: 1.02 }}
              >
                {/* Image cliquable */}
                <Link href={`/fr/products/${product.id}`} className="block">
                  <div className="relative h-48 overflow-hidden cursor-pointer">
                    <Image
                      src={product.image}
                      alt={product.name}
                      fill
                      className="object-cover group-hover:scale-110 transition-transform duration-500"
                    />

                    {/* Badge À découvrir */}
                    <div className="absolute top-3 left-3">
                      <motion.div
                        className="flex items-center gap-1 bg-gradient-to-r from-purple-500 to-pink-500 text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-lg"
                        initial={{ scale: 0, rotate: 5 }}
                        animate={{ scale: 1, rotate: 0 }}
                        transition={{ delay: 0.2, type: "spring" }}
                      >
                        <span>🌟</span>
                        <span>À découvrir</span>
                      </motion.div>
                    </div>

                    {/* Note avec étoiles */}
                    <div className="absolute top-3 right-3">
                      <div className="flex items-center gap-1 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm text-gray-800 dark:text-white text-xs font-medium px-2 py-1 rounded-full shadow-md">
                        <FaStar className="w-3 h-3 text-yellow-500" />
                        <span>{product.rating}</span>
                      </div>
                    </div>

                    {/* Catégorie badge */}
                    <div className="absolute bottom-3 left-3">
                      <div className="bg-black/50 backdrop-blur-sm text-white text-xs font-medium px-2 py-1 rounded-full">
                        {locaFreshCategories.find(cat => cat.id === product.category)?.emoji} {product.category}
                      </div>
                    </div>

                    {/* Overlay au survol avec effet subtil */}
                    <div className="absolute inset-0 bg-gradient-to-t from-purple-900/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center pb-4">
                      <span className="text-white font-medium text-sm bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full">
                        Découvrir ce produit
                      </span>
                    </div>
                  </div>
                </Link>

                <div className="p-4">
                  <Link href={`/fr/products/${product.id}`}>
                    <h3 className="font-bold text-gray-900 dark:text-white text-lg mb-2 line-clamp-2 hover:text-purple-600 dark:hover:text-purple-400 transition-colors cursor-pointer">
                      {product.name}
                    </h3>
                  </Link>

                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                    {product.description}
                  </p>

                  {/* Informations vendeur */}
                  <div className="flex items-center gap-2 mb-3">
                    <FaMapMarkerAlt className="w-3 h-3 text-purple-500" />
                    <span className="text-xs text-gray-600 dark:text-gray-400">
                      {product.seller}
                    </span>
                    {product.distance && (
                      <>
                        <span className="text-gray-400">•</span>
                        <span className="text-xs text-purple-600 dark:text-purple-400 font-medium">
                          {product.distance}km
                        </span>
                      </>
                    )}
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex flex-col">
                      <span className="text-xl font-bold text-gray-900 dark:text-white">
                        {formatPrice(product.price)}
                      </span>
                    </div>

                    {/* Bouton Ajouter au panier amélioré avec animation */}
                    {cart[product.id] > 0 ? (
                      <div className="flex items-center gap-2 bg-purple-50 dark:bg-purple-900/20 rounded-xl p-1">
                        <motion.button
                          type="button"
                          onClick={() => updateCart(product.id, cart[product.id] - 1)}
                          className="w-8 h-8 bg-purple-500 text-white rounded-full flex items-center justify-center hover:bg-purple-600 transition-colors"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          title="Diminuer la quantité"
                        >
                          <FaMinus className="w-3 h-3" />
                        </motion.button>
                        <span className="text-purple-700 dark:text-purple-300 font-bold min-w-[20px] text-center">
                          {cart[product.id]}
                        </span>
                        <motion.button
                          type="button"
                          onClick={() => updateCart(product.id, cart[product.id] + 1)}
                          className="w-8 h-8 bg-purple-500 text-white rounded-full flex items-center justify-center hover:bg-purple-600 transition-colors"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          title="Augmenter la quantité"
                        >
                          <FaPlus className="w-3 h-3" />
                        </motion.button>
                      </div>
                    ) : (
                      <motion.button
                        type="button"
                        onClick={(e) => updateCart(product.id, 1, e)}
                        className="flex items-center gap-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white px-4 py-2 rounded-xl font-bold hover:from-green-600 hover:to-emerald-600 transition-all duration-300 shadow-lg hover:shadow-xl"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        title="Ajouter au panier"
                      >
                        <FaShoppingCart className="w-4 h-4" />
                        <span className="hidden sm:inline">Découvrir</span>
                      </motion.button>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* Section Légumes Premium - Inspirée de TastyDaily */}
        <motion.section
          className="mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
        >
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center">
                <span className="text-white text-xl">🥬</span>
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Légumes Frais du Jour</h2>
                <p className="text-gray-600 dark:text-gray-400">Directement de nos maraîchers locaux</p>
              </div>
            </div>
            <Link href="/fr/products?category=Légumes" className="text-blue-600 hover:text-blue-700 font-medium">
              Voir tous les légumes →
            </Link>
          </div>

          {/* Grille spéciale légumes - Style TastyDaily */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {filteredProducts
              .filter(p => p.category === 'Légumes')
              .slice(0, 8)
              .map((product, index) => (
              <motion.div
                key={`vegetables-${product.id}`}
                className="group bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500 border border-green-100 dark:border-green-900"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -8, scale: 1.02 }}
              >
                {/* Image avec overlay végétal */}
                <Link href={`/fr/products/${product.id}`} className="block">
                  <div className="relative h-48 overflow-hidden cursor-pointer">
                    <Image
                      src={product.image}
                      alt={product.name}
                      fill
                      className="object-cover group-hover:scale-110 transition-transform duration-500"
                    />

                    {/* Badge Légume Frais */}
                    <div className="absolute top-3 left-3">
                      <motion.div
                        className="flex items-center gap-1 bg-gradient-to-r from-green-500 to-emerald-500 text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-lg"
                        initial={{ scale: 0, rotate: -5 }}
                        animate={{ scale: 1, rotate: 0 }}
                        transition={{ delay: 0.2, type: "spring" }}
                      >
                        <span>🌱</span>
                        <span>Frais</span>
                      </motion.div>
                    </div>

                    {/* Badge Bio si applicable */}
                    {product.isBio && (
                      <div className="absolute top-3 right-3">
                        <motion.div
                          className="flex items-center gap-1 bg-green-600 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg"
                          initial={{ opacity: 0, scale: 0 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: 0.3 }}
                        >
                          <span>🌿</span>
                          <span>Bio</span>
                        </motion.div>
                      )}
                    </div>

                    {/* Note avec étoiles */}
                    <div className="absolute bottom-3 left-3">
                      <div className="flex items-center gap-1 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm text-gray-800 dark:text-white text-xs font-medium px-2 py-1 rounded-full shadow-md">
                        <FaStar className="w-3 h-3 text-yellow-500" />
                        <span>{product.rating}</span>
                      </div>
                    </div>

                    {/* Overlay au survol avec effet végétal */}
                    <div className="absolute inset-0 bg-gradient-to-t from-green-900/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center pb-4">
                      <span className="text-white font-medium text-sm bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full">
                        Légume frais du jour
                      </span>
                    </div>
                  </div>
                </Link>

                <div className="p-4">
                  <Link href={`/fr/products/${product.id}`}>
                    <h3 className="font-bold text-gray-900 dark:text-white text-lg mb-2 line-clamp-2 hover:text-green-600 dark:hover:text-green-400 transition-colors cursor-pointer">
                      {product.name}
                    </h3>
                  </Link>

                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                    {product.description}
                  </p>

                  {/* Badges spéciaux légumes */}
                  <div className="flex flex-wrap gap-1 mb-3">
                    {product.badges?.slice(0, 2).map((badge, badgeIndex) => (
                      <span
                        key={badgeIndex}
                        className="text-xs px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 rounded-full font-medium"
                      >
                        {badge}
                      </span>
                    ))}
                  </div>

                  {/* Informations vendeur */}
                  <div className="flex items-center gap-2 mb-3">
                    <FaMapMarkerAlt className="w-3 h-3 text-green-500" />
                    <span className="text-xs text-gray-600 dark:text-gray-400">
                      {product.seller}
                    </span>
                    {product.distance && (
                      <>
                        <span className="text-gray-400">•</span>
                        <span className="text-xs text-green-600 dark:text-green-400 font-medium">
                          {product.distance}km
                        </span>
                      </>
                    )}
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex flex-col">
                      {product.isPromo && product.promoPrice ? (
                        <>
                          <span className="text-xl font-bold text-red-600 dark:text-red-400">
                            {formatPrice(product.promoPrice)}
                          </span>
                          <span className="text-sm text-gray-500 line-through">
                            {formatPrice(product.price)}
                          </span>
                        </>
                      ) : (
                        <span className="text-xl font-bold text-gray-900 dark:text-white">
                          {formatPrice(product.price)}
                        </span>
                      )}
                      <span className="text-xs text-gray-500">
                        par {product.unit}
                      </span>
                    </div>

                    {/* Bouton Ajouter au panier spécial légumes */}
                    {cart[product.id] > 0 ? (
                      <div className="flex items-center gap-2 bg-green-50 dark:bg-green-900/20 rounded-xl p-1">
                        <motion.button
                          type="button"
                          onClick={() => updateCart(product.id, cart[product.id] - 1)}
                          className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center hover:bg-green-600 transition-colors"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          title="Diminuer la quantité"
                        >
                          <FaMinus className="w-3 h-3" />
                        </motion.button>
                        <span className="text-green-700 dark:text-green-300 font-bold min-w-[20px] text-center">
                          {cart[product.id]}
                        </span>
                        <motion.button
                          type="button"
                          onClick={() => updateCart(product.id, cart[product.id] + 1)}
                          className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center hover:bg-green-600 transition-colors"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          title="Augmenter la quantité"
                        >
                          <FaPlus className="w-3 h-3" />
                        </motion.button>
                      </div>
                    ) : (
                      <motion.button
                        type="button"
                        onClick={(e) => updateCart(product.id, 1, e)}
                        className="flex items-center gap-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white px-4 py-2 rounded-xl font-bold hover:from-green-600 hover:to-emerald-600 transition-all duration-300 shadow-lg hover:shadow-xl"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        title="Ajouter au panier"
                      >
                        <FaShoppingCart className="w-4 h-4" />
                        <span className="hidden sm:inline">Ajouter</span>
                      </motion.button>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Call-to-action pour voir plus de légumes */}
          <motion.div
            className="mt-8 text-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1 }}
          >
            <Link
              href="/fr/products?category=Légumes"
              className="inline-flex items-center gap-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white px-6 py-3 rounded-xl font-bold hover:from-green-600 hover:to-emerald-600 transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              <span>🥬</span>
              <span>Découvrir tous nos légumes frais</span>
              <FaArrowRight className="w-4 h-4" />
            </Link>
          </motion.div>
        </motion.section>
      </div>

      {/* Products Section */}
      <motion.section
        className="py-8"
        initial="hidden"
        animate="visible"
        variants={sectionVariants}
      >
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            {filteredProducts.length === 0 ? (
              <div className="text-center py-12">
                <FaSearch className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                  Aucun produit trouvé
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-6">
                  Essayez de modifier vos critères de recherche ou explorez d'autres catégories.
                </p>
                <button
                  onClick={() => {
                    setSearchQuery('');
                    setSelectedCategory('all');
                  }}
                  className="bg-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-primary-600 transition-colors duration-200"
                >
                  Réinitialiser les filtres
                </button>
              </div>
            ) : (
              <div className={`grid gap-6 ${
                viewMode === 'grid'
                  ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
                  : 'grid-cols-1'
              }`}>
                {paginatedProducts.map((product, index) => (
                  <motion.div
                    key={product.id}
                    className={`group bg-white dark:bg-gray-800 rounded-3xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500 border border-gray-100 dark:border-gray-700 ${
                      viewMode === 'list' ? 'flex' : ''
                    }`}
                    variants={productVariants}
                    initial="hidden"
                    animate="visible"
                    transition={{ delay: index * 0.1 }}
                    whileHover={{ y: -8, scale: 1.02 }}
                  >
                    {/* Product Image avec vraies images */}
                    <div className={`relative ${viewMode === 'list' ? 'w-48 h-32' : 'h-56'} overflow-hidden`}>
                      <Image
                        src={product.image}
                        alt={product.name}
                        fill
                        className="object-cover group-hover:scale-110 transition-transform duration-500"
                      />

                      {/* Badges en overlay */}
                      <div className="absolute top-3 left-3 flex flex-col gap-2">
                        {product.isNew && (
                          <motion.span
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            className="bg-gradient-to-r from-green-500 to-emerald-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg"
                          >
                            ✨ Nouveau
                          </motion.span>
                        )}
                        {product.isPromo && (
                          <motion.span
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{ delay: 0.1 }}
                            className="bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg"
                          >
                            🏷️ Promo
                          </motion.span>
                        )}
                        {product.isBio && (
                          <motion.span
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{ delay: 0.2 }}
                            className="bg-gradient-to-r from-green-600 to-lime-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg"
                          >
                            🌱 Bio
                          </motion.span>
                        )}
                      </div>

                      {/* Badges additionnels */}
                      {product.badges && product.badges.length > 0 && (
                        <div className="absolute top-3 right-3 flex flex-col gap-1">
                          {product.badges.slice(0, 2).map((badge, badgeIndex) => (
                            <motion.span
                              key={badge}
                              initial={{ opacity: 0, x: 20 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ delay: badgeIndex * 0.1 }}
                              className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm text-gray-800 dark:text-white text-xs font-medium px-2 py-1 rounded-full shadow-md"
                            >
                              {badge}
                            </motion.span>
                          ))}
                        </div>
                      )}

                      {/* Bouton favoris */}
                      <motion.button
                        type="button"
                        onClick={() => toggleFavorite(product.id)}
                        className={`absolute bottom-3 right-3 w-10 h-10 rounded-full shadow-lg transition-all duration-300 ${
                          favorites.has(product.id)
                            ? 'bg-red-500 text-white'
                            : 'bg-white/90 dark:bg-gray-800/90 text-gray-600 dark:text-gray-300 hover:bg-red-500 hover:text-white'
                        }`}
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        title={favorites.has(product.id) ? 'Retirer des favoris' : 'Ajouter aux favoris'}
                      >
                        <FaHeart className="w-4 h-4 mx-auto" />
                      </motion.button>

                      {/* Overlay stock */}
                      {!product.inStock && (
                        <div className="absolute inset-0 bg-black/60 flex items-center justify-center">
                          <span className="text-white font-bold text-lg">Rupture de stock</span>
                        </div>
                      )}
                    </div>

                    {/* Product Info */}
                    <div className={`p-6 ${viewMode === 'list' ? 'flex-1' : ''}`}>
                      {/* Header avec nom et note */}
                      <div className="flex justify-between items-start mb-3">
                        <div className="flex-1">
                          <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-1 line-clamp-2">
                            {product.name}
                          </h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {product.unit && `${product.weight || '1'} ${product.unit}`}
                          </p>
                        </div>
                        <div className="flex items-center gap-1 bg-yellow-50 dark:bg-yellow-900/20 px-2 py-1 rounded-full">
                          <FaStar className="w-3 h-3 text-yellow-500" />
                          <span className="text-sm font-medium text-yellow-700 dark:text-yellow-400">
                            {product.rating}
                          </span>
                        </div>
                      </div>

                      {/* Description */}
                      <p className="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-2">
                        {product.description}
                      </p>

                      {/* Vendeur et localisation */}
                      <div className="flex items-center gap-2 mb-4">
                        <FaMapMarkerAlt className="w-4 h-4 text-blue-500" />
                        <span className="text-sm text-gray-600 dark:text-gray-300">
                          {product.seller}
                        </span>
                        {product.distance && (
                          <>
                            <span className="text-gray-400">•</span>
                            <span className="text-sm text-green-600 dark:text-green-400 font-medium">
                              {product.distance}km
                            </span>
                          </>
                        )}
                        {product.deliveryTime && (
                          <>
                            <span className="text-gray-400">•</span>
                            <span className="text-sm text-blue-600 dark:text-blue-400">
                              {product.deliveryTime}
                            </span>
                          </>
                        )}
                      </div>

                      {/* Prix et actions */}
                      <div className="flex items-center justify-between">
                        <div className="flex flex-col">
                          {product.isPromo && product.promoPrice ? (
                            <>
                              <div className="flex items-center gap-2">
                                <span className="text-2xl font-bold text-red-600 dark:text-red-400">
                                  {formatPrice(product.promoPrice)}
                                </span>
                                <span className="text-sm text-gray-500 line-through">
                                  {formatPrice(product.price)}
                                </span>
                              </div>
                              <span className="text-xs text-green-600 dark:text-green-400 font-medium">
                                Économisez {formatPrice(product.price - product.promoPrice)}
                              </span>
                            </>
                          ) : (
                            <span className="text-2xl font-bold text-gray-900 dark:text-white">
                              {formatPrice(product.price)}
                            </span>
                          )}
                        </div>

                        {/* Boutons d'action */}
                        <div className="flex items-center gap-2">
                          {cart[product.id] > 0 ? (
                            <div className="flex items-center gap-2 bg-green-50 dark:bg-green-900/20 rounded-2xl p-1">
                              <motion.button
                                type="button"
                                onClick={() => updateCart(product.id, cart[product.id] - 1)}
                                className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center hover:bg-green-600 transition-colors"
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                              >
                                <FaMinus className="w-3 h-3" />
                              </motion.button>
                              <span className="text-green-700 dark:text-green-300 font-bold min-w-[20px] text-center">
                                {cart[product.id]}
                              </span>
                              <motion.button
                                type="button"
                                onClick={() => updateCart(product.id, cart[product.id] + 1)}
                                className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center hover:bg-green-600 transition-colors"
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                              >
                                <FaPlus className="w-3 h-3" />
                              </motion.button>
                            </div>
                          ) : (
                            <motion.button
                              type="button"
                              onClick={() => updateCart(product.id, 1)}
                              disabled={!product.inStock}
                              className={`px-6 py-3 rounded-2xl font-bold transition-all duration-300 flex items-center gap-2 ${
                                product.inStock
                                  ? 'bg-gradient-to-r from-green-500 to-emerald-500 text-white hover:from-green-600 hover:to-emerald-600 shadow-lg hover:shadow-xl'
                                  : 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                              }`}
                              whileHover={product.inStock ? { scale: 1.05 } : {}}
                              whileTap={product.inStock ? { scale: 0.95 } : {}}
                            >
                              <FaShoppingCart className="w-4 h-4" />
                              <span className="hidden sm:inline">
                                {product.inStock ? 'Ajouter' : 'Indisponible'}
                              </span>
                            </motion.button>
                          )}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}

            {/* Pagination moderne inspirée de TastyDaily */}
            {totalPages > 1 && (
              <motion.div
                className="flex items-center justify-center mt-12 gap-2"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                {/* Bouton Précédent */}
                <button
                  type="button"
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className={`px-4 py-2 rounded-xl font-medium transition-all duration-300 ${
                    currentPage === 1
                      ? 'bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                      : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900 hover:text-blue-600 dark:hover:text-blue-400 shadow-md hover:shadow-lg'
                  }`}
                >
                  ← Précédent
                </button>

                {/* Numéros de pages */}
                <div className="flex items-center gap-1">
                  {[...Array(totalPages)].map((_, index) => {
                    const pageNumber = index + 1;
                    const isCurrentPage = pageNumber === currentPage;
                    const isNearCurrentPage = Math.abs(pageNumber - currentPage) <= 2;
                    const isFirstOrLast = pageNumber === 1 || pageNumber === totalPages;

                    if (!isNearCurrentPage && !isFirstOrLast) {
                      if (pageNumber === currentPage - 3 || pageNumber === currentPage + 3) {
                        return (
                          <span key={pageNumber} className="px-2 text-gray-400 dark:text-gray-500">
                            ...
                          </span>
                        );
                      }
                      return null;
                    }

                    return (
                      <motion.button
                        key={pageNumber}
                        type="button"
                        onClick={() => setCurrentPage(pageNumber)}
                        className={`w-10 h-10 rounded-xl font-medium transition-all duration-300 ${
                          isCurrentPage
                            ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg scale-110'
                            : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900 hover:text-blue-600 dark:hover:text-blue-400 shadow-md hover:shadow-lg hover:scale-105'
                        }`}
                        whileHover={{ scale: isCurrentPage ? 1.1 : 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        {pageNumber}
                      </motion.button>
                    );
                  })}
                </div>

                {/* Bouton Suivant */}
                <button
                  type="button"
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className={`px-4 py-2 rounded-xl font-medium transition-all duration-300 ${
                    currentPage === totalPages
                      ? 'bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed'
                      : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900 hover:text-blue-600 dark:hover:text-blue-400 shadow-md hover:shadow-lg'
                  }`}
                >
                  Suivant →
                </button>
              </motion.div>
            )}

            {/* Informations de pagination */}
            <div className="text-center mt-6 text-sm text-gray-600 dark:text-gray-400">
              Affichage de {((currentPage - 1) * productsPerPage) + 1} à {Math.min(currentPage * productsPerPage, filteredProducts.length)} sur {filteredProducts.length} produits
            </div>
          </div>
        </div>
      </motion.section>

      {/* Bouton flottant "Retour en haut" */}
      <AnimatePresence>
        {showScrollTop && (
          <motion.button
            type="button"
            onClick={scrollToTop}
            className="fixed bottom-6 right-6 z-50 w-14 h-14 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300 flex items-center justify-center"
            initial={{ opacity: 0, scale: 0, y: 100 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0, y: 100 }}
            whileHover={{ scale: 1.1, y: -2 }}
            whileTap={{ scale: 0.9 }}
            title="Retour en haut"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 10l7-7m0 0l7 7m-7-7v18"
              />
            </svg>
          </motion.button>
        )}
      </AnimatePresence>

      {/* Bouton panier flottant (si des articles sont ajoutés) */}
      <AnimatePresence>
        {Object.values(cart).reduce((sum, qty) => sum + qty, 0) > 0 && (
          <motion.div
            className="fixed bottom-6 left-6 z-50"
            initial={{ opacity: 0, scale: 0, x: -100 }}
            animate={{ opacity: 1, scale: 1, x: 0 }}
            exit={{ opacity: 0, scale: 0, x: -100 }}
          >
            <Link href="/fr/cart">
              <motion.button
                type="button"
                className="relative w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300 flex items-center justify-center"
                whileHover={{ scale: 1.1, y: -2 }}
                whileTap={{ scale: 0.9 }}
                title="Voir le panier"
              >
                <FaShoppingCart className="w-6 h-6" />
                <motion.span
                  className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white text-xs font-bold rounded-full flex items-center justify-center"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  key={Object.values(cart).reduce((sum, qty) => sum + qty, 0)}
                >
                  {Object.values(cart).reduce((sum, qty) => sum + qty, 0)}
                </motion.span>
              </motion.button>
            </Link>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Système de notifications toast */}
      <div className="fixed top-4 right-4 z-50 space-y-2">
        <AnimatePresence>
          {notifications.map((notification) => (
            <motion.div
              key={notification.id}
              initial={{ opacity: 0, x: 300, scale: 0.8 }}
              animate={{ opacity: 1, x: 0, scale: 1 }}
              exit={{ opacity: 0, x: 300, scale: 0.8 }}
              transition={{ type: "spring", stiffness: 300, damping: 30 }}
              className={`flex items-center gap-3 px-4 py-3 rounded-xl shadow-2xl backdrop-blur-md border max-w-sm ${
                notification.type === 'success'
                  ? 'bg-green-50/90 dark:bg-green-900/90 border-green-200 dark:border-green-700 text-green-800 dark:text-green-200'
                  : 'bg-red-50/90 dark:bg-red-900/90 border-red-200 dark:border-red-700 text-red-800 dark:text-red-200'
              }`}
            >
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                notification.type === 'success'
                  ? 'bg-green-500 text-white'
                  : 'bg-red-500 text-white'
              }`}>
                {notification.type === 'success' ? (
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                ) : (
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                )}
              </div>

              <span className="flex-1 text-sm font-medium">
                {notification.message}
              </span>

              <button
                type="button"
                onClick={() => removeNotification(notification.id)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                title="Fermer"
              >
                <FaTimes className="w-3 h-3" />
              </button>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>
    </div>
  );
}
