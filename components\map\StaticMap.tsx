'use client';

import React, { useState } from 'react';
import { GoogleMap, Marker } from '@react-google-maps/api';
import { Coordinates } from '@/hooks/useGeolocation';
import { FaMapMarkerAlt } from 'react-icons/fa';
import { useGoogleMaps } from '@/contexts/GoogleMapsContext';

interface StaticMapProps {
  location: Coordinates;
  height?: string;
  width?: string;
  zoom?: number;
  markerTitle?: string;
  className?: string;
  interactive?: boolean;
}

const StaticMap: React.FC<StaticMapProps> = ({
  location,
  height = '300px',
  width = '100%',
  zoom = 15,
  markerTitle = 'Emplacement',
  className = '',
  interactive = false,
}) => {
  const [mapRef, setMapRef] = useState<google.maps.Map | null>(null);

  const { isLoaded, loadError } = useGoogleMaps();

  const mapContainerStyle = {
    width,
    height,
  };

  const options = {
    disableDefaultUI: !interactive,
    zoomControl: interactive,
    scrollwheel: interactive,
    draggable: interactive,
  };

  const onMapLoad = (map: google.maps.Map) => {
    setMapRef(map);
  };

  if (loadError) {
    return (
      <div
        className={`flex items-center justify-center bg-gray-200 dark:bg-gray-700 rounded-lg ${className}`}
        style={{ width, height }}
      >
        <div className="text-center p-4">
          <FaMapMarkerAlt className="mx-auto h-8 w-8 text-gray-400 dark:text-gray-500 mb-2" />
          <p className="text-gray-600 dark:text-gray-400">Impossible de charger la carte</p>
        </div>
      </div>
    );
  }

  if (!isLoaded) {
    return (
      <div
        className={`flex items-center justify-center bg-gray-200 dark:bg-gray-700 rounded-lg ${className}`}
        style={{ width, height }}
      >
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className={`relative rounded-lg overflow-hidden ${className}`} style={{ width, height }}>
      <GoogleMap
        mapContainerStyle={mapContainerStyle}
        center={location}
        zoom={zoom}
        options={options}
        onLoad={onMapLoad}
      >
        <Marker
          position={location}
          title={markerTitle}
          icon={{
            url: '/images/marker-store.svg',
            scaledSize: new google.maps.Size(32, 32),
            origin: new google.maps.Point(0, 0),
            anchor: new google.maps.Point(16, 32),
          }}
        />
      </GoogleMap>
    </div>
  );
};

export default StaticMap;
