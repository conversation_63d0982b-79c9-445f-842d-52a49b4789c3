'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import { 
  FaStore, 
  FaPlus, 
  FaMinus, 
  FaTrash, 
  FaTruck, 
  FaMapMarkerAlt,
  FaCreditCard,
  FaShoppingBag,
  FaClock,
  FaCheckCircle
} from 'react-icons/fa';

interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  image: string;
  seller_id: string;
  seller_name: string;
  delivery_fee?: number;
  min_order?: number;
}

interface VendorGroup {
  seller_id: string;
  seller_name: string;
  items: CartItem[];
  delivery_fee: number;
  min_order: number;
  delivery_time: string;
  is_available: boolean;
}

interface MultiVendorCartProps {
  onCheckout?: (vendorGroups: VendorGroup[]) => void;
  onContinueShopping?: () => void;
}

export default function MultiVendorCart({ onCheckout, onContinueShopping }: MultiVendorCartProps) {
  // Données de démonstration
  const [cartItems, setCartItems] = useState<CartItem[]>([
    {
      id: '1',
      name: 'Tomates Bio 1kg',
      price: 1500,
      quantity: 2,
      image: '/assets/images/products/vegetables/tomates-cerises-bio.jpg',
      seller_id: '1',
      seller_name: 'Ferme Bio de Thiès',
      delivery_fee: 1500,
      min_order: 5000
    },
    {
      id: '2',
      name: 'Salade Verte Bio',
      price: 800,
      quantity: 1,
      image: '/assets/images/products/vegetables/salade-verte-croquante.jpg',
      seller_id: '1',
      seller_name: 'Ferme Bio de Thiès',
      delivery_fee: 1500,
      min_order: 5000
    },
    {
      id: '3',
      name: 'Bœuf de Zébu Local 1kg',
      price: 8500,
      quantity: 1,
      image: '/assets/images/products/meat/boeuf-steak-halal.jpg',
      seller_id: '2',
      seller_name: 'Boucherie Halal Premium',
      delivery_fee: 2000,
      min_order: 10000
    }
  ]);

  const [deliveryOption, setDeliveryOption] = useState<'delivery' | 'pickup'>('delivery');

  // Grouper les articles par vendeur
  const vendorGroups: VendorGroup[] = cartItems.reduce((groups, item) => {
    const existingGroup = groups.find(g => g.seller_id === item.seller_id);
    
    if (existingGroup) {
      existingGroup.items.push(item);
    } else {
      groups.push({
        seller_id: item.seller_id,
        seller_name: item.seller_name,
        items: [item],
        delivery_fee: item.delivery_fee || 1500,
        min_order: item.min_order || 5000,
        delivery_time: '30-45 min',
        is_available: true
      });
    }
    
    return groups;
  }, [] as VendorGroup[]);

  const updateQuantity = (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeItem(itemId);
      return;
    }

    setCartItems(prev => 
      prev.map(item => 
        item.id === itemId ? { ...item, quantity: newQuantity } : item
      )
    );
  };

  const removeItem = (itemId: string) => {
    setCartItems(prev => prev.filter(item => item.id !== itemId));
  };

  const getVendorSubtotal = (group: VendorGroup) => {
    return group.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  };

  const getVendorTotal = (group: VendorGroup) => {
    const subtotal = getVendorSubtotal(group);
    return subtotal + (deliveryOption === 'delivery' ? group.delivery_fee : 0);
  };

  const getGrandTotal = () => {
    return vendorGroups.reduce((sum, group) => sum + getVendorTotal(group), 0);
  };

  const checkMinimumOrder = (group: VendorGroup) => {
    const subtotal = getVendorSubtotal(group);
    return subtotal >= group.min_order;
  };

  const canProceedToCheckout = () => {
    return vendorGroups.every(group => checkMinimumOrder(group) && group.is_available);
  };

  if (cartItems.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-8 text-center">
        <FaShoppingBag className="mx-auto h-16 w-16 text-gray-400 dark:text-gray-500 mb-4" />
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          Votre panier est vide
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Découvrez nos produits locaux frais et de qualité
        </p>
        <button
          onClick={onContinueShopping}
          className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
        >
          Continuer mes achats
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Options de livraison */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
          Mode de récupération
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button
            onClick={() => setDeliveryOption('delivery')}
            className={`p-4 border-2 rounded-lg transition-colors ${
              deliveryOption === 'delivery'
                ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center space-x-3">
              <FaTruck className={`w-5 h-5 ${
                deliveryOption === 'delivery' ? 'text-primary-600' : 'text-gray-400'
              }`} />
              <div className="text-left">
                <p className="font-medium text-gray-900 dark:text-white">Livraison</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Livré chez vous
                </p>
              </div>
            </div>
          </button>

          <button
            onClick={() => setDeliveryOption('pickup')}
            className={`p-4 border-2 rounded-lg transition-colors ${
              deliveryOption === 'pickup'
                ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center space-x-3">
              <FaMapMarkerAlt className={`w-5 h-5 ${
                deliveryOption === 'pickup' ? 'text-primary-600' : 'text-gray-400'
              }`} />
              <div className="text-left">
                <p className="font-medium text-gray-900 dark:text-white">Retrait</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  À récupérer sur place
                </p>
              </div>
            </div>
          </button>
        </div>
      </div>

      {/* Articles groupés par vendeur */}
      <AnimatePresence>
        {vendorGroups.map((group) => {
          const subtotal = getVendorSubtotal(group);
          const total = getVendorTotal(group);
          const meetsMinimum = checkMinimumOrder(group);
          const remaining = group.min_order - subtotal;

          return (
            <motion.div
              key={group.seller_id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden"
            >
              {/* En-tête du vendeur */}
              <div className="bg-gray-50 dark:bg-gray-700 px-6 py-4 border-b border-gray-200 dark:border-gray-600">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <FaStore className="text-primary-600 dark:text-primary-400" />
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-white">
                        {group.seller_name}
                      </h3>
                      <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                        <span className="flex items-center">
                          <FaClock className="w-3 h-3 mr-1" />
                          {group.delivery_time}
                        </span>
                        {deliveryOption === 'delivery' && (
                          <span className="flex items-center">
                            <FaTruck className="w-3 h-3 mr-1" />
                            {group.delivery_fee.toLocaleString()} FCFA
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-gray-900 dark:text-white">
                      {total.toLocaleString()} FCFA
                    </p>
                    {!meetsMinimum && (
                      <p className="text-sm text-red-600 dark:text-red-400">
                        +{remaining.toLocaleString()} FCFA requis
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Articles du vendeur */}
              <div className="p-6 space-y-4">
                {group.items.map((item) => (
                  <div key={item.id} className="flex items-center space-x-4">
                    <div className="w-16 h-16 relative rounded-lg overflow-hidden">
                      <Image
                        src={item.image}
                        alt={item.name}
                        fill
                        className="object-cover"
                      />
                    </div>
                    
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        {item.name}
                      </h4>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {item.price.toLocaleString()} FCFA
                      </p>
                    </div>

                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => updateQuantity(item.id, item.quantity - 1)}
                        className="p-1 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                      >
                        <FaMinus className="w-3 h-3" />
                      </button>
                      
                      <span className="w-8 text-center font-medium text-gray-900 dark:text-white">
                        {item.quantity}
                      </span>
                      
                      <button
                        onClick={() => updateQuantity(item.id, item.quantity + 1)}
                        className="p-1 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                      >
                        <FaPlus className="w-3 h-3" />
                      </button>
                    </div>

                    <div className="text-right">
                      <p className="font-semibold text-gray-900 dark:text-white">
                        {(item.price * item.quantity).toLocaleString()} FCFA
                      </p>
                    </div>

                    <button
                      onClick={() => removeItem(item.id)}
                      className="p-2 text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                    >
                      <FaTrash className="w-4 h-4" />
                    </button>
                  </div>
                ))}

                {/* Alerte commande minimum */}
                {!meetsMinimum && (
                  <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-900/30 rounded-lg p-4">
                    <div className="flex items-center space-x-2">
                      <FaClock className="text-yellow-600 dark:text-yellow-400" />
                      <p className="text-sm text-yellow-800 dark:text-yellow-300">
                        Commande minimum: {group.min_order.toLocaleString()} FCFA
                        (il vous manque {remaining.toLocaleString()} FCFA)
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </motion.div>
          );
        })}
      </AnimatePresence>

      {/* Résumé et checkout */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
        <div className="space-y-4">
          <div className="flex justify-between items-center text-lg font-semibold">
            <span className="text-gray-900 dark:text-white">Total général</span>
            <span className="text-gray-900 dark:text-white">
              {getGrandTotal().toLocaleString()} FCFA
            </span>
          </div>

          <div className="flex flex-col sm:flex-row gap-4">
            <button
              onClick={onContinueShopping}
              className="flex-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-lg font-medium hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            >
              Continuer mes achats
            </button>
            
            <button
              onClick={() => onCheckout?.(vendorGroups)}
              disabled={!canProceedToCheckout()}
              className="flex-1 bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
            >
              <FaCreditCard className="w-4 h-4" />
              <span>Passer commande</span>
            </button>
          </div>

          {!canProceedToCheckout() && (
            <p className="text-sm text-red-600 dark:text-red-400 text-center">
              Veuillez atteindre les montants minimum requis pour tous les vendeurs
            </p>
          )}
        </div>
      </div>
    </div>
  );
}
