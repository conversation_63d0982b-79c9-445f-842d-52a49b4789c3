'use client';

import React, { useState, useEffect, useRef } from 'react';
import { FaMapMarkerAlt, FaSearch, FaTimes } from 'react-icons/fa';
import { Coordinates } from '@/hooks/useGeolocation';
import { useGoogleMaps } from '@/contexts/GoogleMapsContext';

interface AddressAutocompleteProps {
  onSelectAddress: (address: string, location: Coordinates) => void;
  initialAddress?: string;
  placeholder?: string;
  className?: string;
}

const AddressAutocomplete: React.FC<AddressAutocompleteProps> = ({
  onSelectAddress,
  initialAddress = '',
  placeholder = 'Rechercher une adresse...',
  className = '',
}) => {
  const [address, setAddress] = useState<string>(initialAddress);
  const [predictions, setPredictions] = useState<google.maps.places.AutocompletePrediction[]>([]);
  const [showPredictions, setShowPredictions] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const inputRef = useRef<HTMLInputElement>(null);
  const autocompleteService = useRef<google.maps.places.AutocompleteService | null>(null);
  const placesService = useRef<google.maps.places.PlacesService | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const { isLoaded, loadError } = useGoogleMaps();

  // Initialiser les services Google Maps
  useEffect(() => {
    if (isLoaded && !autocompleteService.current) {
      autocompleteService.current = new google.maps.places.AutocompleteService();

      // Créer un élément div temporaire pour le PlacesService
      const placesDiv = document.createElement('div');
      placesService.current = new google.maps.places.PlacesService(placesDiv);
    }
  }, [isLoaded]);

  // Gérer les clics en dehors du composant pour fermer les prédictions
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setShowPredictions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Rechercher des prédictions d'adresses
  const searchAddress = (input: string) => {
    setAddress(input);

    if (!input || input.length < 3 || !autocompleteService.current) {
      setPredictions([]);
      setShowPredictions(false);
      return;
    }

    setIsLoading(true);

    autocompleteService.current.getPlacePredictions(
      {
        input,
        componentRestrictions: { country: 'sn' }, // Limiter aux adresses du Sénégal
        types: ['address', 'establishment', 'geocode'],
      },
      (results, status) => {
        setIsLoading(false);

        if (status === google.maps.places.PlacesServiceStatus.OK && results) {
          setPredictions(results);
          setShowPredictions(true);
        } else {
          setPredictions([]);
          setShowPredictions(false);
        }
      }
    );
  };

  // Sélectionner une adresse prédite
  const selectPrediction = (prediction: google.maps.places.AutocompletePrediction) => {
    if (!placesService.current) return;

    setIsLoading(true);

    placesService.current.getDetails(
      {
        placeId: prediction.place_id,
        fields: ['formatted_address', 'geometry'],
      },
      (place, status) => {
        setIsLoading(false);

        if (status === google.maps.places.PlacesServiceStatus.OK && place && place.geometry && place.geometry.location) {
          const location = {
            lat: place.geometry.location.lat(),
            lng: place.geometry.location.lng(),
          };

          setAddress(place.formatted_address || prediction.description);
          onSelectAddress(place.formatted_address || prediction.description, location);
          setShowPredictions(false);
        }
      }
    );
  };

  // Effacer l'adresse
  const clearAddress = () => {
    setAddress('');
    setPredictions([]);
    setShowPredictions(false);
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  if (loadError) {
    return (
      <div className={`relative ${className}`}>
        <div className="flex items-center border border-gray-300 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 px-3 py-2">
          <FaMapMarkerAlt className="text-gray-400 dark:text-gray-500 mr-2" />
          <input
            type="text"
            className="flex-grow bg-transparent outline-none text-gray-700 dark:text-gray-300"
            placeholder="Service de géolocalisation indisponible"
            disabled
          />
        </div>
      </div>
    );
  }

  if (!isLoaded) {
    return (
      <div className={`relative ${className}`}>
        <div className="flex items-center border border-gray-300 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 px-3 py-2">
          <div className="animate-spin h-4 w-4 border-t-2 border-b-2 border-primary-600 rounded-full mr-2"></div>
          <input
            type="text"
            className="flex-grow bg-transparent outline-none text-gray-700 dark:text-gray-300"
            placeholder="Chargement..."
            disabled
          />
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`} ref={containerRef}>
      <div className="flex items-center border border-gray-300 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 px-3 py-2">
        {isLoading ? (
          <div className="animate-spin h-4 w-4 border-t-2 border-b-2 border-primary-600 rounded-full mr-2"></div>
        ) : (
          <FaSearch className="text-gray-400 dark:text-gray-500 mr-2" />
        )}

        <input
          ref={inputRef}
          type="text"
          className="flex-grow bg-transparent outline-none text-gray-700 dark:text-gray-300"
          placeholder={placeholder}
          value={address}
          onChange={(e) => searchAddress(e.target.value)}
          onFocus={() => address.length >= 3 && setShowPredictions(true)}
        />

        {address && (
          <button
            type="button"
            onClick={clearAddress}
            className="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300"
          >
            <FaTimes />
          </button>
        )}
      </div>

      {/* Liste des prédictions */}
      {showPredictions && predictions.length > 0 && (
        <div className="absolute z-10 mt-1 w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg border border-gray-200 dark:border-gray-700 max-h-60 overflow-y-auto">
          {predictions.map((prediction) => (
            <div
              key={prediction.place_id}
              className="px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
              onClick={() => selectPrediction(prediction)}
            >
              <div className="flex items-start">
                <FaMapMarkerAlt className="text-gray-400 dark:text-gray-500 mt-1 mr-2 flex-shrink-0" />
                <div>
                  <div className="text-gray-700 dark:text-gray-300">{prediction.structured_formatting.main_text}</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">{prediction.structured_formatting.secondary_text}</div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default AddressAutocomplete;
