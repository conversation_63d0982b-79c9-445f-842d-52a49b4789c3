'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import { 
  FaPaperPlane, 
  FaImage, 
  FaPhone, 
  FaVideo, 
  FaEllipsisV,
  FaStore,
  FaClock,
  FaCheckDouble
} from 'react-icons/fa';

interface Message {
  id: string;
  sender: 'user' | 'seller';
  content: string;
  timestamp: string;
  type: 'text' | 'image' | 'order';
  status?: 'sent' | 'delivered' | 'read';
  orderData?: {
    orderId: string;
    items: string[];
    total: number;
  };
}

interface SellerChatProps {
  sellerId: string;
  sellerName: string;
  sellerAvatar?: string;
  isOnline?: boolean;
  lastSeen?: string;
  onClose?: () => void;
}

export default function SellerChat({ 
  sellerId, 
  sellerName, 
  sellerAvatar, 
  isOnline = true, 
  lastSeen,
  onClose 
}: SellerChatProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      sender: 'seller',
      content: 'Bonjour ! Merci pour votre intérêt pour nos produits bio. Comment puis-je vous aider ?',
      timestamp: '2024-01-15T10:00:00Z',
      type: 'text',
      status: 'read'
    },
    {
      id: '2',
      sender: 'user',
      content: 'Bonjour ! J\'aimerais savoir si vos tomates sont bien biologiques et quand elles ont été récoltées ?',
      timestamp: '2024-01-15T10:02:00Z',
      type: 'text',
      status: 'read'
    },
    {
      id: '3',
      sender: 'seller',
      content: 'Absolument ! Nos tomates sont certifiées bio et ont été récoltées ce matin. Elles sont très fraîches et savoureuses. Nous avons aussi des tomates cerises si cela vous intéresse.',
      timestamp: '2024-01-15T10:05:00Z',
      type: 'text',
      status: 'read'
    },
    {
      id: '4',
      sender: 'user',
      content: 'Parfait ! Je vais prendre 2kg de tomates et 1kg de tomates cerises. Pouvez-vous me faire un prix ?',
      timestamp: '2024-01-15T10:07:00Z',
      type: 'text',
      status: 'read'
    },
    {
      id: '5',
      sender: 'seller',
      content: 'Avec plaisir ! Pour 2kg de tomates classiques (1500 FCFA/kg) et 1kg de tomates cerises (1800 FCFA/kg), cela fait 4800 FCFA. Je peux vous faire 4500 FCFA pour cette commande.',
      timestamp: '2024-01-15T10:10:00Z',
      type: 'text',
      status: 'delivered'
    }
  ]);

  const [newMessage, setNewMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Auto-scroll vers le bas
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Simuler la frappe du vendeur
  useEffect(() => {
    if (messages.length > 0) {
      const timer = setTimeout(() => {
        setIsTyping(true);
        setTimeout(() => setIsTyping(false), 2000);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [messages]);

  const sendMessage = () => {
    if (!newMessage.trim()) return;

    const message: Message = {
      id: Date.now().toString(),
      sender: 'user',
      content: newMessage,
      timestamp: new Date().toISOString(),
      type: 'text',
      status: 'sent'
    };

    setMessages(prev => [...prev, message]);
    setNewMessage('');

    // Simuler la réponse du vendeur
    setTimeout(() => {
      const responses = [
        'Merci pour votre message ! Je vous réponds dans quelques instants.',
        'C\'est noté ! Je prépare votre commande.',
        'Parfait ! Voulez-vous que je vous livre ou préférez-vous venir récupérer ?',
        'Excellente question ! Laissez-moi vérifier cela pour vous.'
      ];
      
      const sellerResponse: Message = {
        id: (Date.now() + 1).toString(),
        sender: 'seller',
        content: responses[Math.floor(Math.random() * responses.length)],
        timestamp: new Date().toISOString(),
        type: 'text',
        status: 'delivered'
      };

      setMessages(prev => [...prev, sellerResponse]);
    }, 1500);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const getMessageStatusIcon = (status?: Message['status']) => {
    switch (status) {
      case 'sent': return <FaCheckDouble className="w-3 h-3 text-gray-400" />;
      case 'delivered': return <FaCheckDouble className="w-3 h-3 text-blue-500" />;
      case 'read': return <FaCheckDouble className="w-3 h-3 text-green-500" />;
      default: return null;
    }
  };

  return (
    <div className="flex flex-col h-full bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
      {/* En-tête du chat */}
      <div className="bg-gradient-to-r from-primary-600 to-primary-800 text-white p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="relative">
              {sellerAvatar ? (
                <Image
                  src={sellerAvatar}
                  alt={sellerName}
                  width={40}
                  height={40}
                  className="rounded-full"
                />
              ) : (
                <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                  <FaStore className="text-white" />
                </div>
              )}
              {isOnline && (
                <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
              )}
            </div>
            <div>
              <h3 className="font-semibold">{sellerName}</h3>
              <p className="text-xs text-primary-100">
                {isOnline ? 'En ligne' : `Vu ${lastSeen}`}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button className="p-2 hover:bg-white/10 rounded-lg transition-colors">
              <FaPhone className="w-4 h-4" />
            </button>
            <button className="p-2 hover:bg-white/10 rounded-lg transition-colors">
              <FaVideo className="w-4 h-4" />
            </button>
            <button className="p-2 hover:bg-white/10 rounded-lg transition-colors">
              <FaEllipsisV className="w-4 h-4" />
            </button>
            {onClose && (
              <button 
                onClick={onClose}
                className="p-2 hover:bg-white/10 rounded-lg transition-colors"
              >
                ✕
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Zone des messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        <AnimatePresence>
          {messages.map((message) => (
            <motion.div
              key={message.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                message.sender === 'user'
                  ? 'bg-primary-600 text-white'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white'
              }`}>
                <p className="text-sm">{message.content}</p>
                <div className={`flex items-center justify-between mt-1 ${
                  message.sender === 'user' ? 'text-primary-100' : 'text-gray-500 dark:text-gray-400'
                }`}>
                  <span className="text-xs">
                    {new Date(message.timestamp).toLocaleTimeString([], { 
                      hour: '2-digit', 
                      minute: '2-digit' 
                    })}
                  </span>
                  {message.sender === 'user' && (
                    <div className="ml-2">
                      {getMessageStatusIcon(message.status)}
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>

        {/* Indicateur de frappe */}
        {isTyping && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="flex justify-start"
          >
            <div className="bg-gray-100 dark:bg-gray-700 px-4 py-2 rounded-lg">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
            </div>
          </motion.div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Zone de saisie */}
      <div className="border-t border-gray-200 dark:border-gray-700 p-4">
        <div className="flex items-center space-x-2">
          <button className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors">
            <FaImage className="w-5 h-5" />
          </button>
          
          <div className="flex-1 relative">
            <input
              ref={inputRef}
              type="text"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Tapez votre message..."
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
          
          <button
            onClick={sendMessage}
            disabled={!newMessage.trim()}
            className="p-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <FaPaperPlane className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
}
